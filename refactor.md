# 🚀 Monorepo Refactoring Plan: InvoiceGo

## 📋 Overview

This document outlines a comprehensive refactoring plan to eliminate duplicates, consolidate types/schemas, and create reusable shared packages for the InvoiceGo monorepo. The goal is to create a scalable, maintainable codebase that can support multiple applications.

## 🎯 Current State Analysis

### ✅ **What's Already Good:**
- Well-structured `/defs` folder with Zod schemas
- Consistent use of TypeScript and Zod validation
- Good separation of concerns in services
- React Query for data fetching
- Zustand for state management

### ⚠️ **Issues Identified:**

1. **Type Duplicates:**
   - `Currency` type exists in both `/defs/common.ts` and `/stores/settingsStore.ts`
   - `Organization` type duplicated in `/stores/organization.ts` and `/defs/organization.ts`
   - Button variants defined separately in mobile and web components
   - Status types scattered across components

2. **Utility Function Duplicates:**
   - Currency formatting in both `settingsStore.ts` and `@repo/shared`
   - Date formatting scattered across multiple files
   - Validation helpers not centralized

3. **Schema Inconsistencies:**
   - Contact schemas have slight variations across entities
   - Address schemas duplicated with minor differences
   - Branding/color schemas scattered

## 📅 Refactoring Phases

---

## 🔥 **Phase 1: Audit & Consolidate Types** (Week 1)

### **Phase 1.1: Type Audit** ✅ **[CURRENT PHASE]**

**Checklist:**
- [ ] **1.1.1** Audit all type definitions in `/apps/mobile/defs/`
- [ ] **1.1.2** Identify duplicate types across stores, components, services
- [ ] **1.1.3** Map type relationships and dependencies
- [ ] **1.1.4** Document type conflicts and merge strategies
- [ ] **1.1.5** Create type consolidation matrix

**Deliverables:**
- [ ] `type-audit.md` - Complete type inventory
- [ ] `duplicate-types.md` - List of all duplicates found
- [ ] `merge-strategy.md` - How to merge conflicting types

### **Phase 1.2: Core Type Consolidation**

**Checklist:**
- [ ] **1.2.1** Merge `Currency` types (defs/common.ts + settingsStore.ts)
- [ ] **1.2.2** Consolidate `Organization` types (defs + stores)
- [ ] **1.2.3** Unify contact schemas across entities
- [ ] **1.2.4** Merge address schemas with optional fields
- [ ] **1.2.5** Consolidate branding/color schemas
- [ ] **1.2.6** Update all imports to use consolidated types

**Example Merge Strategy:**
```typescript
// Before: Multiple Currency types
// defs/common.ts: { code, symbol, name }
// settingsStore.ts: { code, symbol, name, decimalPlaces, symbolPosition }

// After: Unified Currency type
export const CurrencySchema = z.object({
  code: z.string(),
  symbol: z.string(), 
  name: z.string(),
  decimalPlaces: z.number().default(2),
  symbolPosition: z.enum(['before', 'after']).default('before'),
});
```

### **Phase 1.3: Schema Validation**

**Checklist:**
- [ ] **1.3.1** Run TypeScript compilation after each merge
- [ ] **1.3.2** Update all affected components/services
- [ ] **1.3.3** Test Zod schema validation
- [ ] **1.3.4** Verify no runtime errors
- [ ] **1.3.5** Update type exports in `/defs/index.ts`

---

## 🏗️ **Phase 2: Create Shared Packages** (Week 2)

### **Phase 2.1: Package Structure Setup**

**Checklist:**
- [ ] **2.1.1** Create `packages/schemas` package
- [ ] **2.1.2** Create `packages/dtos` package  
- [ ] **2.1.3** Create `packages/utils` package
- [ ] **2.1.4** Create `packages/constants` package
- [ ] **2.1.5** Set up package.json for each package
- [ ] **2.1.6** Configure TypeScript configs
- [ ] **2.1.7** Update workspace dependencies

**Package Structure:**
```
packages/
├── schemas/           # @repo/schemas
│   ├── src/
│   │   ├── entities/  # User, Client, Invoice, etc.
│   │   ├── common/    # BaseEntity, Currency, etc.
│   │   ├── forms/     # Form input schemas
│   │   └── index.ts
│   └── package.json
├── dtos/             # @repo/dtos  
│   ├── src/
│   │   ├── api/      # API request/response DTOs
│   │   ├── forms/    # Form submission DTOs
│   │   └── index.ts
│   └── package.json
├── utils/            # @repo/utils
│   ├── src/
│   │   ├── currency/ # Currency formatting
│   │   ├── date/     # Date utilities
│   │   ├── validation/ # Validation helpers
│   │   └── index.ts
│   └── package.json
└── constants/        # @repo/constants
    ├── src/
    │   ├── currencies.ts
    │   ├── countries.ts
    │   └── index.ts
    └── package.json
```

### **Phase 2.2: Migrate Schemas**

**Checklist:**
- [ ] **2.2.1** Move core entity schemas to `@repo/schemas`
- [ ] **2.2.2** Move common schemas (Currency, BaseEntity, etc.)
- [ ] **2.2.3** Move form input schemas
- [ ] **2.2.4** Update mobile app imports
- [ ] **2.2.5** Test schema validation still works

### **Phase 2.3: Migrate DTOs**

**Checklist:**
- [ ] **2.3.1** Extract API DTOs from services
- [ ] **2.3.2** Create form submission DTOs
- [ ] **2.3.3** Move to `@repo/dtos` package
- [ ] **2.3.4** Update service layer imports
- [ ] **2.3.5** Verify API calls still work

### **Phase 2.4: Migrate Utilities**

**Checklist:**
- [ ] **2.4.1** Move currency utilities to `@repo/utils/currency`
- [ ] **2.4.2** Move date utilities to `@repo/utils/date`
- [ ] **2.4.3** Move validation helpers to `@repo/utils/validation`
- [ ] **2.4.4** Update all imports across mobile app
- [ ] **2.4.5** Test all utility functions

---

## 🔧 **Phase 3: Advanced Consolidation** (Week 3)

### **Phase 3.1: Component Unification**

**Checklist:**
- [ ] **3.1.1** Audit UI component variants (Button, Typography, etc.)
- [ ] **3.1.2** Create unified component prop interfaces
- [ ] **3.1.3** Consolidate styling systems
- [ ] **3.1.4** Update mobile components to use shared interfaces
- [ ] **3.1.5** Test component rendering

### **Phase 3.2: State Management Cleanup**

**Checklist:**
- [ ] **3.2.1** Audit Zustand stores for duplicate logic
- [ ] **3.2.2** Extract common store patterns
- [ ] **3.2.3** Create shared store utilities
- [ ] **3.2.4** Consolidate selector patterns
- [ ] **3.2.5** Test state management

### **Phase 3.3: Service Layer Optimization**

**Checklist:**
- [ ] **3.3.1** Audit service functions for duplicates
- [ ] **3.3.2** Extract common API patterns
- [ ] **3.3.3** Create shared service utilities
- [ ] **3.3.4** Consolidate React Query patterns
- [ ] **3.3.5** Test all API calls

---

## 🚀 **Phase 4: Multi-App Preparation** (Week 4)

### **Phase 4.1: Package Validation**

**Checklist:**
- [ ] **4.1.1** Test all shared packages independently
- [ ] **4.1.2** Verify TypeScript compilation
- [ ] **4.1.3** Test runtime validation
- [ ] **4.1.4** Check for circular dependencies
- [ ] **4.1.5** Performance testing

### **Phase 4.2: Documentation**

**Checklist:**
- [ ] **4.2.1** Document all shared packages
- [ ] **4.2.2** Create usage examples
- [ ] **4.2.3** Write migration guides
- [ ] **4.2.4** Update README files
- [ ] **4.2.5** Create developer onboarding docs

### **Phase 4.3: Future App Setup**

**Checklist:**
- [ ] **4.3.1** Create app template structure
- [ ] **4.3.2** Set up shared package imports
- [ ] **4.3.3** Test creating new app with shared packages
- [ ] **4.3.4** Validate monorepo benefits
- [ ] **4.3.5** Performance benchmarking

---

## 📊 **Success Metrics**

### **Quantitative Goals:**
- [ ] **Reduce duplicate code by 80%**
- [ ] **Consolidate 50+ type definitions into shared packages**
- [ ] **Create 4 reusable packages**
- [ ] **Zero TypeScript compilation errors**
- [ ] **100% test coverage for shared utilities**

### **Qualitative Goals:**
- [ ] **Improved developer experience**
- [ ] **Faster new app creation**
- [ ] **Consistent type safety across apps**
- [ ] **Easier maintenance and updates**
- [ ] **Better code reusability**

---

## 🔍 **Current Status Tracking**

### **Phase 1 Progress: 0% Complete**
- [ ] Type audit not started
- [ ] Duplicate identification pending
- [ ] Merge strategy not defined

### **Next Actions:**
1. **Start Phase 1.1.1** - Begin comprehensive type audit
2. **Create type inventory spreadsheet**
3. **Identify first merge candidates**

---

## 🛠️ **Tools & Commands**

### **Useful Commands:**
```bash
# Find all type definitions
grep -r "export.*type\|export.*interface" apps/mobile/

# Find Zod schemas  
grep -r "z\.object\|z\.enum\|Schema.*=" apps/mobile/

# Check TypeScript compilation
pnpm --filter mobile tsc --noEmit

# Run tests
pnpm --filter mobile test

# Build all packages
pnpm build
```

### **VS Code Extensions:**
- TypeScript Importer
- Auto Import - ES6, TS, JSX, TSX
- Zod Schema Validator

---

---

## 🔍 **Detailed Duplicate Analysis**

### **Critical Duplicates Found:**

#### **1. Currency Type Conflicts**
```typescript
// Location 1: apps/mobile/defs/common.ts
export const CurrencySchema = z.object({
  code: z.string(),
  symbol: z.string(),
  name: z.string(),
});

// Location 2: apps/mobile/stores/settingsStore.ts
export interface Currency {
  code: string;
  symbol: string;
  name: string;
  decimalPlaces: number;
  symbolPosition: 'before' | 'after';
}

// MERGE STRATEGY: Extend defs/common.ts with additional fields
```

#### **2. Organization Type Conflicts**
```typescript
// Location 1: apps/mobile/defs/organization.ts (Full schema)
export const OrganizationSchema = BaseEntitySchema.extend({
  name: z.string().min(1),
  nickname: z.string().min(1),
  // ... 20+ fields
});

// Location 2: apps/mobile/stores/organization.ts (Simplified)
export interface Organization {
  id: string;
  name: string;
  nickname: string;
  logo: string | null;
  description?: string;
  isDefault?: boolean;
}

// MERGE STRATEGY: Use full schema, make store fields optional
```

#### **3. Contact Schema Variations**
```typescript
// Client contact
export const ClientContactSchema = z.object({
  email: z.string().email().optional(),
  phone: z.string().optional(),
  website: z.string().url().optional(),
});

// Organization contact
export const OrganizationContactSchema = z.object({
  email: z.string().email().optional(),
  phone: z.string().optional(),
  website: z.string().url().optional(),
  address: z.string().optional(), // Extra field
});

// MERGE STRATEGY: Create BaseContactSchema with optional address
```

#### **4. Button Component Variants**
```typescript
// Mobile: apps/mobile/components/ui/Button.tsx
type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'danger' | 'icon';

// Web: packages/ui/src/components/ui/button.tsx
variant: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"

// MERGE STRATEGY: Unify variant names and create mapping
```

#### **5. Utility Function Duplicates**
```typescript
// Location 1: apps/mobile/stores/settingsStore.ts
export const formatCurrency = (amount: number, currencyCode: string = 'USD'): string => {
  // Custom implementation with currency lookup
};

// Location 2: packages/shared/src/utils/helpers.ts
export function formatCurrency(amount: number, currency = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount);
}

// MERGE STRATEGY: Use Intl.NumberFormat approach, enhance with currency config
```

---

## 📋 **Phase 1 Detailed Checklist**

### **Phase 1.1: Type Audit (Current)**

#### **1.1.1 Audit all type definitions** ⏳ **[IN PROGRESS]**
- [x] **Core entities** - Client, Invoice, Service, Organization ✅
- [x] **Common types** - Currency, BaseEntity, Status types ✅
- [x] **Store types** - Organization, Currency, Settings ✅
- [ ] **Component types** - Button, Typography, Toast variants
- [ ] **Service types** - API DTOs, Query types
- [ ] **Form types** - Input schemas, validation types

#### **1.1.2 Identify duplicate types** ⏳ **[IN PROGRESS]**
- [x] **Currency conflicts** - Found 2 locations ✅
- [x] **Organization conflicts** - Found 2 locations ✅
- [x] **Contact schema variations** - Found 3 variations ✅
- [ ] **Button variant conflicts** - Mobile vs Web
- [ ] **Status type duplicates** - Invoice, Payment status
- [ ] **Address schema variations** - Client vs Organization

#### **1.1.3 Map type relationships**
- [ ] **Create dependency graph**
- [ ] **Identify breaking changes**
- [ ] **Plan migration order**

#### **1.1.4 Document conflicts**
- [x] **Currency type conflict** - Documented above ✅
- [x] **Organization type conflict** - Documented above ✅
- [ ] **Component variant conflicts**
- [ ] **Utility function conflicts**

#### **1.1.5 Create consolidation matrix**
- [ ] **Priority matrix** (High/Medium/Low impact)
- [ ] **Complexity matrix** (Easy/Medium/Hard to merge)
- [ ] **Risk assessment** (Breaking changes)

---

## 🎯 **Immediate Next Steps (This Week)**

### **Today:**
1. **Complete Phase 1.1.1** - Finish component and service type audit
2. **Start Phase 1.1.2** - Document remaining duplicates
3. **Begin Currency type merge** - Start with highest impact duplicate

### **This Week:**
1. **Complete Phase 1.1** - Full type audit and documentation
2. **Start Phase 1.2** - Begin Currency and Organization type merges
3. **Set up Phase 2 packages** - Create basic package structure

### **Success Criteria for Phase 1:**
- [ ] **Complete type inventory** - All duplicates documented
- [ ] **Merge strategy defined** - Clear plan for each conflict
- [ ] **First 3 types merged** - Currency, Organization, Contact
- [ ] **Zero compilation errors** - All changes validated
- [ ] **Updated imports** - All references point to consolidated types

---

**📝 Note:** This plan will be updated as we progress through each phase. Each completed checklist item should be marked with ✅ and dated.
