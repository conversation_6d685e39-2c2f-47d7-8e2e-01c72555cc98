// Main exports for @repo/services package

// API Client
export * from './api/client';

// Provider interfaces and factory
export * from './providers/interfaces';
export * from './providers/factory';

// Base service classes
export * from './base/service';

// Re-export key types for convenience
export type {
  IApiProvider,
  CreateInvoiceInput,
  CreateClientInput,
  CreateServiceInput,
  CreateOrganizationInput,
  UpdateOrganizationInput,
  TaxOption,
  Payment,
  PaymentSummary,
  CreatePayment,
} from './providers/interfaces';

export type {
  ProviderType,
  ProviderConfig,
} from './providers/factory';

export type {
  ServiceConfig,
  ServiceError,
} from './base/service';
