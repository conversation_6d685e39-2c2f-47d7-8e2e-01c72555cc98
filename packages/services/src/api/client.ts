// API client configuration for shared services

export interface ApiClientConfig {
  baseUrl: string;
  timeout: number;
  headers: Record<string, string>;
  retries: number;
  retryDelay: number;
}

export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

export interface ApiError {
  message: string;
  status: number;
  code?: string;
  details?: any;
}

// Default configuration
export const defaultApiConfig: ApiClientConfig = {
  baseUrl: process.env.API_BASE_URL || 'https://api.example.com',
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  retries: 3,
  retryDelay: 1000, // 1 second
};

// API client interface
export interface IApiClient {
  get<T>(url: string, config?: Partial<ApiClientConfig>): Promise<ApiResponse<T>>;
  post<T>(url: string, data?: any, config?: Partial<ApiClientConfig>): Promise<ApiResponse<T>>;
  put<T>(url: string, data?: any, config?: Partial<ApiClientConfig>): Promise<ApiResponse<T>>;
  patch<T>(url: string, data?: any, config?: Partial<ApiClientConfig>): Promise<ApiResponse<T>>;
  delete<T>(url: string, config?: Partial<ApiClientConfig>): Promise<ApiResponse<T>>;
  setAuthToken(token: string): void;
  clearAuthToken(): void;
}

// Base API client implementation
export class BaseApiClient implements IApiClient {
  private config: ApiClientConfig;
  private authToken?: string;

  constructor(config: Partial<ApiClientConfig> = {}) {
    this.config = { ...defaultApiConfig, ...config };
  }

  setAuthToken(token: string): void {
    this.authToken = token;
  }

  clearAuthToken(): void {
    this.authToken = undefined;
  }

  private getHeaders(): Record<string, string> {
    const headers = { ...this.config.headers };
    if (this.authToken) {
      headers.Authorization = `Bearer ${this.authToken}`;
    }
    return headers;
  }

  private async makeRequest<T>(
    method: string,
    url: string,
    data?: any,
    config?: Partial<ApiClientConfig>
  ): Promise<ApiResponse<T>> {
    const requestConfig = { ...this.config, ...config };
    const fullUrl = url.startsWith('http') ? url : `${requestConfig.baseUrl}${url}`;
    
    const requestOptions: RequestInit = {
      method,
      headers: this.getHeaders(),
      signal: AbortSignal.timeout(requestConfig.timeout),
    };

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      requestOptions.body = JSON.stringify(data);
    }

    let lastError: ApiError;
    
    for (let attempt = 0; attempt <= requestConfig.retries; attempt++) {
      try {
        const response = await fetch(fullUrl, requestOptions);
        
        let responseData: T;
        const contentType = response.headers.get('content-type');
        
        if (contentType && contentType.includes('application/json')) {
          responseData = await response.json();
        } else {
          responseData = await response.text() as unknown as T;
        }

        if (!response.ok) {
          throw {
            message: `HTTP ${response.status}: ${response.statusText}`,
            status: response.status,
            details: responseData,
          } as ApiError;
        }

        return {
          data: responseData,
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
        };
      } catch (error) {
        lastError = error as ApiError;
        
        if (attempt < requestConfig.retries) {
          await new Promise(resolve => setTimeout(resolve, requestConfig.retryDelay));
        }
      }
    }

    throw lastError!;
  }

  async get<T>(url: string, config?: Partial<ApiClientConfig>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>('GET', url, undefined, config);
  }

  async post<T>(url: string, data?: any, config?: Partial<ApiClientConfig>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>('POST', url, data, config);
  }

  async put<T>(url: string, data?: any, config?: Partial<ApiClientConfig>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>('PUT', url, data, config);
  }

  async patch<T>(url: string, data?: any, config?: Partial<ApiClientConfig>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>('PATCH', url, data, config);
  }

  async delete<T>(url: string, config?: Partial<ApiClientConfig>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>('DELETE', url, undefined, config);
  }
}

// Create default API client instance
export const createApiClient = (config?: Partial<ApiClientConfig>): IApiClient => {
  return new BaseApiClient(config);
};

// Default API client instance
export const apiClient = createApiClient();
