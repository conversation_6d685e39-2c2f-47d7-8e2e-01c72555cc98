{"name": "@repo/services", "version": "0.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./api": "./src/api/index.ts", "./providers": "./src/providers/index.ts", "./types": "./src/types/index.ts", "./base": "./src/base/index.ts"}, "scripts": {"lint": "eslint .", "type-check": "tsc --noEmit"}, "dependencies": {"@repo/schemas": "workspace:*", "@repo/dtos": "workspace:*", "@repo/utils": "workspace:*"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^20.0.0", "eslint": "catalog:", "typescript": "catalog:"}}