import { Organization } from '@repo/schemas';

// Re-export Organization type for convenience
export type { Organization } from '@repo/schemas';

// Dashboard data interfaces
export interface Activity {
  id: string;
  title: string;
  date: string;
  amount: string;
  type: string;
  vendor: string;
  avatar: string | null;
  company?: string;
}

export interface OrganizationDashboardData {
  invoiceStats: {
    pending: { count: number; amount: string };
    paid: { count: number; amount: string };
    overdue: { count: number; amount: string };
  };
  summary: {
    revenue: { value: string; percentChange: number };
    totalInvoices: { value: string; percentChange: number };
  };
  recentActivity: Activity[];
}

export interface OrganizationState {
  activeOrganization: Organization | null;
  organizations: Organization[];
  dashboardData: { [key: string]: OrganizationDashboardData };
  isLoading: boolean;
}

export interface OrganizationActions {
  setActiveOrganization: (organization: Organization | null) => void;
  setOrganizations: (organizations: Organization[]) => void;
  addOrganization: (organization: Organization) => void;
  updateOrganization: (id: string, updates: Partial<Organization>) => void;
  removeOrganization: (id: string) => void;
  setLoading: (loading: boolean) => void;
  
  // Legacy methods for compatibility
  getOrganizations: () => Organization[];
  getOrganizationById: (organizationId: string) => Organization | undefined;
  getDashboardData: (organizationId: string) => OrganizationDashboardData;
  deleteOrganization: (organizationId: string) => void;
}

export type OrganizationStore = OrganizationState & OrganizationActions;
