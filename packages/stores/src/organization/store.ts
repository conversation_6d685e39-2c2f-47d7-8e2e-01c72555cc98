import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  Organization,
  OrganizationStore,
  OrganizationState,
  OrganizationActions
} from './types';
import { createDefaultDashboardData } from './utils';

// Cache invalidation callback type
export type CacheInvalidationCallback = () => void;

// Global cache invalidation callback - can be set by apps
let globalCacheInvalidationCallback: CacheInvalidationCallback | undefined;

export const setCacheInvalidationCallback = (callback: CacheInvalidationCallback) => {
  globalCacheInvalidationCallback = callback;
};

// Create the actual organization store instance
export const useOrganizationStore = create<OrganizationStore>()(
  persist(
    (set, get) => ({
      // Initial state
      activeOrganization: null,
      organizations: [],
      dashboardData: {},
      isLoading: false,

        // Actions
        setActiveOrganization: (organization) => {
          set({ activeOrganization: organization });

          // Clear cache when switching organizations
          if (globalCacheInvalidationCallback) {
            try {
              globalCacheInvalidationCallback();
            } catch (error) {
              console.warn('Cache invalidation failed:', error);
            }
          }
        },
        
        setOrganizations: (organizations) => {
          set((state) => {
            // Auto-select first organization if none selected
            const activeOrganization = state.activeOrganization || (organizations.length > 0 ? organizations[0] : null);
            return {
              organizations,
              activeOrganization,
            };
          });
        },
        
        addOrganization: (organization) => {
          set((state) => ({
            organizations: [...state.organizations, organization],
            activeOrganization: state.activeOrganization || organization,
            dashboardData: {
              ...state.dashboardData,
              [organization.id]: createDefaultDashboardData(),
            },
          }));
        },
        
        updateOrganization: (id, updates) => {
          set((state) => ({
            organizations: state.organizations.map(org =>
              org.id === id ? { ...org, ...updates } : org
            ),
            activeOrganization: state.activeOrganization?.id === id
              ? { ...state.activeOrganization, ...updates }
              : state.activeOrganization
          }));
        },
        
        removeOrganization: (id) => {
          set((state) => {
            const newOrganizations = state.organizations.filter(org => org.id !== id);
            const newActiveOrganization = state.activeOrganization?.id === id
              ? newOrganizations[0] || null
              : state.activeOrganization;
            
            const { [id]: deletedData, ...remainingDashboardData } = state.dashboardData;
            
            return {
              organizations: newOrganizations,
              activeOrganization: newActiveOrganization,
              dashboardData: remainingDashboardData,
            };
          });
        },
        
        setLoading: (isLoading) => set({ isLoading }),
        
        // Legacy methods for backward compatibility
        getOrganizations: () => {
          return get().organizations;
        },
        
        getOrganizationById: (organizationId: string) => {
          return get().organizations.find(org => org.id === organizationId);
        },
        
        getDashboardData: (organizationId: string) => {
          const dashboardData = get().dashboardData[organizationId];
          return dashboardData || createDefaultDashboardData();
        },
        
        deleteOrganization: (organizationId: string) => {
          get().removeOrganization(organizationId);
        },
    }),
    {
      name: "organization",
      partialize: (state) => ({
        activeOrganization: state.activeOrganization,
        organizations: state.organizations,
        dashboardData: state.dashboardData,
      })
    }
  )
);

// Helper function to initialize store with data (for apps that need initial data)
export const initializeOrganizationStore = (initialData: {
  organizations?: Organization[];
  activeOrganization?: Organization | null;
  dashboardData?: { [key: string]: any };
}) => {
  const { setOrganizations, setActiveOrganization } = useOrganizationStore.getState();

  if (initialData.organizations) {
    setOrganizations(initialData.organizations);
  }

  if (initialData.activeOrganization) {
    setActiveOrganization(initialData.activeOrganization);
  }

  if (initialData.dashboardData) {
    useOrganizationStore.setState((state) => ({
      dashboardData: { ...state.dashboardData, ...initialData.dashboardData }
    }));
  }
};
