# @repo/stores

This package contains all shared Zustand stores for business logic across the InvoiceGo monorepo. It provides centralized state management that can be used directly by any application.

## 📦 Installation

```bash
pnpm add @repo/stores
```

## 🏗️ Architecture

The stores package provides actual store instances (not factories) that can be imported and used directly:

```
src/
├── organization.ts   # Organization/company management
├── user.ts          # User authentication & profile  
├── invoice.ts       # Invoice business logic
├── client.ts        # Client management
├── service.ts       # Service management
└── index.ts         # Export all stores
```

## 🔧 Usage

### Organization Store

```typescript
import { useOrganizationStore } from '@repo/stores';

function MyComponent() {
  const { 
    organizations, 
    activeOrganization, 
    setActiveOrganization,
    createOrganization 
  } = useOrganizationStore();

  const handleSwitchOrg = (orgId: string) => {
    setActiveOrganization(orgId);
    // All data automatically refreshes for new organization
  };

  return (
    <div>
      <h1>{activeOrganization?.name}</h1>
      {/* ... */}
    </div>
  );
}
```

### User Store

```typescript
import { useUserStore } from '@repo/stores';

function AuthComponent() {
  const { 
    user, 
    isAuthenticated, 
    login, 
    logout,
    updateProfile 
  } = useUserStore();

  if (!isAuthenticated) {
    return <LoginForm onLogin={login} />;
  }

  return <Dashboard user={user} onLogout={logout} />;
}
```

### Invoice Store

```typescript
import { useInvoiceStore } from '@repo/stores';

function InvoicesPage() {
  const { 
    invoices, 
    createInvoice, 
    updateInvoice,
    deleteInvoice,
    calculateTotal 
  } = useInvoiceStore();

  const total = calculateTotal(invoice);
  
  return (
    <div>
      {invoices.map(invoice => (
        <InvoiceCard 
          key={invoice.id} 
          invoice={invoice}
          onUpdate={updateInvoice}
          onDelete={deleteInvoice}
        />
      ))}
    </div>
  );
}
```

## 🏪 Available Stores

### OrganizationStore
- **State**: `organizations`, `activeOrganization`, `loading`
- **Actions**: `setActiveOrganization`, `createOrganization`, `updateOrganization`
- **Selectors**: `getActiveOrgId`, `isMultiTenant`

### UserStore  
- **State**: `user`, `isAuthenticated`, `loading`
- **Actions**: `login`, `logout`, `updateProfile`, `uploadAvatar`
- **Selectors**: `getUserId`, `getUserEmail`, `hasPermission`

### InvoiceStore
- **State**: `invoices`, `currentInvoice`, `loading`
- **Actions**: `createInvoice`, `updateInvoice`, `deleteInvoice`, `duplicateInvoice`
- **Computed**: `calculateTotal`, `calculateTax`, `getInvoicesByStatus`

### ClientStore
- **State**: `clients`, `loading`
- **Actions**: `createClient`, `updateClient`, `deleteClient`
- **Selectors**: `getClientById`, `getActiveClients`

### ServiceStore
- **State**: `services`, `loading`
- **Actions**: `createService`, `updateService`, `deleteService`
- **Selectors**: `getServiceById`, `getServicesByCategory`

## 🔄 Store Integration

### Automatic Organization Context

All stores automatically filter data by the active organization:

```typescript
import { useOrganizationStore, useInvoiceStore } from '@repo/stores';

function App() {
  const { setActiveOrganization } = useOrganizationStore();
  const { invoices } = useInvoiceStore(); // Automatically filtered by active org

  const switchOrganization = (orgId: string) => {
    setActiveOrganization(orgId);
    // All stores automatically update to show data for new organization
  };
}
```

### Cross-Store Dependencies

Stores automatically handle relationships:

```typescript
import { useClientStore, useInvoiceStore } from '@repo/stores';

function CreateInvoiceForm() {
  const { clients } = useClientStore();
  const { createInvoice } = useInvoiceStore();

  const handleSubmit = (data: CreateInvoiceInput) => {
    // Client data is automatically available and validated
    createInvoice({
      ...data,
      clientId: selectedClient.id,
    });
  };
}
```

## 🎯 Benefits

1. **True Shared State** - Same store instances across all applications
2. **Automatic Sync** - Organization switching updates all stores
3. **Type Safety** - Full TypeScript support with schemas
4. **Performance** - Zustand's optimized subscriptions
5. **Simplicity** - No setup required, just import and use

## 🏗️ Multi-App Architecture

### Mobile App Usage

```typescript
// apps/mobile/app/_layout.tsx
import { useOrganizationStore } from '@repo/stores';

export default function Layout() {
  // Store works out of the box, no additional setup
  return <Slot />;
}
```

### Web App Usage (Future)

```typescript
// apps/web/pages/_app.tsx
import { useOrganizationStore } from '@repo/stores';

export default function App({ Component, pageProps }) {
  // Same stores, same business logic, different UI
  return <Component {...pageProps} />;
}
```

## 🔧 Store Patterns

### Optimistic Updates

```typescript
const { updateInvoice } = useInvoiceStore();

const handleSave = async (updates: Partial<Invoice>) => {
  // Optimistic update - UI updates immediately
  updateInvoice(invoiceId, updates);
  
  try {
    // Real API call happens in background
    await api.updateInvoice(invoiceId, updates);
  } catch (error) {
    // Revert on error
    revertInvoiceUpdate(invoiceId);
  }
};
```

### Computed Values

```typescript
const { invoices, calculateTotal } = useInvoiceStore();

// Computed values are automatically memoized
const totalRevenue = useMemo(() => 
  invoices.reduce((sum, invoice) => sum + calculateTotal(invoice), 0),
  [invoices, calculateTotal]
);
```

## 📚 Related Packages

- `@repo/schemas` - Type definitions and validation
- `@repo/queries` - React Query hooks for API calls
- `@repo/providers` - Context providers for store initialization 