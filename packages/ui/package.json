{"name": "@repo/ui", "version": "0.0.0", "private": true, "scripts": {"ui": "pnpm dlx shadcn@latest", "lint": "eslint ."}, "peerDependencies": {"react": "^18 || ^19"}, "peerDependenciesMeta": {"react-native": {"optional": true}}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "catalog:", "@types/react": "catalog:react18", "autoprefixer": "^10", "postcss": "^8", "postcss-load-config": "^6", "tailwindcss": "catalog:", "typescript": "catalog:"}, "dependencies": {"@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "catalog:", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7"}, "exports": {"./globals.css": "./src/globals.css", "./postcss.config": "./postcss.config.mjs", "./tailwind.config": "./tailwind.config.ts", "./lib/*": "./src/lib/*.ts", "./hooks/*": ["./src/hooks/*.ts", "./src/hooks/*.tsx"], "./components/*": "./src/components/*.tsx"}}