{"name": "@repo/ui-native", "version": "0.0.0", "private": true, "main": "src/index.ts", "scripts": {"lint": "eslint ."}, "peerDependencies": {"react": "^18 || ^19", "react-native": "*"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "catalog:", "@types/react": "catalog:react18", "eslint": "catalog:", "typescript": "catalog:"}, "dependencies": {"class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "nativewind": "^2.0.11", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.14.0", "tailwind-merge": "^2.3.0"}, "exports": {"./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx"}}