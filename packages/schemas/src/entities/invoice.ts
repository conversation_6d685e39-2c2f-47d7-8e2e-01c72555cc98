import { z } from 'zod';
import {
    BaseEntitySchema,
    DiscountTypeSchema,
    EntityIdSchema,
    InvoiceStatusSchema,
    UnitTypeSchema
} from '../common';

// Invoice line item
export const InvoiceLineItemSchema = z.object({
  id: EntityIdSchema,
  description: z.string().min(1, 'Description is required'),
  itemDescription: z.string().optional(),
  quantity: z.number().min(0, 'Quantity must be positive'),
  unitPrice: z.number().min(0, 'Unit price must be positive'),
  unit: UnitTypeSchema.default('fixed'),
  discount: z.number().min(0).optional(),
  discountType: DiscountTypeSchema.default('percentage'),
  total: z.number(),
  // Tax information for this item
  taxable: z.boolean().default(true),
  taxRate: z.number().min(0).max(100).optional(),
  // Metadata
  serviceId: EntityIdSchema.optional(), // If created from a service
  notes: z.string().optional(),
});
export type InvoiceLineItem = z.infer<typeof InvoiceLineItemSchema>;

// Invoice payment information
export const InvoicePaymentSchema = z.object({
  method: z.string().optional(), // 'bank_transfer', 'credit_card', 'cash', etc.
  terms: z.string().optional(),
  instructions: z.string().optional(),
  dueDate: z.date(),
  lateFee: z.number().min(0).optional(),
  lateFeeType: DiscountTypeSchema.optional(),
});
export type InvoicePayment = z.infer<typeof InvoicePaymentSchema>;

// Invoice totals and calculations
export const InvoiceTotalsSchema = z.object({
  subtotal: z.number(),
  discountTotal: z.number().default(0),
  taxTotal: z.number().default(0),
  total: z.number(),
  amountPaid: z.number().default(0),
  amountDue: z.number(),
});
export type InvoiceTotals = z.infer<typeof InvoiceTotalsSchema>;

// Invoice Comment schema
export const InvoiceCommentSchema = BaseEntitySchema.extend({
  invoiceId: EntityIdSchema,
  organizationId: EntityIdSchema,
  text: z.string().min(1, 'Comment text is required'),
  type: z.enum(['internal', 'client']).default('internal'),
  author: z.string().optional(), // User who created the comment
});
export type InvoiceComment = z.infer<typeof InvoiceCommentSchema>;

// Invoice Attachment schema
export const InvoiceAttachmentSchema = z.object({
  id: EntityIdSchema,
  name: z.string().min(1, 'File name is required'),
  size: z.number().min(0, 'File size must be positive'),
  type: z.string(), // MIME type
  uri: z.string().min(1, 'File URI is required'),
  uploadedAt: z.date(),
});
export type InvoiceAttachment = z.infer<typeof InvoiceAttachmentSchema>;

// Tax information schema (simplified for now)
export const InvoiceTaxInfoSchema = z.object({
  taxRate: z.number().min(0).max(100).default(0),
  taxAmount: z.number().default(0),
  taxExempt: z.boolean().default(false),
});
export type InvoiceTaxInfo = z.infer<typeof InvoiceTaxInfoSchema>;

// Main invoice entity
export const InvoiceSchema = BaseEntitySchema.extend({
  // Basic information
  invoiceNumber: z.string().min(1, 'Invoice number is required'),
  status: InvoiceStatusSchema,

  // Dates
  issueDate: z.date(),
  dueDate: z.date(),

  // Relationships
  organizationId: EntityIdSchema,
  clientId: EntityIdSchema,
  clientName: z.string(), // Denormalized for easy display

  // Content
  lineItems: z.array(InvoiceLineItemSchema),

  // Tax configuration
  taxInfo: InvoiceTaxInfoSchema.optional(),

  // Comments and notes
  comments: z.array(InvoiceCommentSchema).default([]),

  // Attachments (max 3)
  attachments: z.array(InvoiceAttachmentSchema).max(3, 'Maximum 3 attachments allowed').default([]),

  // Payment and totals
  payment: InvoicePaymentSchema,
  totals: InvoiceTotalsSchema,

  // Additional information
  notes: z.string().optional(),
  terms: z.string().optional(),
  signature: z.string().optional(), // Base64 encoded signature

  // Metadata
  sentAt: z.date().optional(),
  paidAt: z.date().optional(),
  viewedAt: z.date().optional(),

  // Legacy fields for backward compatibility
  amount: z.string().optional(), // Formatted total amount
  date: z.date().optional(), // Alias for issueDate
  company: z.string().optional(), // Client company name
});
export type Invoice = z.infer<typeof InvoiceSchema>;

// Invoice Activity types and schema
export const InvoiceActivityTypeSchema = z.enum([
  'created',
  'sent',
  'viewed',
  'paid',
  'partial_payment',
  'overdue',
  'reminder_sent',
  'status_changed',
  'edited',
  'cancelled',
  'refunded',
  'noted',
  'downloaded',
  'archived',
  'unarchived',
]);
export type InvoiceActivityType = z.infer<typeof InvoiceActivityTypeSchema>;

export const InvoiceActivitySchema = BaseEntitySchema.extend({
  invoiceId: EntityIdSchema,
  organizationId: EntityIdSchema,
  type: InvoiceActivityTypeSchema,
  description: z.string(),
  metadata: z.record(z.any()).optional(), // Flexible metadata for different activity types
  performedBy: z.string().optional(), // User who performed the action, if applicable
});
export type InvoiceActivity = z.infer<typeof InvoiceActivitySchema>;
