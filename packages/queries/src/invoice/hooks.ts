// Invoice React Query hooks - extracted from mobile app

import { useQuery } from '@tanstack/react-query';
import { Invoice, InvoiceActivity, UpdateInvoiceDto } from '@repo/schemas';
import { debounce } from 'lodash';

// Query key factories
export const INVOICES_KEY = (organizationId: string) => ['invoices', organizationId];
export const INVOICE_KEY = (invoiceId: string, organizationId: string) => ['invoice', invoiceId, organizationId];
export const INVOICE_ACTIVITIES_KEY = (organizationId: string, invoiceId: string) => ['invoiceActivities', organizationId, invoiceId];

// Service functions that use the provider
export const createInvoiceService = (provider: any, queryClient: any) => {
  const fetchInvoices = async (organizationId: string): Promise<Invoice[]> => {
    const response = await provider.getInvoices(organizationId);
    return response;
  };

  const fetchInvoice = async (organizationId: string, invoiceId: string): Promise<Invoice> => {
    const response = await provider.getInvoice(organizationId, invoiceId);
    return response;
  };

  const updateInvoice = async (data: UpdateInvoiceDto): Promise<Invoice> => {
    const response = await provider.updateInvoice(data.organizationId, data.id, data);
    
    // Immediate cache updates
    queryClient.setQueryData(INVOICE_KEY(response.id, data.organizationId), response);
    queryClient.setQueryData(INVOICES_KEY(data.organizationId), (cache: Invoice[]) => {
      if (!cache) return [response];
      return cache.map((invoice) => invoice.id === response.id ? response : invoice);
    });
    
    return response;
  };

  const fetchInvoiceActivities = async (organizationId: string, invoiceId: string): Promise<InvoiceActivity[]> => {
    const response = await provider.getInvoiceActivities(organizationId, invoiceId);
    return response;
  };

  // Debounced version for real-time editing
  const debouncedUpdateInvoice = debounce(updateInvoice, 1000);

  return {
    fetchInvoices,
    fetchInvoice,
    updateInvoice,
    fetchInvoiceActivities,
    debouncedUpdateInvoice,
  };
};

// Hook factory that takes dependencies
export const createInvoiceHooks = (
  provider: any,
  queryClient: any,
  useActiveOrganizationId: () => string | undefined
) => {
  const service = createInvoiceService(provider, queryClient);

  const useInvoices = () => {
    const organizationId = useActiveOrganizationId();
    
    const {
      error,
      isPending: loading,
      data: invoices,
    } = useQuery({
      queryKey: INVOICES_KEY(organizationId!),
      queryFn: () => service.fetchInvoices(organizationId!),
      enabled: !!organizationId,
    });

    return { invoices, loading, error };
  };

  const useInvoice = (invoiceId: string) => {
    const organizationId = useActiveOrganizationId();
    
    const {
      error,
      isPending: loading,
      data: invoice,
    } = useQuery({
      queryKey: INVOICE_KEY(invoiceId, organizationId!),
      queryFn: () => service.fetchInvoice(organizationId!, invoiceId),
      enabled: !!organizationId && !!invoiceId,
    });

    return { invoice, loading, error };
  };

  const useInvoiceActivities = (invoiceId: string) => {
    const organizationId = useActiveOrganizationId();
    
    const {
      error,
      isPending: loading,
      data: activities,
    } = useQuery({
      queryKey: INVOICE_ACTIVITIES_KEY(organizationId!, invoiceId),
      queryFn: () => service.fetchInvoiceActivities(organizationId!, invoiceId),
      enabled: !!organizationId && !!invoiceId,
    });

    return { activities, loading, error };
  };

  return {
    useInvoices,
    useInvoice,
    useInvoiceActivities,
    // Export service functions for direct use
    ...service,
  };
};

// Export query keys for external use
export const invoiceKeys = {
  INVOICES_KEY,
  INVOICE_KEY,
  INVOICE_ACTIVITIES_KEY,
};
