# @repo/queries

This package contains all shared React Query hooks and patterns for data fetching across the InvoiceGo monorepo. It provides consistent data fetching, caching, and synchronization that integrates seamlessly with shared stores.

## 📦 Installation

```bash
pnpm add @repo/queries
```

## 🏗️ Architecture

The queries package provides React Query patterns for all business entities:

```
src/
├── hooks/       # Custom React Query hooks
│   ├── client/  # Client-related queries
│   ├── invoice/ # Invoice-related queries  
│   ├── service/ # Service-related queries
│   └── user/    # User-related queries
├── keys/        # Query key factories
├── config/      # Query client configuration
└── index.ts     # Export all hooks
```

## 🔧 Usage

### Basic Query Hooks

```typescript
import { useClients, useInvoices, useServices } from '@repo/queries';

function DashboardPage() {
  const { data: clients, isLoading: clientsLoading } = useClients();
  const { data: invoices, isLoading: invoicesLoading } = useInvoices();
  const { data: services, isLoading: servicesLoading } = useServices();

  if (clientsLoading || invoicesLoading || servicesLoading) {
    return <LoadingSpinner />;
  }

  return (
    <div>
      <ClientList clients={clients} />
      <InvoiceList invoices={invoices} />
      <ServiceList services={services} />
    </div>
  );
}
```

### Mutation Hooks

```typescript
import { useCreateClient, useUpdateClient, useDeleteClient } from '@repo/queries';

function ClientForm({ client }) {
  const createClient = useCreateClient();
  const updateClient = useUpdateClient();
  const deleteClient = useDeleteClient();

  const handleSave = (data: ClientFormData) => {
    if (client) {
      updateClient.mutate({ id: client.id, ...data });
    } else {
      createClient.mutate(data);
    }
  };

  const handleDelete = () => {
    deleteClient.mutate(client.id);
  };

  return (
    <form onSubmit={handleSave}>
      {/* Form fields */}
      <button 
        type="submit" 
        disabled={createClient.isPending || updateClient.isPending}
      >
        {createClient.isPending || updateClient.isPending ? 'Saving...' : 'Save'}
      </button>
    </form>
  );
}
```

### Organization-Aware Queries

All queries automatically filter by the active organization:

```typescript
import { useOrganizationStore } from '@repo/stores';
import { useClients } from '@repo/queries';

function ClientsList() {
  const { activeOrganization } = useOrganizationStore();
  const { data: clients } = useClients(); // Automatically filtered by activeOrganization

  // When organization changes, all queries automatically refetch
  return (
    <div>
      <h1>Clients for {activeOrganization?.name}</h1>
      {clients?.map(client => <ClientCard key={client.id} client={client} />)}
    </div>
  );
}
```

## 🔧 Available Hooks

### Client Hooks

```typescript
import {
  useClients,           // Get all clients
  useClient,            // Get single client by ID
  useCreateClient,      // Create new client
  useUpdateClient,      // Update existing client
  useDeleteClient       // Delete client
} from '@repo/queries';

// Usage
const { data: clients } = useClients();
const { data: client } = useClient(clientId);
const createClient = useCreateClient();
```

### Invoice Hooks

```typescript
import {
  useInvoices,          // Get all invoices
  useInvoice,           // Get single invoice by ID
  useCreateInvoice,     // Create new invoice
  useUpdateInvoice,     // Update existing invoice
  useDeleteInvoice,     // Delete invoice
  useDuplicateInvoice   // Duplicate existing invoice
} from '@repo/queries';

// Advanced invoice queries
const { data: invoices } = useInvoices({
  status: 'pending',    // Filter by status
  clientId: 'client-1', // Filter by client
  limit: 10            // Pagination
});
```

### Service Hooks

```typescript
import {
  useServices,          // Get all services
  useService,           // Get single service by ID
  useCreateService,     // Create new service
  useUpdateService,     // Update existing service
  useDeleteService      // Delete service
} from '@repo/queries';
```

### User & Organization Hooks

```typescript
import {
  useProfile,           // Get current user profile
  useUpdateProfile,     // Update user profile
  useOrganizations,     // Get user's organizations
  useCreateOrganization // Create new organization
} from '@repo/queries';
```

## 🔄 Cache Integration

### Automatic Cache Updates

Mutations automatically update the cache:

```typescript
const createClient = useCreateClient();

// When this succeeds, the clients list automatically updates
createClient.mutate({
  name: "New Client",
  email: "<EMAIL>"
});
```

### Optimistic Updates

```typescript
import { useUpdateInvoice } from '@repo/queries';

const updateInvoice = useUpdateInvoice({
  // Optimistic update - UI updates immediately
  onMutate: async (variables) => {
    await queryClient.cancelQueries(['invoices']);
    
    const previousInvoices = queryClient.getQueryData(['invoices']);
    
    queryClient.setQueryData(['invoices'], old => 
      old?.map(invoice => 
        invoice.id === variables.id 
          ? { ...invoice, ...variables.updates }
          : invoice
      )
    );

    return { previousInvoices };
  },
  
  // Revert on error
  onError: (err, variables, context) => {
    queryClient.setQueryData(['invoices'], context.previousInvoices);
  }
});
```

### Cache Invalidation

```typescript
import { queryKeys } from '@repo/queries';

// Invalidate specific queries
queryClient.invalidateQueries(queryKeys.clients.all);
queryClient.invalidateQueries(queryKeys.invoices.byStatus('pending'));

// Organization switching automatically invalidates all caches
const { setActiveOrganization } = useOrganizationStore();
setActiveOrganization(newOrgId); // All queries refetch automatically
```

## 🔧 Query Key Factories

Consistent query keys for cache management:

```typescript
import { queryKeys } from '@repo/queries';

// Query key factories ensure consistency
const keys = {
  clients: queryKeys.clients.all,                    // ['clients']
  client: queryKeys.clients.detail(clientId),        // ['clients', clientId]
  invoices: queryKeys.invoices.all,                  // ['invoices']
  invoicesByStatus: queryKeys.invoices.byStatus('pending'), // ['invoices', { status: 'pending' }]
};
```

## 🎯 Benefits

1. **Automatic Organization Context** - All queries filter by active organization
2. **Cache Consistency** - Mutations automatically update related queries
3. **Optimistic Updates** - UI updates immediately, reverts on error
4. **Background Sync** - Data stays fresh with automatic refetching
5. **Error Handling** - Consistent error states across applications

## 🏗️ Multi-App Architecture

### Shared Configuration

```typescript
// Both mobile and web apps use the same query configuration
import { createQueryClient } from '@repo/queries/config';

const queryClient = createQueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000,   // 10 minutes
    },
  },
});
```

### Platform-Specific Providers

```typescript
// Mobile app
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { createQueryClient } from '@repo/queries/config';

const queryClient = createQueryClient();

export default function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <YourApp />
    </QueryClientProvider>
  );
}
```

## 🔧 Advanced Patterns

### Dependent Queries

```typescript
import { useClient, useInvoices } from '@repo/queries';

function ClientInvoices({ clientId }) {
  const { data: client } = useClient(clientId);
  
  // This query waits for client to load
  const { data: invoices } = useInvoices(
    { clientId }, 
    { enabled: !!client }
  );

  return (
    <div>
      <h1>Invoices for {client?.name}</h1>
      <InvoiceList invoices={invoices} />
    </div>
  );
}
```

### Infinite Queries

```typescript
import { useInfiniteInvoices } from '@repo/queries';

function InvoicesList() {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteInvoices({
    limit: 20
  });

  return (
    <div>
      {data?.pages.map(page => 
        page.invoices.map(invoice => 
          <InvoiceCard key={invoice.id} invoice={invoice} />
        )
      )}
      
      {hasNextPage && (
        <button onClick={fetchNextPage} disabled={isFetchingNextPage}>
          {isFetchingNextPage ? 'Loading more...' : 'Load More'}
        </button>
      )}
    </div>
  );
}
```

## 📚 Related Packages

- `@repo/stores` - Business logic stores that integrate with queries
- `@repo/schemas` - Type definitions for query data
- `@repo/providers` - Context providers for query client setup 