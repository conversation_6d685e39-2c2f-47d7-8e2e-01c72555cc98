{"name": "@repo/queries", "version": "0.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./hooks": "./src/hooks/index.ts", "./keys": "./src/keys/index.ts", "./config": "./src/config/index.ts", "./mutations": "./src/mutations/index.ts"}, "scripts": {"lint": "eslint .", "type-check": "tsc --noEmit"}, "dependencies": {"@repo/schemas": "workspace:*", "@repo/dtos": "workspace:*", "@repo/constants": "workspace:*", "@repo/stores": "workspace:*", "@tanstack/react-query": "^5.79.0", "react": "catalog:"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "eslint": "catalog:", "typescript": "catalog:", "@types/react": "catalog:"}}