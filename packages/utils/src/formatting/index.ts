// Formatting utility functions

export const formatNumber = (value: number, decimals: number = 2): string => {
  return value.toFixed(decimals);
};

export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${value.toFixed(decimals)}%`;
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const formatPhoneNumber = (phone: string, format: 'US' | 'INTERNATIONAL' = 'US'): string => {
  // Remove all non-numeric characters
  const cleaned = phone.replace(/\D/g, '');
  
  if (format === 'US' && cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  } else if (format === 'US' && cleaned.length === 11 && cleaned[0] === '1') {
    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }
  
  return phone; // Return original if can't format
};

export const formatPostalCode = (postalCode: string, country?: string): string => {
  const cleaned = postalCode.replace(/\s/g, '').toUpperCase();
  
  switch (country?.toUpperCase()) {
    case 'CA':
      if (cleaned.length === 6) {
        return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
      }
      break;
    case 'UK':
    case 'GB':
      if (cleaned.length >= 5) {
        return `${cleaned.slice(0, -3)} ${cleaned.slice(-3)}`;
      }
      break;
  }
  
  return postalCode;
};

export const formatBusinessNumber = (number: string, type: 'EIN' | 'SSN' | 'GENERIC' = 'GENERIC'): string => {
  const cleaned = number.replace(/\D/g, '');
  
  switch (type) {
    case 'EIN':
      if (cleaned.length === 9) {
        return `${cleaned.slice(0, 2)}-${cleaned.slice(2)}`;
      }
      break;
    case 'SSN':
      if (cleaned.length === 9) {
        return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 5)}-${cleaned.slice(5)}`;
      }
      break;
  }
  
  return number;
};

export const truncateText = (text: string, maxLength: number, suffix: string = '...'): string => {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength - suffix.length) + suffix;
};

export const capitalizeFirst = (text: string): string => {
  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
};

export const capitalizeWords = (text: string): string => {
  return text.replace(/\w\S*/g, (txt) => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
};

export const slugify = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^\w ]+/g, '')
    .replace(/ +/g, '-');
};

export const formatInvoiceNumber = (number: string | number, prefix: string = 'INV'): string => {
  const numStr = String(number).padStart(4, '0');
  return `${prefix}-${numStr}`;
};

export const formatDisplayName = (name: string, maxLength: number = 30): string => {
  const trimmed = name.trim();
  if (trimmed.length <= maxLength) return trimmed;
  
  // Try to truncate at word boundary
  const words = trimmed.split(' ');
  let result = words[0];
  
  for (let i = 1; i < words.length; i++) {
    if ((result + ' ' + words[i]).length <= maxLength) {
      result += ' ' + words[i];
    } else {
      break;
    }
  }
  
  return result.length < trimmed.length ? result + '...' : result;
};

export const formatInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .join('')
    .slice(0, 2);
};
