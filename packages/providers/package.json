{"name": "@repo/providers", "version": "0.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./auth": "./src/auth/index.ts", "./organization": "./src/organization/index.ts", "./query": "./src/query/index.ts", "./root": "./src/root/index.ts"}, "scripts": {"lint": "eslint .", "type-check": "tsc --noEmit"}, "dependencies": {"@repo/stores": "workspace:*", "@repo/queries": "workspace:*", "@tanstack/react-query": "^5.79.0", "react": "catalog:"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "eslint": "catalog:", "typescript": "catalog:", "@types/react": "catalog:"}}