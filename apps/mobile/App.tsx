import { StatusBar } from 'expo-status-bar';
import { Text, View } from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { Button } from '@repo/ui-native/components/button';
import { APP_NAME, capitalize } from '@repo/shared';
import './global.css';

export default function App() {
  return (
    <SafeAreaProvider>
      <SafeAreaView className="flex-1 bg-background">
        <View className="flex-1 items-center justify-center p-4 space-y-4">
          <Text className="text-2xl font-bold text-foreground mb-4">
            {capitalize('welcome to')} {APP_NAME}!
          </Text>
          <Text className="text-base text-muted-foreground text-center mb-8">
            This is your Expo React Native app integrated with the Turborepo monorepo.
          </Text>

          <View className="space-y-4 w-full max-w-xs">
            <Button onPress={() => console.log('Default button pressed')}>
              Default Button
            </Button>

            <Button variant="secondary" onPress={() => console.log('Secondary button pressed')}>
              Secondary Button
            </Button>

            <Button variant="outline" onPress={() => console.log('Outline button pressed')}>
              Outline Button
            </Button>

            <Button variant="destructive" onPress={() => console.log('Destructive button pressed')}>
              Destructive Button
            </Button>
          </View>

          <StatusBar style="auto" />
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
}
