import { StatusBar } from 'expo-status-bar';
import { Text, View, StyleSheet } from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { Button } from './src/components/ui/button';
import { APP_NAME, capitalize } from '@repo/shared';

export default function App() {
  return (
    <SafeAreaProvider>
      <SafeAreaView style={styles.container}>
        <View style={styles.content}>
          <Text style={styles.title}>
            {capitalize('welcome to')} {APP_NAME}!
          </Text>
          <Text style={styles.subtitle}>
            This is your Expo React Native app integrated with the Turborepo monorepo.
          </Text>

          <View style={styles.buttonContainer}>
            <Button onPress={() => console.log('Default button pressed')}>
              Default Button
            </Button>

            <Button variant="secondary" onPress={() => console.log('Secondary button pressed')}>
              Secondary Button
            </Button>

            <Button variant="outline" onPress={() => console.log('Outline button pressed')}>
              Outline Button
            </Button>

            <Button variant="destructive" onPress={() => console.log('Destructive button pressed')}>
              Destructive Button
            </Button>
          </View>

          <StatusBar style="auto" />
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#18181b',
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#71717a',
    textAlign: 'center',
    marginBottom: 32,
    paddingHorizontal: 16,
  },
  buttonContainer: {
    width: '100%',
    maxWidth: 300,
    gap: 16,
  },
});
