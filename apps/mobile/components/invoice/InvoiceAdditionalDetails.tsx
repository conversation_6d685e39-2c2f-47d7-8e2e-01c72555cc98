import { RowItem, RowItemInput, Typography } from '@/components/ui';
import { colors } from '@/constants/Colors';
import { useInvoiceStore } from '@/stores';
import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import React from 'react';
import { Alert, Image, StyleSheet, TouchableOpacity, View } from 'react-native';

interface InvoiceAdditionalDetailsProps {
	onOpenSignatureModal: () => void;
	isPickingFile: boolean;
	setIsPickingFile: (picking: boolean) => void;
}

export function InvoiceAdditionalDetails({
	onOpenSignatureModal,
	isPickingFile,
	setIsPickingFile
}: InvoiceAdditionalDetailsProps) {
	const invoiceStore = useInvoiceStore();

	// Helper functions for file handling
	const formatFileSize = (bytes: number): string => {
		if (bytes < 1024) return bytes + ' B';
		if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
		return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
	};

	const getFileExtension = (filename: string): string => {
		return filename.split('.').pop()?.toUpperCase() || 'FILE';
	};

	const getFileIcon = (mimeType: string): keyof typeof Ionicons.glyphMap => {
		if (mimeType.startsWith('image/')) return 'image-outline';
		if (mimeType.includes('pdf')) return 'document-text-outline';
		if (mimeType.includes('word') || mimeType.includes('document')) return 'document-outline';
		if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'grid-outline';
		if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'easel-outline';
		if (mimeType.includes('zip') || mimeType.includes('rar')) return 'archive-outline';
		return 'document-outline';
	};

	const handleAddAttachment = async () => {
		if (invoiceStore.attachments.length >= 3) {
			Alert.alert('Maximum 3 files allowed');
			return;
		}

		if (isPickingFile) return;

		setIsPickingFile(true);

		try {
			const result = await DocumentPicker.getDocumentAsync({
				type: ['image/*', 'application/pdf', 'text/*'],
				copyToCacheDirectory: true,
				multiple: false,
			});

			if (!result.canceled && result.assets && result.assets.length > 0) {
				const file = result.assets[0];
				
				// Check file size (max 10MB)
				if (file.size && file.size > 10 * 1024 * 1024) {
					Alert.alert('File size must be less than 10MB');
					return;
				}

				// Add attachment to store
				invoiceStore.addAttachment({
					name: file.name,
					uri: file.uri,
					type: file.mimeType || 'application/octet-stream',
					size: file.size || 0,
				});
			}
		} catch (error) {
			console.error('Error picking document:', error);
			Alert.alert('Failed to select file. Please try again.');
		} finally {
			setIsPickingFile(false);
		}
	};

	return (
		<>
			{/* Notes */}
			<RowItemInput
				leftIcon="document-text-outline"
				leftIconColor={colors.primary}
				title="Notes"
				value={invoiceStore.notes}
				placeholder="Add notes for the client..."
				onChangeText={invoiceStore.setNotes}
			/>

			{/* Terms */}
			<RowItemInput
				leftIcon="clipboard-outline"
				leftIconColor={colors.primary}
				title="Terms & Conditions"
				value={invoiceStore.terms}
				placeholder="Enter payment terms..."
				onChangeText={invoiceStore.setTerms}
			/>

			{/* Attachments */}
			<RowItem
				leftIcon="attach-outline"
				leftIconColor={colors.primary}
				title="Attachments"
				rightIcon={isPickingFile ? "hourglass-outline" : "add"}
				subtitle={`${invoiceStore.attachments.length}/3 files`}
				onPress={handleAddAttachment}
				showDivider={true}
				isCollapsed={false}
			>
				{/* Only show attachments container if there are attachments */}
				{invoiceStore.attachments.length > 0 && (
					<View style={styles.attachmentsContainer}>
						<View style={styles.attachmentsGrid}>
							{invoiceStore.attachments.map((attachment) => (
								<View key={attachment.id} style={styles.attachmentCard}>
									{/* Image Preview or File Icon */}
									<View style={styles.attachmentPreview}>
										{attachment.type.startsWith('image/') ? (
											<Image
												source={{ uri: attachment.uri }}
												style={styles.imagePreview}
											/>
										) : (
											<View style={styles.filePreview}>
												<Ionicons
													name={getFileIcon(attachment.type)}
													size={24}
													color={colors.primary}
												/>
												<Typography variant="caption" color="secondary" numberOfLines={1}>
													{getFileExtension(attachment.name)}
												</Typography>
											</View>
										)}
									</View>

									{/* File Info */}
									<View style={styles.attachmentDetails}>
										<Typography variant="caption" bold numberOfLines={1} style={styles.fileName}>
											{attachment.name}
										</Typography>
										<Typography variant="caption" color="secondary">
											{formatFileSize(attachment.size)}
										</Typography>
									</View>

									{/* Remove Button */}
									<TouchableOpacity
										style={styles.removeAttachmentBtn}
										onPress={() => invoiceStore.removeAttachment(attachment.id)}
										hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
									>
										<Ionicons name="close-circle" size={22} color={colors.error} />
									</TouchableOpacity>
								</View>
							))}
						</View>
					</View>
				)}
			</RowItem>

			{/* Signature */}
			<RowItem
				leftIcon="create-outline"
				leftIconColor={colors.primary}
				title="Signature"
				subtitle=""
				onPress={onOpenSignatureModal}
				showDivider={false}
			>
				<View style={styles.signaturePreview}>
					<View style={styles.signatureArea}>
						<View style={styles.signatureDrawing}>
							<View style={[styles.signaturePath, {
								transform: [{ rotate: '15deg' }],
								width: 80,
								height: 2,
								backgroundColor: colors.text.primary,
								position: 'absolute',
								top: 30,
								left: 20,
							}]} />
							<View style={[styles.signaturePath, {
								transform: [{ rotate: '-10deg' }],
								width: 60,
								height: 2,
								backgroundColor: colors.text.primary,
								position: 'absolute',
								top: 45,
								left: 40,
							}]} />
							<View style={[styles.signaturePath, {
								transform: [{ rotate: '25deg' }],
								width: 50,
								height: 2,
								backgroundColor: colors.text.primary,
								position: 'absolute',
								top: 25,
								left: 70,
							}]} />
						</View>
					</View>
				</View>
			</RowItem>
		</>
	);
}

const styles = StyleSheet.create({
	signaturePreview: {
		marginTop: 8,
	},
	signatureArea: {
		backgroundColor: colors.cardBackground,
		borderRadius: 8,
		borderWidth: 1,
		borderColor: colors.divider,
		padding: 20,
		minHeight: 120,
		justifyContent: 'center',
		alignItems: 'center',
	},
	signatureDrawing: {
		width: '100%',
		height: '100%',
		position: 'relative',
	},
	signaturePath: {
		position: 'absolute',
	},
	attachmentsContainer: {
		padding: 8,
	},
	attachmentsGrid: {
		flexDirection: 'row',
		flexWrap: 'wrap',
		gap: 8,
	},
	attachmentCard: {
		flexDirection: 'column',
		alignItems: 'center',
		padding: 8,
		borderRadius: 8,
		borderWidth: 1,
		borderColor: colors.divider,
		backgroundColor: colors.background,
		width: 80,
		height: 100,
		position: 'relative',
	},
	attachmentPreview: {
		width: 48,
		height: 48,
		marginBottom: 4,
		justifyContent: 'center',
		alignItems: 'center',
	},
	imagePreview: {
		width: 48,
		height: 48,
		borderRadius: 6,
		backgroundColor: colors.cardBackground,
	},
	filePreview: {
		justifyContent: 'center',
		alignItems: 'center',
		width: 48,
		height: 48,
	},
	attachmentDetails: {
		alignItems: 'center',
	},
	fileName: {
		fontSize: 10,
		textAlign: 'center',
	},
	removeAttachmentBtn: {
		position: 'absolute',
		top: -8,
		right: -8,
		backgroundColor: colors.cardBackground,
		borderRadius: 12,
		padding: 2,
		shadowColor: '#000',
		shadowOffset: { width: 0, height: 1 },
		shadowOpacity: 0.2,
		shadowRadius: 2,
		elevation: 2,
	},
}); 