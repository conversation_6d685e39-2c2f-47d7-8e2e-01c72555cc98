import { colors } from '@/constants/Colors';
import {
  useCreateCustomPaymentInstruction,
  useCustomPaymentInstructions,
  useDeleteCustomPaymentInstruction,
  useOrganizationPaymentMethods
} from '@/services/payment-method/payment-methods';
import { useInvoiceStore } from '@/stores/invoiceStore';
import { Ionicons } from '@expo/vector-icons';
import React, { useMemo, useState } from 'react';
import { Alert, Animated, Modal, StyleSheet, TextInput, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { Button } from './Button';
import { Typography } from './Typography';

const AVAILABLE_GATEWAYS = [
  {
    type: 'stripe' as const,
    name: 'Stripe',
    description: 'Credit & debit cards',
    icon: 'card-outline' as keyof typeof Ionicons.glyphMap,
  },
  {
    type: 'paypal' as const,
    name: 'PayPal',
    description: 'PayPal payments',
    icon: 'logo-paypal' as keyof typeof Ionicons.glyphMap,
  },
];

interface PaymentMethodsSheetProps {
  visible: boolean;
  onClose: () => void;
  animation: Animated.Value;
}

// Memoized Gateway Card Component
const GatewayCard = React.memo(({
  gateway,
  config,
  isSelected,
  status,
  onPress
}: {
  gateway: typeof AVAILABLE_GATEWAYS[0];
  config: any;
  isSelected: boolean;
  status: string;
  onPress: () => void;
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return colors.success;
      case 'configured': return colors.warning;
      default: return colors.text.secondary;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Active';
      case 'configured': return 'Configured';
      default: return 'Not Connected';
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.gatewayCard,
        isSelected && styles.gatewayCardActive,
        status === 'not_configured' && styles.gatewayCardNotConfigured
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.gatewayHeader}>
        <View style={[
          styles.gatewayIcon,
          isSelected && styles.gatewayIconActive
        ]}>
          <Ionicons
            name={gateway.icon}
            size={20}
            color={isSelected ? colors.cardBackground : colors.text.secondary}
          />
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(status) }]}>
          <Typography
            variant="caption"
            style={styles.statusText}
          >
            {getStatusText(status)}
          </Typography>
        </View>
      </View>

      <Typography
        variant="bodySmall"
        bold
        style={styles.gatewayName}
      >
        {gateway.name}
      </Typography>

      {config?.displayName ? (
        <Typography variant="caption" color="secondary" style={styles.gatewayAccount}>
          {config.displayName}
        </Typography>
      ) : (
        <Typography variant="caption" color="secondary" style={styles.gatewayDescription}>
          {gateway.description}
        </Typography>
      )}

      {status === 'not_configured' && (
        <View style={styles.connectPrompt}>
          <Typography variant="caption" color="primary" bold>
            Tap to Connect
          </Typography>
        </View>
      )}
    </TouchableOpacity>
  );
});

GatewayCard.displayName = 'GatewayCard';

// Memoized Custom Instruction Card Component
const CustomInstructionCard = React.memo(({
  instruction,
  isSelected,
  onToggle,
  onDelete
}: {
  instruction: any;
  isSelected: boolean;
  onToggle: () => void;
  onDelete: () => void;
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.customInstructionCard,
        isSelected && styles.customInstructionCardSelected
      ]}
      onPress={onToggle}
      activeOpacity={0.7}
    >
      <View style={styles.customInstructionHeader}>
        <View style={styles.customInstructionTitleRow}>
          <View style={[
            styles.customInstructionIcon,
            isSelected && styles.customInstructionIconSelected
          ]}>
            <Ionicons
              name="document-text-outline"
              size={18}
              color={isSelected ? colors.cardBackground : colors.text.secondary}
            />
          </View>
          <Typography
            variant="bodySmall"
            bold
            style={styles.customInstructionTitle}
          >
            {instruction.title}
          </Typography>
        </View>
        <View style={styles.customInstructionActions}>
          {isSelected && (
            <Ionicons name="checkmark-circle" size={18} color={colors.primary} />
          )}
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={onDelete}
            hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
          >
            <Ionicons name="trash-outline" size={16} color={colors.error} />
          </TouchableOpacity>
        </View>
      </View>
      <Typography variant="caption" color="secondary" numberOfLines={2} style={styles.customInstructionPreview}>
        {instruction.instructions}
      </Typography>
    </TouchableOpacity>
  );
});

CustomInstructionCard.displayName = 'CustomInstructionCard';

export function PaymentMethodsSheet({ visible, onClose, animation }: PaymentMethodsSheetProps) {
  const { data: paymentMethodsConfig } = useOrganizationPaymentMethods();
  const { data: customInstructions = [] } = useCustomPaymentInstructions();

  // Get current selections from invoice store
  const selectedGatewayId = useInvoiceStore((state) => state.paymentMethods.selectedGatewayId);
  const selectedCustomInstructionIds = useInvoiceStore((state) => state.paymentMethods.selectedCustomInstructionIds);
  const isManualSelected = useInvoiceStore((state) => state.paymentMethods.isManualSelected);

  // Get actions from invoice store
  const setSelectedGateway = useInvoiceStore((state) => state.setSelectedGateway);
  const toggleCustomInstruction = useInvoiceStore((state) => state.toggleCustomInstruction);
  const setManualSelected = useInvoiceStore((state) => state.setManualSelected);

  // Add new instruction
  const { mutateAsync: createInstruction } = useCreateCustomPaymentInstruction();
  const { mutateAsync: deleteInstruction } = useDeleteCustomPaymentInstruction();

  const [showAddInstructionForm, setShowAddInstructionForm] = useState(false);
  const [instructionTitle, setInstructionTitle] = useState('');
  const [instructionContent, setInstructionContent] = useState('');

  const handleSaveNewInstruction = async () => {
    if (!instructionTitle.trim() || !instructionContent.trim()) return;

    try {
      const newInstruction = await createInstruction({
        title: instructionTitle.trim(),
        instructions: instructionContent.trim(),
      });

      // Auto-select the new instruction when manual is selected
      if (isManualSelected) {
        toggleCustomInstruction(newInstruction.id);
      }

      setInstructionTitle('');
      setInstructionContent('');
      setShowAddInstructionForm(false);
    } catch (error) {
      console.error('Failed to create custom instruction:', error);
    }
  };

  const handleDeleteInstruction = async (instructionId: string) => {
    try {
      await deleteInstruction(instructionId);
      // Remove from selections if it was selected
      if (selectedCustomInstructionIds.includes(instructionId)) {
        toggleCustomInstruction(instructionId);
      }
    } catch (error) {
      console.error('Failed to delete custom instruction:', error);
    }
  };

  const handleGatewayToggle = (gatewayType: string) => {
    const config = gatewayConfigs.get(gatewayType);

    // Don't allow selection of unconfigured gateways
    if (!config || config.status === 'not_configured') {
      Alert.alert(`Please configure your ${gatewayType} account first.\n\nThis would normally open the ${gatewayType} connection flow.`);
      return;
    }

    const isCurrentlySelected = selectedGatewayId === config.id;
    setSelectedGateway(isCurrentlySelected ? undefined : config.id);
  };

  const handleManualToggle = () => {
    setManualSelected(!isManualSelected);
  };

  const isValidSelection = () => {
    return selectedGatewayId || (isManualSelected && selectedCustomInstructionIds.length > 0);
  };

  // Memoized computed values
  const configuredGateways = useMemo(() => paymentMethodsConfig?.paymentGateways || [], [paymentMethodsConfig]);

  const gatewayConfigs = useMemo(() => {
    const configs = new Map();
    configuredGateways.forEach(gateway => {
      configs.set(gateway.type, gateway);
    });
    return configs;
  }, [configuredGateways]);

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      statusBarTranslucent={true}
      presentationStyle="overFullScreen"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <TouchableWithoutFeedback onPress={onClose}>
          <View style={{ flex: 1 }} />
        </TouchableWithoutFeedback>

        <Animated.View style={[styles.sheetContainer, {
          transform: [{
            translateY: animation.interpolate({
              inputRange: [0, 1],
              outputRange: [600, 0],
            }),
          }],
        }]}>
          <View style={styles.sheetHeader}>
            <Typography variant="body" bold>Payment Methods</Typography>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={colors.text.secondary} />
            </TouchableOpacity>
          </View>

          <KeyboardAwareScrollView
            style={styles.sheetContent}
            contentContainerStyle={styles.sheetContentContainer}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
            bottomOffset={20}
            extraKeyboardSpace={0}
          >
            {/* Summary */}
            <View style={styles.summarySection}>
              <Typography variant="bodySmall" color="secondary">
                {isValidSelection()
                  ? selectedGatewayId
                    ? '1 payment method selected'
                    : `Manual payment with ${selectedCustomInstructionIds.length} instruction${selectedCustomInstructionIds.length === 1 ? '' : 's'}`
                  : 'No payment methods selected'
                }
              </Typography>
            </View>

            {/* Payment Gateways */}
            <View style={styles.section}>
              <Typography variant="bodySmall" color="secondary" style={styles.sectionTitle}>
                Payment Gateways
              </Typography>
              <Typography variant="caption" color="secondary" style={styles.sectionDescription}>
                Connect your payment accounts to accept online payments
              </Typography>

              <View style={styles.gatewaysGrid}>
                {AVAILABLE_GATEWAYS.map((gateway) => {
                  const config = gatewayConfigs.get(gateway.type);
                  const status = config?.status || 'not_configured';
                  // Only show as active if it's actually configured AND currently selected
                  const isSelected = status !== 'not_configured' && config?.id === selectedGatewayId;

                  return (
                    <GatewayCard
                      key={gateway.type}
                      gateway={gateway}
                      config={config}
                      isSelected={isSelected}
                      status={status}
                      onPress={() => handleGatewayToggle(gateway.type)}
                    />
                  );
                })}
              </View>
            </View>

            {/* Manual Payment Method */}
            <View style={styles.section}>
              <Typography variant="bodySmall" color="secondary" style={styles.sectionTitle}>
                Manual Payment
              </Typography>
              <Typography variant="caption" color="secondary" style={styles.sectionDescription}>
                Accept payments via bank transfer, checks, or other offline methods
              </Typography>

              <TouchableOpacity
                style={[
                  styles.manualCard,
                  isManualSelected && styles.manualCardSelected
                ]}
                onPress={handleManualToggle}
                activeOpacity={0.7}
              >
                <View style={styles.manualCardHeader}>
                  <View style={[
                    styles.manualIcon,
                    isManualSelected && styles.manualIconSelected
                  ]}>
                    <Ionicons
                      name="document-text"
                      size={20}
                      color={isManualSelected ? colors.cardBackground : colors.text.secondary}
                    />
                  </View>
                  <View style={styles.manualInfo}>
                    <Typography variant="bodySmall" bold>Manual Payment Instructions</Typography>
                    <Typography variant="caption" color="secondary">
                      {isManualSelected
                        ? selectedCustomInstructionIds.length > 0
                          ? `${selectedCustomInstructionIds.length} instruction${selectedCustomInstructionIds.length === 1 ? '' : 's'} selected`
                          : 'Select payment instructions below'
                        : 'Add payment instructions for offline payments'
                      }
                    </Typography>
                  </View>
                  {isManualSelected && selectedCustomInstructionIds.length > 0 && (
                    <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
                  )}
                </View>
              </TouchableOpacity>
            </View>

            {/* Custom Payment Instructions - Only show when manual is selected */}
            {isManualSelected && (
              <View style={styles.section}>
                <View style={styles.customInstructionsHeader}>
                  <Typography variant="bodySmall" color="secondary" style={styles.sectionTitle}>
                    Select Payment Instructions
                  </Typography>
                  <TouchableOpacity
                    style={styles.addInstructionsButton}
                    onPress={() => setShowAddInstructionForm(true)}
                    activeOpacity={0.7}
                  >
                    <Ionicons name="add" size={16} color={colors.primary} />
                    <Typography variant="caption" color="primary" style={styles.addButtonText}>
                      Add Instructions
                    </Typography>
                  </TouchableOpacity>
                </View>

                {customInstructions.length > 0 ? (
                  <View style={styles.customInstructionsList}>
                    {customInstructions.map((instruction) => {
                      const isSelected = selectedCustomInstructionIds.includes(instruction.id);

                      return (
                        <CustomInstructionCard
                          key={instruction.id}
                          instruction={instruction}
                          isSelected={isSelected}
                          onToggle={() => toggleCustomInstruction(instruction.id)}
                          onDelete={() => handleDeleteInstruction(instruction.id)}
                        />
                      );
                    })}
                  </View>
                ) : (
                  <View style={styles.emptyCustomInstructions}>
                    <Typography variant="bodySmall" color="secondary" center>
                      No payment instructions available
                    </Typography>
                    <Typography variant="caption" color="secondary" center style={styles.emptyHint}>
                      Add instructions for bank transfers, checks, or other payment methods
                    </Typography>
                  </View>
                )}

                {/* Validation message */}
                {isManualSelected && selectedCustomInstructionIds.length === 0 && (
                  <View style={styles.validationMessage}>
                    <Ionicons name="alert-circle-outline" size={16} color={colors.warning} />
                    <Typography variant="caption" color="error" style={styles.validationText}>
                      Select at least one payment instruction to use manual payment
                    </Typography>
                  </View>
                )}
              </View>
            )}
          </KeyboardAwareScrollView>
        </Animated.View>

        {/* Add Instructions Modal */}
        <Modal
          visible={showAddInstructionForm}
          transparent={true}
          animationType="none"
          statusBarTranslucent={true}
          presentationStyle="overFullScreen"
          onRequestClose={() => setShowAddInstructionForm(false)}
        >
          <View style={styles.addModalContainer}>
            <TouchableOpacity
              style={styles.addModalBackdrop}
              activeOpacity={1}
              onPress={() => setShowAddInstructionForm(false)}
            />

            <Animated.View style={[styles.addModalContent, {
              transform: [{
                translateY: animation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [600, 0],
                }),
              }],
            }]}>
              <View style={styles.addModalHeader}>
                <Typography variant="body" bold>Add Payment Instructions</Typography>
                <TouchableOpacity onPress={() => setShowAddInstructionForm(false)} style={styles.addModalCloseButton}>
                  <Ionicons name="close" size={24} color={colors.text.secondary} />
                </TouchableOpacity>
              </View>

              <KeyboardAwareScrollView
                style={styles.addModalForm}
                contentContainerStyle={styles.addModalFormContent}
                keyboardShouldPersistTaps="handled"
              >
                <View style={styles.formField}>
                  <Typography variant="bodySmall" color="secondary" style={styles.fieldLabel}>
                    Payment Method Name*
                  </Typography>
                  <TextInput
                    style={styles.titleInput}
                    placeholder="e.g., Bank Transfer, Check, Cash"
                    placeholderTextColor={colors.text.secondary}
                    value={instructionTitle}
                    onChangeText={setInstructionTitle}
                    autoCapitalize="words"
                    returnKeyType="next"
                    blurOnSubmit={false}
                  />
                </View>

                <View style={styles.formField}>
                  <Typography variant="bodySmall" color="secondary" style={styles.fieldLabel}>
                    Payment Instructions*
                  </Typography>
                  <TextInput
                    style={styles.instructionsInput}
                    placeholder="Enter detailed payment instructions that will appear on the invoice..."
                    placeholderTextColor={colors.text.secondary}
                    value={instructionContent}
                    onChangeText={setInstructionContent}
                    multiline
                    numberOfLines={3}
                    textAlignVertical="top"
                    returnKeyType="default"
                  />
                  <Typography variant="caption" color="secondary" style={styles.inputHint}>
                    Include account details, routing numbers, or specific payment procedures
                  </Typography>
                </View>
                <View style={styles.addModalActions}>
                  <Button
                    title="Cancel"
                    variant="secondary"
                    onPress={() => setShowAddInstructionForm(false)}
                    style={styles.cancelButton}
                  />
                  <Button
                    title="Save"
                    onPress={handleSaveNewInstruction}
                    disabled={!instructionTitle.trim() || !instructionContent.trim()}
                    style={styles.saveButton}
                  />
                </View>
              </KeyboardAwareScrollView>
            </Animated.View>
          </View>
        </Modal>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  sheetContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.cardBackground,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 6,
    maxHeight: '90%',
  },
  sheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  closeButton: {
    padding: 4,
  },
  sheetContent: {
    paddingHorizontal: 16,
    maxHeight: 600,
  },
  sheetContentContainer: {
    paddingBottom: 20,
  },
  summarySection: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
    marginBottom: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 11,
    marginBottom: 12,
    lineHeight: 14,
  },
  gatewaysGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  gatewayCard: {
    width: '48%',
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: colors.divider,
    minHeight: 100,
  },
  gatewayCardActive: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryVeryLight,
  },
  gatewayCardNotConfigured: {
    borderStyle: 'dashed',
  },
  gatewayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  gatewayIcon: {
    width: 32,
    height: 32,
    borderRadius: 8,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  gatewayIconActive: {
    backgroundColor: colors.primary,
  },
  statusBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    minWidth: 60,
    alignItems: 'center',
  },
  statusText: {
    color: colors.cardBackground,
    fontSize: 10,
    fontWeight: '600',
  },
  gatewayName: {
    fontSize: 12,
    marginBottom: 2,
    color: colors.text.primary,
  },
  gatewayAccount: {
    fontSize: 10,
    lineHeight: 12,
  },
  gatewayDescription: {
    fontSize: 10,
    lineHeight: 12,
  },
  connectPrompt: {
    marginTop: 4,
    paddingTop: 4,
    borderTopWidth: 1,
    borderTopColor: colors.divider,
  },
  customInstructionsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  addInstructionsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primaryVeryLight,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  addButtonText: {
    marginLeft: 4,
    fontSize: 11,
    fontWeight: '600',
  },
  customInstructionsList: {
    gap: 8,
  },
  customInstructionCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  customInstructionCardSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryVeryLight,
  },
  customInstructionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  customInstructionTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  customInstructionIcon: {
    width: 24,
    height: 24,
    borderRadius: 4,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  customInstructionIconSelected: {
    backgroundColor: colors.primaryVeryLight,
  },
  customInstructionTitle: {
    flex: 1,
    fontSize: 12,
  },
  customInstructionActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  deleteButton: {
    padding: 2,
  },
  customInstructionPreview: {
    fontSize: 10,
    lineHeight: 14,
    marginLeft: 32,
  },
  emptyCustomInstructions: {
    backgroundColor: colors.background,
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
  },
  emptyHint: {
    marginTop: 4,
  },
  // Add Instructions Modal styles
  addModalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  addModalBackdrop: {
    flex: 1,
  },
  addModalContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.cardBackground,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '85%',
    minHeight: '50%',
  },
  addModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  addModalCloseButton: {
    padding: 4,
  },
  addModalForm: {
    flex: 1,
    padding: 16,
  },
  addModalFormContent: {
    // paddingBottom: 20,
  },
  formField: {
    // marginBottom: 12,
  },
  fieldLabel: {
    marginBottom: 8,
    fontWeight: '500',
  },
  titleInput: {
    backgroundColor: colors.background,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: colors.divider,
    fontSize: 14,
    color: colors.text.primary,
  },
  instructionsInput: {
    backgroundColor: colors.background,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: colors.divider,
    fontSize: 14,
    color: colors.text.primary,
    minHeight: 120,
    maxHeight: 180,
  },
  inputHint: {
    marginTop: 6,
    lineHeight: 14,
  },
  addModalActions: {
    flexDirection: 'row',
    gap: 12,
    // padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.divider,
    backgroundColor: colors.cardBackground,
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 1,
  },
  manualCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  manualCardSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryVeryLight,
  },
  manualCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  manualIcon: {
    width: 32,
    height: 32,
    borderRadius: 8,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  manualIconSelected: {
    backgroundColor: colors.primary,
  },
  manualInfo: {
    flex: 1,
    marginLeft: 12,
  },
  validationMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderColor: colors.warning,
    borderRadius: 8,
  },
  validationText: {
    marginLeft: 8,
  },
}); 