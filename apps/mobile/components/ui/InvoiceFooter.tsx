import { colors } from '@/constants/Colors';
import { useTaxOptions } from '@/core/hooks/use-tax-options';
import { useClients } from '@/services/client/clients';
import { useCreateInvoice } from '@/services/invoice/create';
import { formatCurrency, useInvoiceStore } from '@/stores';
import { useActiveOrganizationId } from '@/stores';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useMemo, useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { ActionDialog } from './ActionDialog';
import { Button } from './Button';
import { ErrorDialog } from './ErrorDialog';
import { SuccessDialog } from './SuccessDialog';
import { Typography } from './Typography';

interface InvoiceFooterProps {
    isFormValid: boolean;
    isFixed?: boolean;
    isPinned?: boolean;
    onTogglePin?: () => void;
    onShowDetails?: () => void;
    paddingBottom?: number;
    // Edit mode props
    editInvoiceId?: string;
    onUpdate?: (data: any) => Promise<any>;
}

export function InvoiceFooter({ 
    isFormValid, 
    isFixed = false, 
    isPinned = true,
    onTogglePin,
    onShowDetails,
    paddingBottom = 8,
    // Edit mode props
    editInvoiceId,
    onUpdate
}: InvoiceFooterProps) {
    const invoiceStore = useInvoiceStore();
    const activeOrganizationId = useActiveOrganizationId();
    const { data: taxOptions = [] } = useTaxOptions();
    const { clients = [] } = useClients();
    const { createInvoice } = useCreateInvoice();

    // Dialog states
    const [showErrorDialog, setShowErrorDialog] = useState(false);
    const [errorDialogConfig, setErrorDialogConfig] = useState({ title: '', message: '' });
    const [showSuccessDialog, setShowSuccessDialog] = useState(false);
    const [successDialogConfig] = useState({ title: '', message: '' });
    const [showActionDialog, setShowActionDialog] = useState(false);
    const [actionDialogConfig, setActionDialogConfig] = useState({ 
        title: '', 
        message: '', 
        actions: [] as any[] 
    });

    // Helper function to show error dialog
    const showError = (title: string, message: string) => {
        setErrorDialogConfig({ title, message });
        setShowErrorDialog(true);
    };

    // Helper function to show action dialog
    const showActions = (title: string, message: string, actions: any[]) => {
        setActionDialogConfig({ title, message, actions });
        setShowActionDialog(true);
    };

    // Get client using React Query hook instead of clientStore
    const getClientById = useMemo(() => (clientId: string) => {
        return clients.find(client => client.id === clientId && client.organizationId === activeOrganizationId);
    }, [clients, activeOrganizationId]);

    // Get tax information using React Query hook instead of taxStore
    const getTaxInfo = () => {
        if (!invoiceStore.taxConfig.taxId) {
            return { name: 'Tax', rate: 0 };
        }
        
        const taxOption = taxOptions.find(option => option.id === invoiceStore.taxConfig.taxId);
        return {
            name: taxOption?.name || 'Tax',
            rate: taxOption?.rate || 0
        };
    };

    // Calculate total discounts
    const activeItems = invoiceStore.lineItems.filter(item => 
        item.description && item.description.trim() !== ''
    );
    const totalDiscounts = activeItems.reduce((sum, item) => {
        const discountValue = parseFloat(item.discount || '0') || 0;
        if (discountValue > 0) {
            const quantity = parseFloat(item.quantity) || 0;
            const price = parseFloat(item.price) || 0;
            const subtotal = quantity * price;
            
            if (item.discountType === 'percentage') {
                return sum + (subtotal * (discountValue / 100));
            } else {
                return sum + discountValue;
            }
        }
        return sum;
    }, 0);

    const handleSaveDraft = async () => {
        if (!isFormValid) {
            showError('Incomplete Invoice', 'Please fill in all required fields before saving.');
            return;
        }

        if (!activeOrganizationId) {
            showError('Error', 'No organization selected.');
            return;
        }

        const client = getClientById(invoiceStore.selectedClientId || '');
        const taxInfo = getTaxInfo();
        
        // Create invoice data for API
        const invoiceData = {
            organizationId: activeOrganizationId,
            invoiceNumber: invoiceStore.invoiceNumber,
            status: 'draft' as const,
            issueDate: invoiceStore.invoiceDate,
            dueDate: invoiceStore.dueDate,
            clientId: invoiceStore.selectedClientId || '',
            clientName: client?.name || 'Unknown Client',
            lineItems: activeItems.map(item => ({
                id: item.id,
                description: item.description,
                itemDescription: item.itemDescription || '',
                quantity: parseFloat(item.quantity) || 0,
                unitPrice: parseFloat(item.price) || 0,
                unit: (item.unit || 'fixed') as any,
                discount: parseFloat(item.discount || '0') || undefined,
                discountType: (item.discountType || 'percentage') as any,
                total: parseFloat(item.total) || 0,
                taxable: item.taxable || false,
                taxRate: item.taxRate || 0,
            })),
            payment: {
                dueDate: invoiceStore.dueDate,
                terms: invoiceStore.terms || 'Net 30',
            },
            totals: {
                subtotal: invoiceStore.getSubtotal(),
                taxTotal: invoiceStore.getTaxAmount(),
                total: invoiceStore.getTotal(),
                discountTotal: totalDiscounts,
                amountPaid: 0,
                amountDue: invoiceStore.getTotal(),
            },
            taxInfo: {
                taxName: taxInfo.name,
                defaultRate: taxInfo.rate,
                inclusive: invoiceStore.taxConfig.inclusive,
                enabled: invoiceStore.taxConfig.method !== 'none'
            },
            notes: invoiceStore.notes,
            terms: invoiceStore.terms,
            // Legacy fields for backward compatibility
            amount: formatCurrency(invoiceStore.getTotal()),
            date: invoiceStore.invoiceDate,
            company: undefined,
            comments: [],
            attachments: invoiceStore.attachments || [],
        };
        
        try {
            let result: any;
            
            if (editInvoiceId && onUpdate) {
                // Edit mode - update existing invoice
                result = await onUpdate({
                    id: editInvoiceId,
                    ...invoiceData,
                });
                console.log('Invoice updated:', result);
                
                showActions(
                    'Invoice Updated',
                    `Your invoice ${result.invoiceNumber} has been updated.`,
                    [
                        { title: 'Continue Editing', variant: 'outline' as const, onPress: () => {} },
                        { 
                            title: 'View Invoice', 
                            variant: 'primary' as const,
                            onPress: () => {
                                router.replace(`/invoice-detail?invoiceId=${result.id}`);
                            }
                        }
                    ]
                );
            } else {
                // Create mode - create new invoice
                result = await createInvoice(invoiceData);
                console.log('Invoice saved as draft:', result);
                
                showActions(
                    'Draft Saved',
                    `Your invoice ${result.id} has been saved as a draft.`,
                    [
                        { title: 'Continue Editing', variant: 'outline' as const, onPress: () => {} },
                        { 
                            title: 'View Invoice', 
                            variant: 'primary' as const,
                            onPress: () => {
                                router.replace(`/invoice-detail?invoiceId=${result.id}`);
                            }
                        }
                    ]
                );
            }
        } catch (error) {
            console.error('Failed to save invoice:', error);
            showError('Error', `Failed to ${editInvoiceId ? 'update' : 'save'} invoice. Please try again.`);
        }
    };

    const handleSendInvoice = async () => {
        if (!isFormValid) {
            showError('Incomplete Invoice', 'Please fill in all required fields before sending.');
            return;
        }

        if (!activeOrganizationId) {
            showError('Error', 'No organization selected.');
            return;
        }

        const client = getClientById(invoiceStore.selectedClientId || '');
        const taxInfo = getTaxInfo();

        // Create invoice data for API
        const invoiceData = {
            organizationId: activeOrganizationId,
            invoiceNumber: invoiceStore.invoiceNumber,
            status: 'pending' as const,
            issueDate: invoiceStore.invoiceDate,
            dueDate: invoiceStore.dueDate,
            clientId: invoiceStore.selectedClientId || '',
            clientName: client?.name || 'Unknown Client',
            lineItems: activeItems.map(item => ({
                id: item.id,
                description: item.description,
                itemDescription: item.itemDescription || '',
                quantity: parseFloat(item.quantity) || 0,
                unitPrice: parseFloat(item.price) || 0,
                unit: (item.unit || 'fixed') as any,
                discount: parseFloat(item.discount || '0') || undefined,
                discountType: (item.discountType || 'percentage') as any,
                total: parseFloat(item.total) || 0,
                taxable: item.taxable || false,
                taxRate: item.taxRate || 0,
            })),
            payment: {
                dueDate: invoiceStore.dueDate,
                terms: invoiceStore.terms || 'Net 30',
            },
            totals: {
                subtotal: invoiceStore.getSubtotal(),
                taxTotal: invoiceStore.getTaxAmount(),
                total: invoiceStore.getTotal(),
                discountTotal: totalDiscounts,
                amountPaid: 0,
                amountDue: invoiceStore.getTotal(),
            },
            taxInfo: {
                taxName: taxInfo.name,
                defaultRate: taxInfo.rate,
                inclusive: invoiceStore.taxConfig.inclusive,
                enabled: invoiceStore.taxConfig.method !== 'none'
            },
            notes: invoiceStore.notes,
            terms: invoiceStore.terms,
            // Legacy fields for backward compatibility
            amount: formatCurrency(invoiceStore.getTotal()),
            date: invoiceStore.invoiceDate,
            company: undefined,
            comments: [],
            attachments: invoiceStore.attachments || [],
        };

        try {
            let result: any;
            
            if (editInvoiceId && onUpdate) {
                // Edit mode - update existing invoice
                result = await onUpdate({
                    id: editInvoiceId,
                    ...invoiceData,
                });
                console.log('Invoice updated and sent:', result);
                
                showActions(
                    'Invoice Updated & Sent!',
                    `Your invoice ${result.invoiceNumber} has been updated and marked as sent.`,
                    [
                        { title: 'Create Another', variant: 'outline' as const, onPress: () => {} },
                        { 
                            title: 'View Invoice', 
                            variant: 'primary' as const,
                            onPress: () => {
                                router.replace(`/invoice-detail?invoiceId=${result.id}`);
                            }
                        }
                    ]
                );
            } else {
                // Create mode - create new invoice
                result = await createInvoice(invoiceData);
                console.log('Invoice sent:', result);
                
                showActions(
                    'Invoice Sent!',
                    `Your invoice ${result.id} has been created and marked as sent.`,
                    [
                        { title: 'Create Another', variant: 'outline' as const, onPress: () => {} },
                        { 
                            title: 'View Invoice', 
                            variant: 'primary' as const,
                            onPress: () => {
                                router.replace(`/invoice-detail?invoiceId=${result.id}`);
                            }
                        }
                    ]
                );
            }
        } catch (error) {
            console.error('Failed to send invoice:', error);
            showError('Error', `Failed to ${editInvoiceId ? 'update' : 'send'} invoice. Please try again.`);
        }
    };

    const containerStyle = [
        isFixed ? styles.fixedFooter : styles.inlineFooter,
        { paddingBottom: Math.max(8, paddingBottom) }
    ];

    return (
        <View style={containerStyle}>
            {/* Pin Toggle Button - only show when footer is fixed */}
            {isFixed && onTogglePin && (
                <TouchableOpacity 
                    style={styles.pinToggle}
                    onPress={onTogglePin}
                    hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
                >
                    <Ionicons 
                        name={isPinned ? "push" : "arrow-down"} 
                        size={16} 
                        color={colors.text.secondary} 
                    />
                    <Typography variant="caption" color="secondary" style={{ marginLeft: 4 }}>
                        {isPinned ? 'Unpin' : 'Pin'}
                    </Typography>
                </TouchableOpacity>
            )}

            <View style={styles.summaryContainer}>
                {invoiceStore.taxConfig.inclusive && invoiceStore.getTaxAmount() !== 0 ? (
                    <>
                        <View style={styles.summaryRow}>
                            <Typography variant="bodySmall" color="secondary">
                                Subtotal (excl. tax)
                            </Typography>
                            <Typography variant="bodySmall">
                                {formatCurrency(invoiceStore.getPreTaxAmount())}
                            </Typography>
                        </View>
                        {totalDiscounts > 0 && (
                            <View style={styles.summaryRow}>
                                <Typography variant="bodySmall" color="secondary">Total Discounts</Typography>
                                <Typography variant="bodySmall" color="success">-{formatCurrency(totalDiscounts)}</Typography>
                            </View>
                        )}
                        <View style={styles.summaryRow}>
                            <Typography variant="bodySmall" color="secondary">
                                {invoiceStore.taxConfig.method === 'as_deduction' ? 'Tax Deduction' : 'Tax (included)'}
                            </Typography>
                            <Typography variant="bodySmall">
                                {invoiceStore.taxConfig.method === 'as_deduction' ? '-' : ''}{formatCurrency(Math.abs(invoiceStore.getTaxAmount()))}
                            </Typography>
                        </View>
                    </>
                ) : (
                    <>
                        <View style={styles.summaryRow}>
                            <Typography variant="bodySmall" color="secondary">Subtotal</Typography>
                            <Typography variant="bodySmall">{formatCurrency(invoiceStore.getSubtotal())}</Typography>
                        </View>
                        {totalDiscounts > 0 && (
                            <View style={styles.summaryRow}>
                                <Typography variant="bodySmall" color="secondary">Total Discounts</Typography>
                                <Typography variant="bodySmall" color="success">-{formatCurrency(totalDiscounts)}</Typography>
                            </View>
                        )}
                        {invoiceStore.getTaxAmount() !== 0 && (
                            <View style={styles.summaryRow}>
                                <Typography variant="bodySmall" color="secondary">
                                    {invoiceStore.taxConfig.method === 'as_deduction' ? 'Tax Deduction' : 'Tax'}
                                </Typography>
                                <Typography variant="bodySmall">
                                    {invoiceStore.taxConfig.method === 'as_deduction' ? '-' : ''}{formatCurrency(Math.abs(invoiceStore.getTaxAmount()))}
                                </Typography>
                            </View>
                        )}
                    </>
                )}
                <View style={styles.summaryTotalRow}>
                    <Typography variant="body" bold>Total</Typography>
                    <Typography variant="body" color="primary" bold>{formatCurrency(invoiceStore.getTotal())}</Typography>
                </View>
                
                {/* Details Button - beneath the total */}
                {onShowDetails && (
                    <TouchableOpacity 
                        style={styles.showDetailsButton}
                        onPress={onShowDetails}
                        hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
                    >
                        <Typography variant="caption" color="primary">
                            Details
                        </Typography>
                        <Ionicons name="chevron-forward" size={14} color={colors.primary} />
                    </TouchableOpacity>
                )}
            </View>

            {/* Action Buttons */}
            <View style={styles.actionButtonsContainer}>
                <Button
                    title={editInvoiceId ? "Update & Send" : "Send"}
                    icon="paper-plane"
                    onPress={handleSendInvoice}
                    style={{
                        ...styles.sendButton,
                        backgroundColor: !isFormValid ? colors.disabled : colors.primary
                    }}
                    disabled={!isFormValid}
                />
                <Button
                    title={editInvoiceId ? "Update Draft" : "Save Draft"}
                    variant="outline"
                    icon="document"
                    onPress={handleSaveDraft}
                    style={styles.saveButton}
                />
            </View>

            {/* Custom Dialogs */}
            <ErrorDialog
                visible={showErrorDialog}
                title={errorDialogConfig.title}
                message={errorDialogConfig.message}
                onClose={() => setShowErrorDialog(false)}
            />

            <SuccessDialog
                visible={showSuccessDialog}
                title={successDialogConfig.title}
                message={successDialogConfig.message}
                onClose={() => setShowSuccessDialog(false)}
            />

            <ActionDialog
                visible={showActionDialog}
                title={actionDialogConfig.title}
                message={actionDialogConfig.message}
                actions={actionDialogConfig.actions}
                onClose={() => setShowActionDialog(false)}
                icon="checkmark-circle"
                iconColor={colors.success}
            />
        </View>
    );
}

const styles = StyleSheet.create({
    fixedFooter: {
        position: 'absolute',
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: colors.cardBackground,
        borderTopWidth: 2,
        borderTopColor: colors.divider,
        paddingHorizontal: 16,
        paddingTop: 12,
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: -4 },
        shadowOpacity: 0.15,
        shadowRadius: 8,
        elevation: 12,
        zIndex: 9999,
    },
    inlineFooter: {
        backgroundColor: colors.cardBackground,
        marginHorizontal: 0, // Match the full-width style of RowItem
        marginTop: 0, // Remove margin to blend with other components
        paddingHorizontal: 16,
        paddingTop: 16, // Match RowItem padding
        borderTopWidth: 1, // Add subtle divider like other sections
        borderTopColor: colors.divider,
    },
    pinToggle: {
        padding: 6,
        borderRadius: 6,
        backgroundColor: 'rgba(0, 0, 0, 0.05)',
    },
    summaryContainer: {
        marginBottom: 16,
    },
    summaryRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 3,
        paddingHorizontal: 2,
    },
    summaryTotalRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 3,
        paddingHorizontal: 2,
        borderTopWidth: 1,
        borderTopColor: colors.divider,
        marginTop: 6,
        paddingTop: 9,
    },
    showDetailsButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 8,
        borderRadius: 6,
        backgroundColor: 'rgba(0, 0, 0, 0.05)',
        marginTop: 8,
        alignSelf: 'flex-end',
    },
    actionButtonsContainer: {
        flexDirection: 'row',
        gap: 12,
    },
    sendButton: {
        flex: 1,
    },
    saveButton: {
        flex: 1,
    },
}); 