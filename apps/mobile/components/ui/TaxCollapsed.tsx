import { taxOptions } from '@/constants/data';
import { formatCurrency, TaxMethod } from '@/stores';

export function getTaxCollapsedText(taxConfig: any, getTaxAmount: () => number) {
    const amount = getTaxAmount();
    
    switch (taxConfig.method) {
        case TaxMethod.NONE:
            return 'No tax applied';
            
        case TaxMethod.ON_TOTAL:
            const tax = taxOptions.find(t => t.id === taxConfig.taxId);
            const rate = tax?.rate || 0;
            if (amount === 0) return 'No tax applied';
            const inclusivity = taxConfig.inclusive ? 'Inclusive' : 'Exclusive';
            return `${tax?.name || 'Tax'} ${rate}% (${inclusivity}) • ${formatCurrency(Math.abs(amount))}`;
            
        case TaxMethod.PER_ITEM:
            if (amount === 0) return 'Per-item tax • No tax applied';
            const itemInclusivity = taxConfig.inclusive ? 'Inclusive' : 'Exclusive';
            return `Per-item tax (${itemInclusivity}) • ${formatCurrency(Math.abs(amount))}`;
            
        case TaxMethod.AS_DEDUCTION:
            const deductionTax = taxOptions.find(t => t.id === taxConfig.taxId);
            const deductionRate = deductionTax?.rate || 0;
            return `${deductionTax?.name || 'Tax'} deduction ${deductionRate}% • -${formatCurrency(Math.abs(amount))}`;
            
        default:
            return 'No tax applied';
    }
} 