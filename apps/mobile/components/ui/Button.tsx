import { colors } from '@/constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import React, { ReactNode } from 'react';
import { StyleSheet, Text, TextStyle, TouchableOpacity, View, ViewStyle } from 'react-native';
import {
  MobileButtonProps,
  ButtonVariant,
  ButtonSize,
  BUTTON_VARIANT_MAP
} from '@repo/ui-interfaces/button';

// Legacy mobile-specific variant type for backward compatibility
type LegacyButtonVariant = 'primary' | 'secondary' | 'outline' | 'danger' | 'icon';

interface ButtonProps extends Omit<MobileButtonProps, 'variant'> {
  // Support both new unified variants and legacy mobile variants
  variant?: ButtonVariant | LegacyButtonVariant;
  icon?: React.ComponentProps<typeof Ionicons>['name'];
  iconRight?: React.ComponentProps<typeof Ionicons>['name'];
  style?: ViewStyle;
  textStyle?: TextStyle;
}

// Helper function to normalize variant names
const normalizeVariant = (variant: ButtonVariant | LegacyButtonVariant): LegacyButtonVariant => {
  // Map new unified variants to legacy mobile variants
  const variantMap: Record<ButtonVariant, LegacyButtonVariant> = {
    primary: 'primary',
    secondary: 'secondary',
    outline: 'outline',
    destructive: 'danger',
    ghost: 'icon',
    link: 'icon',
  };

  return variantMap[variant as ButtonVariant] || variant as LegacyButtonVariant;
};

export function Button({
  onPress,
  title,
  icon,
  iconRight,
  variant = 'primary',
  size = 'medium',
  style,
  textStyle,
  disabled = false,
  children,
}: ButtonProps) {
  const normalizedVariant = normalizeVariant(variant);
  const getIconSize = () => {
    switch (size) {
      case 'small': return 14;
      case 'medium': return 16;
      case 'large': return 20;
      default: return 16;
    }
  };

  const getIconColor = () => {
    if (disabled) return colors.text.tertiary;

    switch (normalizedVariant) {
      case 'primary': return 'white';
      case 'secondary': return colors.primary;
      case 'outline': return colors.primary;
      case 'danger': return 'white';
      case 'icon': return colors.primary;
      default: return colors.primary;
    }
  };

  const renderContent = () => {
    // If children are provided, just render them
    if (children) return children;

    return (
      <View style={styles.contentContainer}>
        {icon && (
          <Ionicons 
            name={icon} 
            size={getIconSize()} 
            color={getIconColor()} 
            style={title ? styles.leftIcon : undefined} 
          />
        )}
        
        {title && (
          <Text style={[
            styles.text,
            styles[`${normalizedVariant}Text`],
            styles[`${size}Text`],
            disabled && styles.disabledText,
            textStyle
          ]}>
            {title}
          </Text>
        )}
        
        {iconRight && (
          <Ionicons 
            name={iconRight} 
            size={getIconSize()} 
            color={getIconColor()} 
            style={title ? styles.rightIcon : undefined} 
          />
        )}
      </View>
    );
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled}
      style={[
        styles.button,
        styles[normalizedVariant],
        styles[size],
        disabled && styles.disabled,
        style
      ]}
    >
      {renderContent()}
    </TouchableOpacity>
  );
}

// Action Button variant used in many places
export function ActionButton({ 
  onPress, 
  icon, 
  title, 
  style, 
  bgColor = 'rgba(52, 144, 243, 0.08)',
  textColor = colors.primary 
}: { 
  onPress: () => void;
  icon: React.ComponentProps<typeof Ionicons>['name'];
  title: string;
  style?: ViewStyle;
  bgColor?: string;
  textColor?: string;
}) {
  return (
    <TouchableOpacity 
      onPress={onPress}
      style={[styles.actionButton, { backgroundColor: bgColor }, style]}
    >
      <Ionicons name={icon} size={16} color={textColor} />
      <Text style={[styles.actionButtonText, { color: textColor }]}>{title}</Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  // Variants
  primary: {
    backgroundColor: colors.primary,
  },
  secondary: {
    backgroundColor: 'rgba(52, 144, 243, 0.08)',
  },
  outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.primary,
  },
  danger: {
    backgroundColor: colors.error,
  },
  icon: {
    backgroundColor: 'transparent',
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  // Sizes
  small: {
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
  medium: {
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  large: {
    paddingVertical: 14,
    paddingHorizontal: 20,
  },
  // Text styles
  text: {
    fontWeight: '600',
  },
  primaryText: {
    color: 'white',
  },
  secondaryText: {
    color: colors.primary,
  },
  outlineText: {
    color: colors.primary,
  },
  dangerText: {
    color: 'white',
  },
  iconText: {
    color: colors.primary,
  },
  smallText: {
    fontSize: 14,
  },
  mediumText: {
    fontSize: 16,
  },
  largeText: {
    fontSize: 18,
  },
  // Icon styles
  leftIcon: {
    marginRight: 8,
  },
  rightIcon: {
    marginLeft: 8,
  },
  // Disabled state
  disabled: {
    backgroundColor: '#F2F2F7',
    borderColor: '#E5E5EA',
    opacity: 0.8,
  },
  disabledText: {
    color: '#8E8E93',
  },
  // Action button (specific style seen in clients/services)
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: 'rgba(52, 144, 243, 0.08)',
    borderRadius: 8,
  },
  actionButtonText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary,
  },
}); 