import { colors } from '@/constants/Colors';
import { useCustomPaymentInstructions, useOrganizationPaymentMethods } from '@/services/payment-method/payment-methods';
import { useInvoiceStore } from '@/stores/invoiceStore';
import React from 'react';
import { RowItem } from './RowItem';

interface PaymentMethodsProps {
  onPress: () => void;
}

export function PaymentMethods({ onPress }: PaymentMethodsProps) {
  // Fetch available options from organization config (what's possible to select)
  const { data: paymentMethodsConfig } = useOrganizationPaymentMethods();
  const { data: customInstructions = [] } = useCustomPaymentInstructions();
  
  // Get current invoice selections (what's actually selected for this invoice)
  const selectedGatewayId = useInvoiceStore((state) => state.paymentMethods.selectedGatewayId);
  const selectedCustomInstructionIds = useInvoiceStore((state) => state.paymentMethods.selectedCustomInstructionIds);
  const isManualSelected = useInvoiceStore((state) => state.paymentMethods.isManualSelected);
  const totalSelected = useInvoiceStore((state) => state.getTotalSelectedPaymentMethods());
  
  // Get available gateway configurations
  const configuredGateways = paymentMethodsConfig?.paymentGateways || [];
  
  // Build subtitle
  const buildSubtitle = () => {
    if (totalSelected === 0) {
      return 'No payment methods selected';
    }

    const parts: string[] = [];
    
    // Add selected gateway info
    if (selectedGatewayId) {
      const selectedGateway = paymentMethodsConfig?.paymentGateways.find(g => g.id === selectedGatewayId);
      if (selectedGateway) {
        parts.push(selectedGateway.type);
      }
    }
    
    // Add manual payment info
    if (isManualSelected && selectedCustomInstructionIds.length > 0) {
      parts.push(`Manual (${selectedCustomInstructionIds.length} instruction${selectedCustomInstructionIds.length === 1 ? '' : 's'})`);
    }

    return `${totalSelected} selected • ${parts.join(', ')}`;
  };

  return (
    <RowItem
      leftIcon="cash-outline"
      leftIconColor={colors.primary}
      title="Payment Methods"
      rightIcon="chevron-forward"
      subtitle={buildSubtitle()}
      onPress={onPress}
      showDivider={true}
      isCollapsed={false}
    />
  );
} 