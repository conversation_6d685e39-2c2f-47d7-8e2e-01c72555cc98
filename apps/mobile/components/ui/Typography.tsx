import { colors } from '@/constants/Colors';
import React, { ReactNode } from 'react';
import { StyleSheet, Text, TextStyle } from 'react-native';

type TypographyVariant = 
  | 'h1' 
  | 'h2' 
  | 'h3' 
  | 'h4' 
  | 'body' 
  | 'bodySmall' 
  | 'label' 
  | 'caption';

type TypographyColor = 
  | 'primary' 
  | 'secondary' 
  | 'tertiary' 
  | 'white' 
  | 'success' 
  | 'error';

interface TypographyProps {
  children: ReactNode;
  variant?: TypographyVariant;
  color?: TypographyColor;
  style?: TextStyle;
  numberOfLines?: number;
  ellipsizeMode?: 'head' | 'middle' | 'tail' | 'clip';
  center?: boolean;
  bold?: boolean;
}

export function Typography({
  children,
  variant = 'body',
  color = 'primary',
  style,
  numberOfLines,
  ellipsizeMode,
  center = false,
  bold = false,
}: TypographyProps) {
  // Safely create the color style key
  const colorStyleKey = `color${color.charAt(0).toUpperCase() + color.slice(1)}` as keyof typeof styles;
  
  return (
    <Text
      style={[
        styles[variant],
        styles[colorStyleKey],
        center && styles.center,
        bold && styles.bold,
        style
      ]}
      numberOfLines={numberOfLines}
      ellipsizeMode={ellipsizeMode}
    >
      {children}
    </Text>
  );
}

const styles = StyleSheet.create({
  // Variants
  h1: {
    fontSize: 28,
    fontWeight: '700',
    lineHeight: 34,
  },
  h2: {
    fontSize: 24,
    fontWeight: '700',
    lineHeight: 32,
  },
  h3: {
    fontSize: 20,
    fontWeight: '600',
    lineHeight: 28,
  },
  h4: {
    fontSize: 18,
    fontWeight: '600',
    lineHeight: 24,
  },
  body: {
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 22,
  },
  bodySmall: {
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },
  caption: {
    fontSize: 12,
    fontWeight: '400',
    lineHeight: 16,
  },
  // Colors
  colorPrimary: {
    color: colors.text.primary,
  },
  colorSecondary: {
    color: colors.text.secondary,
  },
  colorTertiary: {
    color: colors.text.tertiary,
  },
  colorWhite: {
    color: colors.text.white,
  },
  colorSuccess: {
    color: colors.success,
  },
  colorError: {
    color: colors.error,
  },
  // Modifiers
  center: {
    textAlign: 'center',
  },
  bold: {
    fontWeight: '700',
  },
}); 