import { colors } from '@/constants/Colors';
import { useOrganizationStore, type Organization } from '@/stores';
import { useActiveOrganization } from '@/stores';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
    Animated,
    Image,
    Modal,
    StyleSheet,
    Text,
    TouchableOpacity,
    TouchableWithoutFeedback,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface OrganizationSelectorProps {
  containerStyle?: object;
}

export function OrganizationSelector({ containerStyle }: OrganizationSelectorProps) {
  // Use clean selectors and selective subscriptions to prevent unnecessary re-renders
  const activeOrganization = useActiveOrganization();
  const organizations = useOrganizationStore(state => state.organizations);
  const setActiveOrganization = useOrganizationStore(state => state.setActiveOrganization);
  
  const [showCompanySelector, setShowCompanySelector] = useState(false);
  const sheetAnimationRef = useRef(new Animated.Value(0));
  
  // Memoize active company to prevent unnecessary re-renders
  const activeCompany = useMemo(() => 
    activeOrganization, 
    [activeOrganization]
  );
  
  // Function to handle organization selection with useCallback to prevent re-renders
  const handleSelectCompany = useCallback((organization: Organization) => {
    // Close the modal first, then update the organization to prevent conflicts
    Animated.timing(sheetAnimationRef.current, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      setShowCompanySelector(false);
      // Update organization after modal is closed
      setActiveOrganization(organization);
    });
  }, [setActiveOrganization]);
  
  // Function to open organization selector sheet
  const openCompanySheet = useCallback(() => {
    setShowCompanySelector(true);
    Animated.timing(sheetAnimationRef.current, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);
  
  // Function to close organization selector sheet
  const closeCompanySheet = useCallback(() => {
    Animated.timing(sheetAnimationRef.current, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      setShowCompanySelector(false);
    });
  }, []);
  
  // Memoize animation transforms to prevent recalculation
  const sheetTransform = useMemo(() => ({
    transform: [
      {
        translateY: sheetAnimationRef.current.interpolate({
          inputRange: [0, 1],
          outputRange: [600, 0],
        }),
      },
    ],
  }), []);
  
  const backdropOpacity = useMemo(() => 
    sheetAnimationRef.current.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 0.5],
    }), 
    []
  );
  
  // Memoized render function for company items
  const renderCompanyItem = useCallback((company: Organization) => {
    const isActive = company.id === activeOrganization?.id;
    
    return (
      <TouchableOpacity
        key={company.id}
        style={[styles.companyItem, isActive && styles.activeCompanyItem]}
        onPress={() => handleSelectCompany(company)}
      >
        <View style={styles.companyItemAvatar}>
          {company.logo ? (
            <Image source={{ uri: company.logo }} style={styles.companyItemAvatarImage} />
          ) : (
            <Text style={styles.companyItemAvatarText} adjustsFontSizeToFit numberOfLines={1}>
              {company.nickname}
            </Text>
          )}
        </View>
        <View style={styles.companyItemContent}>
          <View style={styles.companyItemInfo}>
            <Text style={styles.companyItemName}>{company.name}</Text>
            {isActive && (
              <View style={styles.companyItemBadge}>
                <Ionicons name="checkmark" size={14} color={colors.primary} />
              </View>
            )}
          </View>
          {isActive && (
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => handleEditOrganization(company)}
            >
              <Ionicons name="pencil" size={18} color={colors.text.secondary} />
            </TouchableOpacity>
          )}
        </View>
      </TouchableOpacity>
    );
  }, [activeOrganization?.id, handleSelectCompany]);

  const handleAddOrganization = useCallback(() => {
    closeCompanySheet();
    // Small delay to allow animation to complete before navigation
    setTimeout(() => {
      router.push('/create-organization');
    }, 200);
  }, [closeCompanySheet]);

  const handleEditOrganization = useCallback((organization: Organization) => {
    closeCompanySheet();
    // Small delay to allow animation to complete before navigation
    setTimeout(() => {
      router.push(`/create-organization?id=${organization.id}`);
    }, 200);
  }, [closeCompanySheet]);
  
  return (
    <>
      <TouchableOpacity 
        style={[styles.companySelector, containerStyle]}
        onPress={openCompanySheet}
      >
        <Text style={styles.companyAvatarText} adjustsFontSizeToFit numberOfLines={1}>
          {activeCompany?.nickname || 'ORG'}
        </Text>
        <Ionicons name="chevron-down" size={14} color={colors.text.white} style={styles.chevronIcon} />
      </TouchableOpacity>
      
      {/* Organization Selector Modal */}
      <Modal
        visible={showCompanySelector}
        transparent={true}
        animationType="none"
        onRequestClose={closeCompanySheet}
      >
        <View style={styles.modalContainer}>
          {/* Backdrop - closes the sheet when tapped */}
          <TouchableWithoutFeedback onPress={closeCompanySheet}>
            <Animated.View style={[styles.backdrop, { opacity: backdropOpacity }]} />
          </TouchableWithoutFeedback>
          
          {/* The sheet content */}
          <Animated.View style={[styles.sheetContainer, sheetTransform]}>
            <SafeAreaView edges={['bottom']}>
              <View style={styles.sheetHeader}>
                <Text style={styles.sheetTitle}>Select Organization</Text>
                <TouchableOpacity onPress={closeCompanySheet} style={styles.closeButton}>
                  <Ionicons name="close" size={24} color={colors.text.secondary} />
                </TouchableOpacity>
              </View>
              
              <View style={styles.companyList}>
                {/* Organization list */}
                {organizations.map(company => renderCompanyItem(company))}
              </View>
              
              <TouchableOpacity 
                style={styles.addCompanyButton}
                onPress={handleAddOrganization}
              >
                <Ionicons name="add-circle-outline" size={20} color={colors.primary} />
                <Text style={styles.addCompanyText}>Add New Organization</Text>
              </TouchableOpacity>
            </SafeAreaView>
          </Animated.View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  companySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 50,
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  companyAvatarText: {
    color: colors.text.white,
    fontWeight: '600',
    fontSize: 14,
    paddingHorizontal: 2,
    minWidth: 30,
    textAlign: 'center',
  },
  chevronIcon: {
    marginLeft: 6,
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  sheetContainer: {
    backgroundColor: colors.cardBackground,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 8,
    paddingHorizontal: 20,
    paddingBottom: 20,
    maxHeight: '70%',
  },
  sheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  sheetTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  closeButton: {
    padding: 4,
  },
  companyList: {
    marginTop: 10,
  },
  companyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  activeCompanyItem: {
    backgroundColor: 'rgba(52, 144, 243, 0.05)',
  },
  companyItemAvatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.avatarBackground.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  companyItemAvatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 22,
  },
  companyItemAvatarText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.primary,
    paddingHorizontal: 4,
    textAlign: 'center',
  },
  companyItemContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  companyItemInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  companyItemName: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.primary,
  },
  companyItemBadge: {
    backgroundColor: 'rgba(52, 144, 243, 0.08)',
    borderRadius: 12,
    paddingHorizontal: 4,
    paddingVertical: 2,
    marginLeft: 8,
  },
  sheetDivider: {
    height: 1,
    backgroundColor: colors.divider,
    marginVertical: 10,
  },
  addCompanyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    marginTop: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(52, 144, 243, 0.08)',
  },
  addCompanyText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
    color: colors.primary,
  },
  editButton: {
    padding: 4,
  },
}); 