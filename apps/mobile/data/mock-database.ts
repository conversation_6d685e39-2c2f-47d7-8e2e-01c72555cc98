import { Client, ClientActivity } from '../defs/client';
import { Invoice, InvoiceActivity } from '../defs/invoice';
import { Organization } from '../defs/organization';
import { Payment } from '../defs/payment';
import { CustomPaymentInstruction, OrganizationPaymentMethods } from '../defs/payment-method';
import { Service, ServiceActivity } from '../defs/service';
import { TaxOption } from '../defs/tax';

// Settings imports
import { AppDefaults, SupportedCurrency } from '../defs/app-defaults';
import { Feedback, FeedbackCategory } from '../defs/feedback';
import { InvoiceTemplate } from '../defs/invoice-template';
import { Subscription, SubscriptionPlan } from '../defs/subscription';
import { FAQ, HelpArticle, HelpCategory } from '../defs/support';
import { SecuritySettings, UserProfile } from '../defs/user';

export class MockDatabase {
  // Organizations
  static organizations: Organization[] = [
    {
      id: '1',
      name: 'Moonlight Design',
      nickname: 'MO<PERSON>',
      description: 'Creative design studio',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
        website: 'https://moonlightdesign.com',
        address: '123 Design St, Creative City, USA',
      },
      logo: null,
      isActive: true,
      settings: {
        currency: 'USD',
        dateFormat: 'MM/DD/YYYY',
        timezone: 'UTC',
        fiscalYearStart: '01/01',
        invoicePrefix: 'INV',
        defaultPaymentTerms: 'Net 30',
      },
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    },
    {
      id: '2',
      name: 'Stellar Web Services',
      nickname: 'SWS',
      description: 'Web development and SEO services',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
        website: 'https://stellarweb.com',
        address: '456 Web Ave, Tech City, USA',
      },
      logo: null,
      isActive: true,
      settings: {
        currency: 'USD',
        dateFormat: 'MM/DD/YYYY',
        timezone: 'UTC',
        fiscalYearStart: '01/01',
        invoicePrefix: 'INV',
        defaultPaymentTerms: 'Net 30',
      },
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    },
    {
      id: '3',
      name: 'Green Tech Solutions',
      nickname: 'GTS',
      description: 'Sustainable technology consulting',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
        website: 'https://greentech.com',
        address: '789 Green Blvd, Eco City, USA',
      },
      logo: null,
      isActive: true,
      settings: {
        currency: 'USD',
        dateFormat: 'MM/DD/YYYY',
        timezone: 'UTC',
        fiscalYearStart: '01/01',
        invoicePrefix: 'INV',
        defaultPaymentTerms: 'Net 30',
      },
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    }
  ];

  // Custom Payment Instructions
  static customPaymentInstructions: CustomPaymentInstruction[] = [
    // Organization 1 - Moonlight Design
    {
      id: 'cpi_001',
      organizationId: '1',
      title: 'Bank Wire Transfer',
      instructions: 'Please send wire transfer to:\n\nBank: First National Bank\nAccount Name: Moonlight Design LLC\nAccount Number: *********0\nRouting Number: *********\nSWIFT Code: FNBKUS33\n\nPlease include invoice number in the reference field.',
      isActive: true,
      sortOrder: 1,
      createdAt: new Date('2023-02-15'),
      updatedAt: new Date('2023-02-15'),
    },
    {
      id: 'cpi_002',
      organizationId: '1',
      title: 'Business Check',
      instructions: 'Make checks payable to: Moonlight Design LLC\n\nMail to:\n123 Design St\nCreative City, USA 12345\n\nPlease write the invoice number in the memo line for faster processing.',
      isActive: true,
      sortOrder: 2,
      createdAt: new Date('2023-03-01'),
      updatedAt: new Date('2023-03-01'),
    },
    // Organization 2 - Stellar Web Services
    {
      id: 'cpi_003',
      organizationId: '2',
      title: 'ACH Bank Transfer',
      instructions: 'Direct deposit information:\n\nBank: Tech Community Bank\nAccount Name: Stellar Web Services Inc\nAccount Number: **********\nRouting Number: *********\n\nPlease allow 2-3 business days for processing.',
      isActive: true,
      sortOrder: 1,
      createdAt: new Date('2023-02-20'),
      updatedAt: new Date('2023-02-20'),
    },
    {
      id: 'cpi_004',
      organizationId: '2',
      title: 'Cryptocurrency',
      instructions: 'We accept the following cryptocurrencies:\n\nBitcoin (BTC): ******************************************\nEthereum (ETH): ******************************************\n\nPlease contact us before sending payment to confirm current rates and addresses.',
      isActive: true,
      sortOrder: 3,
      createdAt: new Date('2023-04-10'),
      updatedAt: new Date('2023-04-10'),
    },
  ];

  // Organization Payment Methods Configuration
  static organizationPaymentMethods: OrganizationPaymentMethods[] = [
    // Organization 1 - Moonlight Design (PayPal configured but not active, Stripe not connected)
    {
      organizationId: '1',
      activeGateway: undefined, // No gateway active initially
      paymentGateways: [
        {
          id: 'gateway_1_paypal',
          type: 'paypal',
          status: 'configured',
          accountId: 'PAYPAL987654321',
          accountEmail: '<EMAIL>',
          displayName: 'Moonlight Design PayPal',
          configuredAt: new Date('2023-03-15'),
        }
        // Stripe not in array = not configured
      ],
      customInstructions: ['cpi_001', 'cpi_002'],
      allowPartialPayments: true,
      autoCreatePaymentLinks: true,
      updatedAt: new Date('2023-06-15'),
    },
    // Organization 2 - Stellar Web Services (PayPal active)
    {
      organizationId: '2',
      activeGateway: 'gateway_2_paypal',
      paymentGateways: [
        {
          id: 'gateway_2_paypal',
          type: 'paypal',
          status: 'active',
          accountId: 'PAYPAL*********',
          accountEmail: '<EMAIL>',
          displayName: 'Stellar Web Business Account',
          configuredAt: new Date('2023-01-20'),
          lastUsed: new Date('2023-05-30'),
        }
      ],
      customInstructions: ['cpi_003', 'cpi_004'],
      allowPartialPayments: false,
      autoCreatePaymentLinks: true,
      updatedAt: new Date('2023-05-30'),
    },
    // Organization 3 - Green Tech Solutions (No gateways configured yet)
    {
      organizationId: '3',
      activeGateway: undefined,
      paymentGateways: [],
      customInstructions: [],
      allowPartialPayments: true,
      autoCreatePaymentLinks: false,
      updatedAt: new Date('2023-01-01'),
    },
  ];

  // Clients with full data
  static clients: Client[] = [
    // Organization 1 - Moonlight Design clients
    {
      id: '1',
      name: 'Acme Inc.',
      displayName: 'Acme Inc.',
      company: 'Acme Corporation',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
      },
      address: {
        fullAddress: '123 Main St, Anytown, USA',
      },
      photo: 'https://images.unsplash.com/photo-1560179707-f14e90ef3623?w=100&h=100&fit=crop&crop=face',
      organizationId: '1',
      isActive: true,
      defaultTaxExempt: false,
      createdAt: new Date('2023-01-15'),
      updatedAt: new Date('2023-01-15'),
    },
    {
      id: '2',
      name: 'Globex Corporation',
      displayName: 'Globex Corporation',
      company: 'Globex Corp',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
      },
      address: {
        fullAddress: '456 Business Ave, Commerce City, USA',
      },
      photo: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=100&h=100&fit=crop&crop=face',
      organizationId: '1',
      isActive: true,
      defaultTaxExempt: false,
      createdAt: new Date('2023-02-22'),
      updatedAt: new Date('2023-02-22'),
    },
    {
      id: '3',
      name: 'TechCorp',
      displayName: 'TechCorp',
      company: 'TechCorp Ltd',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
      },
      address: {
        fullAddress: '789 Innovation Dr, Silicon Valley, USA',
      },
      organizationId: '1',
      isActive: true,
      defaultTaxExempt: false,
      createdAt: new Date('2023-03-10'),
      updatedAt: new Date('2023-03-10'),
    },
    {
      id: '4',
      name: 'Initech',
      displayName: 'Initech',
      company: 'Initech Solutions',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
      },
      address: {
        fullAddress: '321 Corporate Blvd, Metro City, USA',
      },
      organizationId: '1',
      isActive: true,
      defaultTaxExempt: false,
      createdAt: new Date('2023-04-05'),
      updatedAt: new Date('2023-04-05'),
    },
    {
      id: '5',
      name: 'Massive Dynamic',
      displayName: 'Massive Dynamic',
      company: 'Massive Dynamic Inc',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
      },
      address: {
        fullAddress: '555 Science Parkway, New York, USA',
      },
      organizationId: '1',
      isActive: true,
      defaultTaxExempt: false,
      createdAt: new Date('2023-04-28'),
      updatedAt: new Date('2023-04-28'),
    },
    {
      id: '6',
      name: 'DevCorp',
      displayName: 'DevCorp',
      company: 'DevCorp Technologies',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
      },
      address: {
        fullAddress: '987 Developer Lane, Tech City, USA',
      },
      organizationId: '1',
      isActive: true,
      defaultTaxExempt: false,
      createdAt: new Date('2023-05-15'),
      updatedAt: new Date('2023-05-15'),
    },
    // Organization 2 - Stellar Web Services clients  
    {
      id: '7',
      name: 'Stark Industries',
      displayName: 'Stark Industries',
      company: 'Stark Industries Inc',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
      },
      address: {
        fullAddress: '789 Tech Blvd, Innovation Valley, USA',
      },
      organizationId: '2',
      isActive: true,
      defaultTaxExempt: false,
      createdAt: new Date('2023-03-10'),
      updatedAt: new Date('2023-03-10'),
    },
    {
      id: '8',
      name: 'Wayne Enterprises',
      displayName: 'Wayne Enterprises',
      company: 'Wayne Enterprises LLC',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
      },
      address: {
        fullAddress: '101 Gotham Rd, Gotham City, USA',
      },
      organizationId: '2',
      isActive: true,
      defaultTaxExempt: false,
      createdAt: new Date('2023-04-05'),
      updatedAt: new Date('2023-04-05'),
    },
    {
      id: '9',
      name: 'International Business Solutions and Technology Consulting Corporation',
      displayName: 'IBSTC Corporation',
      company: 'International Business Solutions and Technology Consulting Corporation Ltd',
      contact: {
        email: '<EMAIL>',
        phone: '(*************',
      },
      address: {
        fullAddress: '2000 Corporate Complex Drive, International Business District, New York, USA',
      },
      organizationId: '1',
      isActive: true,
      defaultTaxExempt: false,
      createdAt: new Date('2023-06-01'),
      updatedAt: new Date('2023-06-01'),
    },
  ];

  // Services with full data
  static services: Service[] = [
    // Organization 1 - Moonlight Design services
    {
      id: 'S001',
      name: 'Logo Design',
      description: 'Professional logo design with unlimited revisions',
      pricing: {
        rate: 350.00,
        unit: 'fixed',
        currency: 'USD',
      },
      organizationId: '1',
      isActive: true,
      taxable: true,
      tags: ['design', 'branding', 'logo', 'creative'],
      createdAt: new Date('2023-01-15'),
      updatedAt: new Date('2023-01-15'),
    },
    {
      id: 'S002',
      name: 'Website Design',
      description: 'Custom website design with responsive layouts',
      pricing: {
        rate: 85.00,
        unit: 'hour',
        currency: 'USD',
      },
      organizationId: '1',
      isActive: true,
      taxable: true,
      tags: ['web', 'design', 'responsive', 'ui/ux'],
      createdAt: new Date('2023-02-10'),
      updatedAt: new Date('2023-02-10'),
    },
    {
      id: 'S003',
      name: 'Brand Identity Package',
      description: 'Complete brand identity including logo, colors, and style guide',
      pricing: {
        rate: 1200.00,
        unit: 'project',
        currency: 'USD',
      },
      organizationId: '1',
      isActive: true,
      taxable: true,
      tags: ['branding', 'identity', 'package', 'premium'],
      createdAt: new Date('2023-03-22'),
      updatedAt: new Date('2023-03-22'),
    },
    {
      id: 'S007',
      name: 'Social Media Graphics',
      description: 'Customized graphics package for social media platforms',
      pricing: {
        rate: 120.00,
        unit: 'day',
        currency: 'EUR',
      },
      organizationId: '1',
      isActive: false,
      taxable: true,
      tags: ['social-media', 'graphics', 'marketing'],
      createdAt: new Date('2023-04-05'),
      updatedAt: new Date('2023-04-05'),
    },
    {
      id: 'S008',
      name: 'UI/UX Design',
      description: 'User interface and experience design for web and mobile apps',
      pricing: {
        rate: 95.00,
        unit: 'hour',
        currency: 'USD',
      },
      organizationId: '1',
      isActive: true,
      taxable: true,
      tags: ['ui', 'ux', 'mobile', 'web', 'user-experience'],
      createdAt: new Date('2023-04-18'),
      updatedAt: new Date('2023-04-18'),
    },
    {
      id: 'S009',
      name: 'Print Design',
      description: 'Design for business cards, brochures, flyers, and other print materials',
      pricing: {
        rate: 75.00,
        unit: 'hour',
        currency: 'GBP',
      },
      organizationId: '1',
      isActive: false,
      taxable: true,
      tags: ['print', 'design', 'marketing-materials'],
      createdAt: new Date('2023-05-12'),
      updatedAt: new Date('2023-05-12'),
    },
    // Organization 2 - Stellar Web Services
    {
      id: 'S004',
      name: 'Web Development',
      description: 'Full-stack web development services',
      pricing: {
        rate: 95.00,
        unit: 'hour',
        currency: 'USD',
      },
      organizationId: '2',
      isActive: true,
      taxable: true,
      tags: ['development', 'full-stack', 'web', 'programming'],
      createdAt: new Date('2023-01-20'),
      updatedAt: new Date('2023-01-20'),
    },
    {
      id: 'S005',
      name: 'SEO Optimization',
      description: 'Search engine optimization for existing websites',
      pricing: {
        rate: 450.00,
        unit: 'day',
        currency: 'CAD',
      },
      organizationId: '2',
      isActive: true,
      taxable: true,
      tags: ['seo', 'optimization', 'marketing', 'analytics'],
      createdAt: new Date('2023-02-15'),
      updatedAt: new Date('2023-02-15'),
    },
    {
      id: 'S006',
      name: 'Website Maintenance',
      description: 'Monthly website maintenance and updates',
      pricing: {
        rate: 250.00,
        unit: 'month',
        currency: 'USD',
      },
      organizationId: '2',
      isActive: true,
      taxable: true,
      tags: ['maintenance', 'support', 'updates', 'hosting'],
      createdAt: new Date('2023-04-05'),
      updatedAt: new Date('2023-04-05'),
    },
  ];

  // Service Activities
  static serviceActivities: ServiceActivity[] = [
    // S001 - Logo Design activities
    {
      id: 'SA001',
      serviceId: 'S001',
      organizationId: '1',
      type: 'created',
      description: 'Service created',
      createdAt: new Date('2023-01-15'),
    },
    {
      id: 'SA002',
      serviceId: 'S001',
      organizationId: '1',
      type: 'used_in_invoice',
      description: 'Used in invoice INV-001 for Acme Inc.',
      metadata: {
        invoiceId: 'INV-001',
        invoiceNumber: 'INV-001',
        clientName: 'Acme Inc.',
        amount: 350,
      },
      createdAt: new Date('2023-05-15'),
    },
    // S002 - Website Design activities
    {
      id: 'SA003',
      serviceId: 'S002',
      organizationId: '1',
      type: 'created',
      description: 'Service created',
      createdAt: new Date('2023-02-10'),
    },
    {
      id: 'SA004',
      serviceId: 'S002',
      organizationId: '1',
      type: 'pricing_changed',
      description: 'Pricing updated from $75/hr to $85/hr',
      metadata: {
        field: 'pricing.rate',
        oldValue: 75,
        newValue: 85,
      },
      createdAt: new Date('2023-03-15'),
    },
    {
      id: 'SA005',
      serviceId: 'S002',
      organizationId: '1',
      type: 'used_in_invoice',
      description: 'Used in invoice INV-002 for Globex Corporation (10 hours)',
      metadata: {
        invoiceId: 'INV-002',
        invoiceNumber: 'INV-002',
        clientName: 'Globex Corporation',
        amount: 850,
      },
      createdAt: new Date('2023-06-01'),
    },
    // S003 - Brand Identity Package activities
    {
      id: 'SA006',
      serviceId: 'S003',
      organizationId: '1',
      type: 'created',
      description: 'Service created',
      createdAt: new Date('2023-03-22'),
    },
    {
      id: 'SA007',
      serviceId: 'S003',
      organizationId: '1',
      type: 'used_in_invoice',
      description: 'Used in invoice INV-2024-001 for IBSTC Corporation',
      metadata: {
        invoiceId: 'INV-003',
        invoiceNumber: 'INV-2024-001',
        clientName: 'International Business Solutions and Technology Consulting Corporation',
        amount: 1200,
      },
      createdAt: new Date('2024-01-15'),
    },
    // S007 - Social Media Graphics activities (inactive service)
    {
      id: 'SA008',
      serviceId: 'S007',
      organizationId: '1',
      type: 'created',
      description: 'Service created',
      createdAt: new Date('2023-04-05'),
    },
    {
      id: 'SA009',
      serviceId: 'S007',
      organizationId: '1',
      type: 'deactivated',
      description: 'Service deactivated - low demand',
      createdAt: new Date('2023-08-12'),
    },
    // S008 - UI/UX Design activities
    {
      id: 'SA010',
      serviceId: 'S008',
      organizationId: '1',
      type: 'created',
      description: 'Service created',
      createdAt: new Date('2023-04-18'),
    },
    {
      id: 'SA011',
      serviceId: 'S008',
      organizationId: '1',
      type: 'updated',
      description: 'Service description updated to include mobile app design',
      metadata: {
        field: 'description',
        oldValue: 'User interface and experience design for web apps',
        newValue: 'User interface and experience design for web and mobile apps',
      },
      createdAt: new Date('2023-09-22'),
    },
    // S009 - Print Design activities (inactive service)
    {
      id: 'SA012',
      serviceId: 'S009',
      organizationId: '1',
      type: 'created',
      description: 'Service created',
      createdAt: new Date('2023-05-12'),
    },
    {
      id: 'SA013',
      serviceId: 'S009',
      organizationId: '1',
      type: 'deactivated',
      description: 'Service deactivated - focus shift to digital services',
      createdAt: new Date('2023-10-05'),
    },
    // Organization 2 - Stellar Web Services activities
    // S004 - Web Development activities
    {
      id: 'SA014',
      serviceId: 'S004',
      organizationId: '2',
      type: 'created',
      description: 'Service created',
      createdAt: new Date('2023-01-20'),
    },
    {
      id: 'SA015',
      serviceId: 'S004',
      organizationId: '2',
      type: 'used_in_invoice',
      description: 'Used in invoice INV-021 for Wayne Enterprises (20 hours)',
      metadata: {
        invoiceId: 'INV-021',
        invoiceNumber: 'INV-021',
        clientName: 'Wayne Enterprises',
        amount: 1900,
      },
      createdAt: new Date('2023-04-10T09:15:00Z'),
    },
    // S005 - SEO Optimization activities
    {
      id: 'SA016',
      serviceId: 'S005',
      organizationId: '2',
      type: 'created',
      description: 'Service created',
      createdAt: new Date('2023-02-15'),
    },
    // S006 - Website Maintenance activities
    {
      id: 'SA017',
      serviceId: 'S006',
      organizationId: '2',
      type: 'created',
      description: 'Service created',
      createdAt: new Date('2023-04-05'),
    },
  ];

  // Client Activities
  static clientActivities: ClientActivity[] = [
    // Client 1 - Acme Inc. activities
    {
      id: 'CA001',
      clientId: '1',
      organizationId: '1',
      type: 'created',
      description: 'Client added to organization',
      createdAt: new Date('2023-01-15'),
    },
    {
      id: 'CA002',
      clientId: '1',
      organizationId: '1',
      type: 'used_in_invoice',
      description: 'Appeared in invoice INV-001',
      metadata: {
        invoiceId: 'INV-001',
        invoiceNumber: 'INV-001',
        amount: 591.25,
        status: 'paid',
      },
      createdAt: new Date('2023-05-15'),
    },
    // Client 2 - Globex Corporation activities
    {
      id: 'CA003',
      clientId: '2',
      organizationId: '1',
      type: 'created',
      description: 'Client added to organization',
      createdAt: new Date('2023-02-22'),
    },
    {
      id: 'CA004',
      clientId: '2',
      organizationId: '1',
      type: 'used_in_invoice',
      description: 'Appeared in invoice INV-002',
      metadata: {
        invoiceId: 'INV-002',
        invoiceNumber: 'INV-002',
        amount: 913.75,
        status: 'pending',
      },
      createdAt: new Date('2023-06-01'),
    },
    // Client 3 - TechCorp activities
    {
      id: 'CA005',
      clientId: '3',
      organizationId: '1',
      type: 'created',
      description: 'Client added to organization',
      createdAt: new Date('2023-03-10'),
    },
    // Client 8 - Wayne Enterprises activities (Org 2)
    {
      id: 'CA006',
      clientId: '8',
      organizationId: '2',
      type: 'created',
      description: 'Client added to organization',
      createdAt: new Date('2023-04-05'),
    },
    {
      id: 'CA007',
      clientId: '8',
      organizationId: '2',
      type: 'used_in_invoice',
      description: 'Appeared in invoice INV-021',
      metadata: {
        invoiceId: 'INV-021',
        invoiceNumber: 'INV-021',
        amount: 2061.50,
        status: 'overdue',
      },
      createdAt: new Date('2023-04-10'),
    },
    // Client 9 - IBSTC Corporation activities
    {
      id: 'CA008',
      clientId: '9',
      organizationId: '1',
      type: 'created',
      description: 'Client added to organization',
      createdAt: new Date('2023-06-01'),
    },
    {
      id: 'CA009',
      clientId: '9',
      organizationId: '1',
      type: 'contact_updated',
      description: 'Contact information updated',
      metadata: {
        field: 'contact.email',
        oldValue: '<EMAIL>',
        newValue: '<EMAIL>',
      },
      createdAt: new Date('2023-08-15'),
    },
    {
      id: 'CA010',
      clientId: '9',
      organizationId: '1',
      type: 'used_in_invoice',
      description: 'Appeared in invoice INV-2024-001',
      metadata: {
        invoiceId: 'INV-003',
        invoiceNumber: 'INV-2024-001',
        amount: 1290.00,
        status: 'pending',
      },
      createdAt: new Date('2024-01-15'),
    },
    // Additional activities for other clients
    {
      id: 'CA011',
      clientId: '4',
      organizationId: '1',
      type: 'created',
      description: 'Client added to organization',
      createdAt: new Date('2023-04-05'),
    },
    {
      id: 'CA012',
      clientId: '5',
      organizationId: '1',
      type: 'created',
      description: 'Client added to organization',
      createdAt: new Date('2023-04-28'),
    },
    {
      id: 'CA013',
      clientId: '6',
      organizationId: '1',
      type: 'created',
      description: 'Client added to organization',
      createdAt: new Date('2023-05-15'),
    },
    {
      id: 'CA014',
      clientId: '7',
      organizationId: '2',
      type: 'created',
      description: 'Client added to organization',
      createdAt: new Date('2023-03-10'),
    },
  ];

  // Complete invoices with line items and relationships
  static invoices: Invoice[] = [
    // Organization 1 - Moonlight Design invoices
    {
      id: 'INV-001',
      invoiceNumber: 'INV-001',
      status: 'paid',
      issueDate: new Date('2023-05-15'),
      dueDate: new Date('2023-06-15'),
      organizationId: '1',
      clientId: '1',
      clientName: 'Acme Inc.',
      lineItems: [
        {
          id: '1',
          description: 'Logo Design',
          itemDescription: 'Professional logo design with unlimited revisions',
          quantity: 1,
          unitPrice: 350,
          unit: 'fixed',
          discountType: 'percentage',
          total: 350,
          taxable: true,
          taxRate: 7.5,
          serviceId: 'S001',
        },
        {
          id: '2',
          description: 'Brand Guidelines',
          itemDescription: 'Complete brand style guide documentation',
          quantity: 1,
          unitPrice: 200,
          unit: 'fixed',
          discountType: 'percentage',
          total: 200,
          taxable: true,
          taxRate: 7.5,
        }
      ],
      taxInfo: {
        taxName: 'Sales Tax',
        defaultRate: 7.5,
        inclusive: false,
        enabled: true
      },
      payment: {
        dueDate: new Date('2023-06-15'),
        terms: 'Net 30',
      },
      totals: {
        subtotal: 550,
        discountTotal: 0,
        taxTotal: 41.25,
        total: 591.25,
        amountPaid: 541.25,
        amountDue: 0,
      },
      notes: 'Thank you for your business!',
      terms: 'Payment due within 30 days.',
      attachments: [
        {
          id: 'att_001',
          name: 'logo-design-brief.pdf',
          size: 1245680,
          type: 'application/pdf',
          uri: 'file:///mock/path/logo-design-brief.pdf',
          uploadedAt: new Date('2023-05-15T09:15:00Z'),
        },
        {
          id: 'att_002',
          name: 'brand-inspiration.jpg',
          size: 2048000,
          type: 'image/jpeg',
          uri: 'file:///mock/path/brand-inspiration.jpg',
          uploadedAt: new Date('2023-05-15T09:20:00Z'),
        },
      ],
      comments: [
        {
          id: 'IC001',
          invoiceId: 'INV-001',
          organizationId: '1',
          text: 'Client requested rush delivery on logo design. Completed ahead of schedule.',
          type: 'internal' as const,
          author: 'admin',
          createdAt: new Date('2023-05-16T10:30:00Z'),
          updatedAt: new Date('2023-05-16T10:30:00Z'),
        },
        {
          id: 'IC002',
          invoiceId: 'INV-001',
          organizationId: '1',
          text: 'Payment received via wire transfer. Transaction processed successfully.',
          type: 'internal' as const,
          author: 'admin',
          createdAt: new Date('2023-06-15T12:00:00Z'),
          updatedAt: new Date('2023-06-15T12:00:00Z'),
        },
      ],
      createdAt: new Date('2023-05-15'),
      updatedAt: new Date('2023-06-15'),
      // Legacy fields for compatibility
      amount: '$591.25',
      date: new Date('2023-05-15'),
    },
    {
      id: 'INV-002',
      invoiceNumber: 'INV-002',
      status: 'pending',
      issueDate: new Date('2023-06-01'),
      dueDate: new Date('2023-06-18'),
      organizationId: '1',
      clientId: '2',
      clientName: 'Globex Corporation',
      lineItems: [
        {
          id: '3',
          description: 'Website Design',
          itemDescription: 'Custom website design with responsive layouts',
          quantity: 10,
          unitPrice: 85,
          unit: 'hour',
          discountType: 'percentage',
          total: 850,
          taxable: true,
          taxRate: 7.5,
          serviceId: 'S002',
        }
      ],
      taxInfo: {
        taxName: 'Sales Tax',
        defaultRate: 7.5,
        inclusive: false,
        enabled: true
      },
      payment: {
        dueDate: new Date('2023-06-18'),
        terms: 'Net 30',
      },
      totals: {
        subtotal: 850,
        discountTotal: 0,
        taxTotal: 63.75,
        total: 913.75,
        amountPaid: 700.00,
        amountDue: 213.75,
      },
      notes: '',
      terms: 'Payment due within 30 days.',
      attachments: [
        {
          id: 'att_003',
          name: 'website-wireframes.sketch',
          size: 8924500,
          type: 'application/octet-stream',
          uri: 'file:///mock/path/website-wireframes.sketch',
          uploadedAt: new Date('2023-06-01T15:45:00Z'),
        },
        {
          id: 'att_004',
          name: 'responsive-design-specs.docx',
          size: 567200,
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          uri: 'file:///mock/path/responsive-design-specs.docx',
          uploadedAt: new Date('2023-06-01T16:00:00Z'),
        },
        {
          id: 'att_005',
          name: 'color-palette.png',
          size: 345600,
          type: 'image/png',
          uri: 'file:///mock/path/color-palette.png',
          uploadedAt: new Date('2023-06-01T16:10:00Z'),
        },
      ],
      comments: [
        {
          id: 'IC003',
          invoiceId: 'INV-002',
          organizationId: '1',
          text: 'Client is very satisfied with the responsive design. Requested additional mobile optimizations.',
          type: 'internal' as const,
          author: 'admin',
          createdAt: new Date('2023-06-03T14:20:00Z'),
          updatedAt: new Date('2023-06-03T14:20:00Z'),
        },
        {
          id: 'IC004',
          invoiceId: 'INV-002',
          organizationId: '1',
          text: 'Follow up needed - client mentioned potential payment delay due to budget approval process.',
          type: 'internal' as const,
          author: 'admin',
          createdAt: new Date('2023-06-20T09:15:00Z'),
          updatedAt: new Date('2023-06-20T09:15:00Z'),
        },
      ],
      createdAt: new Date('2023-06-01'),
      updatedAt: new Date('2023-06-01'),
      // Legacy fields
      amount: '$913.75',
      date: new Date('2023-06-01'),
    },
    // Organization 2 - Stellar Web Services invoices
    {
      id: 'INV-021',
      invoiceNumber: 'INV-021',
      status: 'overdue',
      issueDate: new Date('2023-04-10'),
      dueDate: new Date('2023-05-10'),
      organizationId: '2',
      clientId: '8',
      clientName: 'Wayne Enterprises',
      lineItems: [
        {
          id: '4',
          description: 'Web Development',
          itemDescription: 'Full-stack web development services',
          quantity: 20,
          unitPrice: 95,
          unit: 'hour',
          discountType: 'percentage',
          total: 1900,
          taxable: true,
          taxRate: 8.5,
          serviceId: 'S004',
        }
      ],
      taxInfo: {
        taxName: 'Sales Tax',
        defaultRate: 8.5,
        inclusive: false,
        enabled: true
      },
      payment: {
        dueDate: new Date('2023-05-10'),
        terms: 'Net 30',
      },
      totals: {
        subtotal: 1900,
        discountTotal: 0,
        taxTotal: 161.50,
        total: 2086.50,
        amountPaid: 0,
        amountDue: 2086.50,
      },
      notes: '',
      terms: 'Payment due within 30 days.',
      attachments: [
        {
          id: 'att_006',
          name: 'web-development-brief.pdf',
          size: 1245680,
          type: 'application/pdf',
          uri: 'file:///mock/path/web-development-brief.pdf',
          uploadedAt: new Date('2023-04-10T09:15:00Z'),
        },
        {
          id: 'att_007',
          name: 'responsive-design-specs.docx',
          size: 567200,
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          uri: 'file:///mock/path/responsive-design-specs.docx',
          uploadedAt: new Date('2023-04-10T09:20:00Z'),
        },
      ],
      comments: [
        {
          id: 'IC005',
          invoiceId: 'INV-021',
          organizationId: '2',
          text: 'Client requested additional features during development. Scope creep documented.',
          type: 'internal' as const,
          author: 'admin',
          createdAt: new Date('2023-04-15T11:45:00Z'),
          updatedAt: new Date('2023-04-15T11:45:00Z'),
        },
        {
          id: 'IC006',
          invoiceId: 'INV-021',
          organizationId: '2',
          text: 'URGENT: Invoice overdue. Client has been unresponsive to payment reminders.',
          type: 'internal' as const,
          author: 'admin',
          createdAt: new Date('2023-05-20T16:30:00Z'),
          updatedAt: new Date('2023-05-20T16:30:00Z'),
        },
      ],
      createdAt: new Date('2023-04-10'),
      updatedAt: new Date('2023-04-10'),
      // Legacy fields
      amount: '$2,086.50',
      date: new Date('2023-04-10'),
    },
    {
      id: 'INV-003',
      invoiceNumber: 'INV-2024-001',
      status: 'pending',
      issueDate: new Date('2024-01-15'),
      dueDate: new Date('2024-02-14'),
      organizationId: '1',
      clientId: '9',
      clientName: 'International Business Solutions and Technology Consulting Corporation',
      lineItems: [
        {
          id: '5',
          description: 'Brand Identity Package',
          itemDescription: 'Complete brand identity including logo, colors, and style guide',
          quantity: 1,
          unitPrice: 1200,
          unit: 'project',
          discountType: 'percentage',
          total: 1200,
          taxable: true,
          taxRate: 7.5,
          serviceId: 'S003',
        }
      ],
      taxInfo: {
        taxName: 'Sales Tax',
        defaultRate: 7.5,
        inclusive: false,
        enabled: true
      },
      payment: {
        dueDate: new Date('2024-02-14'),
        terms: 'Net 30',
      },
      totals: {
        subtotal: 1200,
        discountTotal: 0,
        taxTotal: 90,
        total: 1290,
        amountPaid: 645.00,
        amountDue: 645.00,
      },
      notes: 'Thank you for choosing our premium brand identity package.',
      terms: 'Payment due within 30 days.',
      attachments: [
        {
          id: 'att_008',
          name: 'brand-identity-package.pdf',
          size: 1245680,
          type: 'application/pdf',
          uri: 'file:///mock/path/brand-identity-package.pdf',
          uploadedAt: new Date('2024-01-15T11:00:00Z'),
        },
        {
          id: 'att_009',
          name: 'color-palette.png',
          size: 345600,
          type: 'image/png',
          uri: 'file:///mock/path/color-palette.png',
          uploadedAt: new Date('2024-01-15T11:10:00Z'),
        },
      ],
      comments: [
        {
          id: 'IC007',
          invoiceId: 'INV-003',
          organizationId: '1',
          text: 'Premium client - excellent communication throughout the branding project.',
          type: 'internal' as const,
          author: 'admin',
          createdAt: new Date('2024-01-16T13:20:00Z'),
          updatedAt: new Date('2024-01-16T13:20:00Z'),
        },
        {
          id: 'IC008',
          invoiceId: 'INV-003',
          organizationId: '1',
          text: 'Client approved final brand identity package. Very pleased with the deliverables.',
          type: 'internal' as const,
          author: 'admin',
          createdAt: new Date('2024-01-22T10:45:00Z'),
          updatedAt: new Date('2024-01-22T10:45:00Z'),
        },
      ],
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15'),
      // Legacy fields
      amount: '$1,290.00',
      date: new Date('2024-01-15'),
    },
  ];

  // Invoice Activities
  static invoiceActivities: InvoiceActivity[] = [
    // INV-001 - Acme Inc. activities (paid invoice)
    {
      id: 'IA001',
      invoiceId: 'INV-001',
      organizationId: '1',
      type: 'created',
      description: 'Invoice created',
      createdAt: new Date('2023-05-15T09:00:00Z'),
      updatedAt: new Date('2023-05-15T09:00:00Z'),
    },
    {
      id: 'IA002',
      invoiceId: 'INV-001',
      organizationId: '1',
      type: 'sent',
      description: 'Invoice sent to client via email',
      metadata: {
        method: 'email',
        recipientEmail: '<EMAIL>',
      },
      createdAt: new Date('2023-05-15T10:30:00Z'),
      updatedAt: new Date('2023-05-15T10:30:00Z'),
    },
    {
      id: 'IA003',
      invoiceId: 'INV-001',
      organizationId: '1',
      type: 'viewed',
      description: 'Invoice viewed by client',
      metadata: {
        viewerIP: '*************',
        userAgent: 'Mozilla/5.0...',
      },
      createdAt: new Date('2023-05-16T14:22:00Z'),
      updatedAt: new Date('2023-05-16T14:22:00Z'),
    },
    {
      id: 'IA004',
      invoiceId: 'INV-001',
      organizationId: '1',
      type: 'paid',
      description: 'Payment received - Bank transfer',
      metadata: {
        paymentMethod: 'bank_transfer',
        amount: 591.25,
        transactionId: 'TXN-********-001',
        reference: 'INV-001 Payment',
      },
      createdAt: new Date('2023-06-15T11:45:00Z'),
      updatedAt: new Date('2023-06-15T11:45:00Z'),
    },
    {
      id: 'IA005',
      invoiceId: 'INV-001',
      organizationId: '1',
      type: 'status_changed',
      description: 'Status changed from pending to paid',
      metadata: {
        oldStatus: 'pending',
        newStatus: 'paid',
      },
      createdAt: new Date('2023-06-15T11:45:30Z'),
      updatedAt: new Date('2023-06-15T11:45:30Z'),
    },

    // INV-002 - Globex Corporation activities (pending invoice)
    {
      id: 'IA006',
      invoiceId: 'INV-002',
      organizationId: '1',
      type: 'created',
      description: 'Invoice created',
      createdAt: new Date('2023-06-01T15:30:00Z'),
      updatedAt: new Date('2023-06-01T15:30:00Z'),
    },
    {
      id: 'IA007',
      invoiceId: 'INV-002',
      organizationId: '1',
      type: 'sent',
      description: 'Invoice sent to client via email',
      metadata: {
        method: 'email',
        recipientEmail: '<EMAIL>',
      },
      createdAt: new Date('2023-06-01T16:15:00Z'),
      updatedAt: new Date('2023-06-01T16:15:00Z'),
    },
    {
      id: 'IA008',
      invoiceId: 'INV-002',
      organizationId: '1',
      type: 'viewed',
      description: 'Invoice viewed by client',
      metadata: {
        viewerIP: '*********',
        userAgent: 'Mozilla/5.0...',
      },
      createdAt: new Date('2023-06-02T09:12:00Z'),
      updatedAt: new Date('2023-06-02T09:12:00Z'),
    },
    {
      id: 'IA009',
      invoiceId: 'INV-002',
      organizationId: '1',
      type: 'reminder_sent',
      description: 'Payment reminder sent to client',
      metadata: {
        reminderType: 'gentle',
        method: 'email',
        daysOverdue: 10,
      },
      createdAt: new Date('2023-06-28T10:00:00Z'),
      updatedAt: new Date('2023-06-28T10:00:00Z'),
    },

    // INV-021 - Wayne Enterprises activities (overdue invoice)
    {
      id: 'IA010',
      invoiceId: 'INV-021',
      organizationId: '2',
      type: 'created',
      description: 'Invoice created',
      createdAt: new Date('2023-04-10T08:45:00Z'),
      updatedAt: new Date('2023-04-10T08:45:00Z'),
    },
    {
      id: 'IA011',
      invoiceId: 'INV-021',
      organizationId: '2',
      type: 'sent',
      description: 'Invoice sent to client via email',
      metadata: {
        method: 'email',
        recipientEmail: '<EMAIL>',
      },
      createdAt: new Date('2023-04-10T10:20:00Z'),
      updatedAt: new Date('2023-04-10T10:20:00Z'),
    },
    {
      id: 'IA012',
      invoiceId: 'INV-021',
      organizationId: '2',
      type: 'viewed',
      description: 'Invoice viewed by client',
      createdAt: new Date('2023-04-12T16:30:00Z'),
      updatedAt: new Date('2023-04-12T16:30:00Z'),
    },
    {
      id: 'IA013',
      invoiceId: 'INV-021',
      organizationId: '2',
      type: 'overdue',
      description: 'Invoice became overdue',
      metadata: {
        daysOverdue: 1,
        dueDate: '2023-05-10',
      },
      createdAt: new Date('2023-05-11T00:01:00Z'),
      updatedAt: new Date('2023-05-11T00:01:00Z'),
    },
    {
      id: 'IA014',
      invoiceId: 'INV-021',
      organizationId: '2',
      type: 'status_changed',
      description: 'Status changed from pending to overdue',
      metadata: {
        oldStatus: 'pending',
        newStatus: 'overdue',
      },
      createdAt: new Date('2023-05-11T00:01:30Z'),
      updatedAt: new Date('2023-05-11T00:01:30Z'),
    },
    {
      id: 'IA015',
      invoiceId: 'INV-021',
      organizationId: '2',
      type: 'reminder_sent',
      description: 'Overdue payment reminder sent',
      metadata: {
        reminderType: 'firm',
        method: 'email',
        daysOverdue: 30,
      },
      createdAt: new Date('2023-06-10T09:00:00Z'),
      updatedAt: new Date('2023-06-10T09:00:00Z'),
    },

    // INV-003 (INV-2024-001) - IBSTC Corporation activities (recent pending invoice)
    {
      id: 'IA016',
      invoiceId: 'INV-003',
      organizationId: '1',
      type: 'created',
      description: 'Invoice created',
      createdAt: new Date('2024-01-15T11:00:00Z'),
      updatedAt: new Date('2024-01-15T11:00:00Z'),
    },
    {
      id: 'IA017',
      invoiceId: 'INV-003',
      organizationId: '1',
      type: 'sent',
      description: 'Invoice sent to client via email',
      metadata: {
        method: 'email',
        recipientEmail: '<EMAIL>',
      },
      createdAt: new Date('2024-01-15T11:30:00Z'),
      updatedAt: new Date('2024-01-15T11:30:00Z'),
    },
    {
      id: 'IA018',
      invoiceId: 'INV-003',
      organizationId: '1',
      type: 'viewed',
      description: 'Invoice viewed by client',
      metadata: {
        viewerIP: '************',
        userAgent: 'Mozilla/5.0...',
      },
      createdAt: new Date('2024-01-16T08:45:00Z'),
      updatedAt: new Date('2024-01-16T08:45:00Z'),
    },
    {
      id: 'IA019',
      invoiceId: 'INV-003',
      organizationId: '1',
      type: 'noted',
      description: 'Internal note added: "Client requested 45-day payment terms"',
      metadata: {
        noteType: 'internal',
        note: 'Client requested 45-day payment terms',
        addedBy: 'admin',
      },
      createdAt: new Date('2024-01-18T14:20:00Z'),
      updatedAt: new Date('2024-01-18T14:20:00Z'),
    },
    {
      id: 'IA020',
      invoiceId: 'INV-003',
      organizationId: '1',
      type: 'downloaded',
      description: 'Invoice PDF downloaded by client',
      metadata: {
        downloadType: 'pdf',
        clientIP: '************',
      },
      createdAt: new Date('2024-01-20T10:15:00Z'),
      updatedAt: new Date('2024-01-20T10:15:00Z'),
    },
  ];

  // Payment Records - Comprehensive payment tracking
  static payments: Payment[] = [
    // INV-001 - Acme Inc. - Full payment (single payment)
    {
      id: 'PAY-001',
      invoiceId: 'INV-001',
      organizationId: '1',
      amount: 591.25,
      method: 'bank_transfer',
      type: 'payment',
      status: 'completed',
      paymentDate: new Date('2023-06-15'),
      processedAt: new Date('2023-06-15T11:45:00Z'),
      transactionId: 'TXN-********-001',
      reference: 'INV-001 Payment',
      notes: 'Wire transfer received from Acme Inc.',
      clientNotified: true,
      receiptSent: true,
      attachments: [],
      createdAt: new Date('2023-06-15T11:45:00Z'),
      updatedAt: new Date('2023-06-15T11:45:00Z'),
    },

    // INV-002 - Globex Corporation - Partial payments (2 payments, still outstanding)
    {
      id: 'PAY-002',
      invoiceId: 'INV-002',
      organizationId: '1',
      amount: 400.00,
      method: 'credit_card',
      type: 'payment',
      status: 'completed',
      paymentDate: new Date('2023-06-10'),
      processedAt: new Date('2023-06-10T14:30:00Z'),
      transactionId: 'CC-********-102',
      reference: 'Partial payment 1 of 2',
      notes: 'First partial payment via corporate credit card',
      externalTransactionId: 'ch_1NXY2023Globex001',
      clientNotified: true,
      receiptSent: true,
      attachments: [],
      createdAt: new Date('2023-06-10T14:30:00Z'),
      updatedAt: new Date('2023-06-10T14:30:00Z'),
    },
    {
      id: 'PAY-003',
      invoiceId: 'INV-002',
      organizationId: '1',
      amount: 300.00,
      method: 'check',
      type: 'payment',
      status: 'completed',
      paymentDate: new Date('2023-06-25'),
      processedAt: new Date('2023-06-25T10:15:00Z'),
      transactionId: 'CHK-20230625-204',
      reference: 'Check #1247 - Partial payment 2',
      notes: 'Check payment received and deposited',
      clientNotified: true,
      receiptSent: true,
      attachments: [
        {
          id: 'att_pay_001',
          name: 'check-1247-scan.pdf',
          type: 'application/pdf',
          uri: 'file:///mock/path/check-1247-scan.pdf',
          size: 124568,
          uploadedAt: new Date('2023-06-25T10:20:00Z'),
        }
      ],
      createdAt: new Date('2023-06-25T10:15:00Z'),
      updatedAt: new Date('2023-06-25T10:15:00Z'),
    },

    // INV-021 - Wayne Enterprises - No payments yet (overdue invoice)
    // (No payment records for this invoice)

    // INV-003 - IBSTC Corporation - Recent partial payment
    {
      id: 'PAY-004',
      invoiceId: 'INV-003',
      organizationId: '1',
      amount: 645.00, // Half payment
      method: 'bank_transfer',
      type: 'payment',
      status: 'completed',
      paymentDate: new Date('2024-01-30'),
      processedAt: new Date('2024-01-30T09:30:00Z'),
      transactionId: 'TXN-********-301',
      reference: 'Partial payment - 50% down',
      notes: 'Initial payment as agreed. Remaining balance due in 30 days.',
      clientNotified: true,
      receiptSent: true,
      attachments: [],
      createdAt: new Date('2024-01-30T09:30:00Z'),
      updatedAt: new Date('2024-01-30T09:30:00Z'),
    },

    // Example of a refund (for testing refund functionality)
    {
      id: 'PAY-005',
      invoiceId: 'INV-001',
      organizationId: '1',
      amount: -50.00, // Negative amount for refund
      method: 'bank_transfer',
      type: 'refund',
      status: 'completed',
      paymentDate: new Date('2023-07-01'),
      processedAt: new Date('2023-07-01T15:20:00Z'),
      transactionId: 'REF-********-001',
      reference: 'Partial refund - design revision credit',
      notes: 'Refund for unused design revisions',
      refundedPaymentId: 'PAY-001',
      refundReason: 'Client requested fewer revisions than contracted',
      clientNotified: true,
      receiptSent: true,
      attachments: [],
      createdAt: new Date('2023-07-01T15:20:00Z'),
      updatedAt: new Date('2023-07-01T15:20:00Z'),
    },

    // Example of a late fee
    {
      id: 'PAY-006',
      invoiceId: 'INV-021',
      organizationId: '2',
      amount: 25.00,
      method: 'other',
      type: 'fee',
      status: 'completed',
      paymentDate: new Date('2023-06-10'),
      processedAt: new Date('2023-06-10T00:01:00Z'),
      transactionId: 'FEE-********-001',
      reference: 'Late payment fee - 30 days overdue',
      notes: 'Automatic late fee applied per payment terms',
      clientNotified: true,
      receiptSent: false,
      attachments: [],
      createdAt: new Date('2023-06-10T00:01:00Z'),
      updatedAt: new Date('2023-06-10T00:01:00Z'),
    },
  ];

  // Tax options
  static taxOptions: TaxOption[] = [
    {
      id: 'no-tax',
      name: 'No Tax',
      rate: 0,
      description: 'No tax applied',
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    },
    {
      id: 'sales-tax-7.5',
      name: 'Sales Tax',
      rate: 7.5,
      description: '7.5% Sales Tax',
      region: 'State',
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    },
    {
      id: 'sales-tax-8.5',
      name: 'Sales Tax',
      rate: 8.5,
      description: '8.5% Sales Tax',
      region: 'State',
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    },
  ];

  // ============================================
  // SETTINGS DATA - Phase 1 Implementation
  // ============================================

  // User Profiles
  static userProfiles: UserProfile[] = [
    {
      id: 'user_001',
      firstName: 'John',
      lastName: 'Doe', 
      email: '<EMAIL>',
      phone: '+1 (*************',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      timezone: 'America/New_York',
      dateFormat: 'MM/DD/YYYY',
      language: 'en',
      notifications: {
        email: true,
        push: true,
        marketing: false,
        invoiceReminders: true,
        paymentUpdates: true,
      },
      preferences: {
        currency: 'USD',
        theme: 'system',
        compactMode: false,
      },
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    },
  ];

  // Security Settings
  static securitySettings: SecuritySettings[] = [
    {
      id: 'sec_001',
      userId: 'user_001',
      twoFactorEnabled: false,
      loginNotifications: true,
      sessionTimeout: 30,
      lastPasswordChange: new Date('2023-01-01'),
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    },
  ];

  // Subscription Plans
  static subscriptionPlans: SubscriptionPlan[] = [
    {
      id: 'plan_starter',
      name: 'Starter',
      description: 'Perfect for freelancers and small businesses',
      price: 9.99,
      currency: 'USD',
      interval: 'month',
      features: [
        'Up to 2 organizations',
        'Unlimited invoices',
        'Basic templates',
        'Email support',
        'Mobile app access',
      ],
      limits: {
        organizations: 2,
        invoices: -1, // unlimited
        clients: 100,
        storage: 1024, // 1GB
        templates: 5,
        customFields: 3,
      },
      isPopular: false,
      isActive: true,
      trialDays: 14,
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    },
    {
      id: 'plan_professional',
      name: 'Professional',
      description: 'For growing businesses with advanced needs',
      price: 19.99,
      currency: 'USD',
      interval: 'month',
      features: [
        'Up to 10 organizations',
        'Unlimited invoices',
        'Premium templates',
        'Priority support',
        'Custom branding',
        'Advanced reporting',
        'API access',
        'Integrations',
      ],
      limits: {
        organizations: 10,
        invoices: -1,
        clients: 1000,
        storage: 5120, // 5GB
        templates: 20,
        customFields: 10,
      },
      isPopular: true,
      isActive: true,
      trialDays: 14,
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    },
    {
      id: 'plan_enterprise',
      name: 'Enterprise',
      description: 'For large organizations with complex requirements',
      price: 49.99,
      currency: 'USD',
      interval: 'month',
      features: [
        'Unlimited organizations',
        'Unlimited invoices',
        'All templates',
        '24/7 dedicated support',
        'White-label branding',
        'Advanced analytics',
        'Full API access',
        'All integrations',
        'Custom workflows',
        'SSO support',
      ],
      limits: {
        organizations: -1,
        invoices: -1,
        clients: -1,
        storage: -1,
        templates: -1,
        customFields: -1,
      },
      isPopular: false,
      isActive: true,
      trialDays: 30,
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    },
  ];

  // User Subscriptions
  static subscriptions: Subscription[] = [
    {
      id: 'sub_001',
      userId: 'user_001',
      planId: 'plan_professional',
      status: 'active',
      currentPeriodStart: new Date('2024-01-01'),
      currentPeriodEnd: new Date('2024-02-01'),
      trialStart: new Date('2023-12-15'),
      trialEnd: new Date('2023-12-31'),
      cancelAtPeriodEnd: false,
      createdAt: new Date('2023-12-15'),
      updatedAt: new Date('2023-12-15'),
    },
  ];

  // Invoice Templates
  static invoiceTemplates: InvoiceTemplate[] = [
    {
      id: 'template_minimal',
      name: 'Minimal',
      description: 'Clean and simple design perfect for any business',
      thumbnail: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=300&h=400&fit=crop',
      category: 'minimal',
      isPremium: false,
      isBuiltIn: true,
      colorScheme: {
        primary: '#2563eb',
        secondary: '#64748b',
        accent: '#0ea5e9',
        text: '#1e293b',
        background: '#ffffff',
        border: '#e2e8f0',
      },
      layout: {
        headerStyle: 'minimal',
        fontFamily: 'Inter',
        fontSize: 'medium',
        logoPosition: 'left',
        logoSize: 'medium',
        showCompanyDetails: true,
        showClientDetails: true,
        itemTableStyle: 'minimal',
        showItemNumbers: false,
        showTaxColumn: true,
        showDiscountColumn: false,
        compactLayout: false,
      },
      customization: {
        footerText: 'Thank you for your business!',
        thanksMessage: 'We appreciate your prompt payment.',
        showPaymentInstructions: true,
        showSignature: false,
        watermark: {
          enabled: false,
          opacity: 0.1,
        },
      },
      organizationId: '1',
      isDefault: true,
      isActive: true,
      usage: {
        count: 15,
        lastUsed: new Date('2024-01-15'),
      },
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    },
    {
      id: 'template_modern',
      name: 'Modern',
      description: 'Contemporary design with bold colors and modern typography',
      thumbnail: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=300&h=400&fit=crop',
      category: 'modern',
      isPremium: false,
      isBuiltIn: true,
      colorScheme: {
        primary: '#7c3aed',
        secondary: '#a855f7',
        accent: '#c084fc',
        text: '#1f2937',
        background: '#ffffff',
        border: '#e5e7eb',
      },
      layout: {
        headerStyle: 'modern',
        fontFamily: 'Poppins',
        fontSize: 'medium',
        logoPosition: 'left',
        logoSize: 'large',
        showCompanyDetails: true,
        showClientDetails: true,
        itemTableStyle: 'modern',
        showItemNumbers: true,
        showTaxColumn: true,
        showDiscountColumn: true,
        compactLayout: false,
      },
      customization: {
        footerText: 'Thank you for choosing our services!',
        thanksMessage: 'Payment terms: Net 30 days',
        showPaymentInstructions: true,
        showSignature: false,
        watermark: {
          enabled: false,
          opacity: 0.1,
        },
      },
      isDefault: false,
      isActive: true,
      usage: {
        count: 8,
        lastUsed: new Date('2024-01-10'),
      },
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    },
    {
      id: 'template_professional',
      name: 'Professional',
      description: 'Classic business template with traditional styling',
      thumbnail: 'https://images.unsplash.com/photo-1554224155-8d04cb21cd6c?w=300&h=400&fit=crop',
      category: 'professional',
      isPremium: true,
      isBuiltIn: true,
      colorScheme: {
        primary: '#1f2937',
        secondary: '#4b5563',
        accent: '#6b7280',
        text: '#111827',
        background: '#ffffff',
        border: '#d1d5db',
      },
      layout: {
        headerStyle: 'classic',
        fontFamily: 'Roboto',
        fontSize: 'medium',
        logoPosition: 'center',
        logoSize: 'medium',
        showCompanyDetails: true,
        showClientDetails: true,
        itemTableStyle: 'bordered',
        showItemNumbers: true,
        showTaxColumn: true,
        showDiscountColumn: false,
        compactLayout: false,
      },
      customization: {
        footerText: 'Professional Services Invoice',
        thanksMessage: 'Thank you for your business partnership.',
        showPaymentInstructions: true,
        showSignature: true,
        watermark: {
          enabled: false,
          opacity: 0.1,
        },
      },
      isDefault: false,
      isActive: true,
      usage: {
        count: 5,
        lastUsed: new Date('2024-01-08'),
      },
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    },
  ];

  // App Defaults per Organization
  static appDefaults: AppDefaults[] = [
    {
      id: 'defaults_1',
      organizationId: '1',
      currency: {
        code: 'USD',
        symbol: '$',
        position: 'before',
        decimalPlaces: 2,
        thousandsSeparator: ',',
        decimalSeparator: '.',
      },
      taxes: {
        defaultRate: 7.5,
        taxName: 'Sales Tax',
        inclusive: false,
        enabled: true,
        compoundTax: false,
        taxOnShipping: false,
        exemptDescription: 'Tax Exempt',
        regions: [
          {
            id: 'region_1',
            name: 'California',
            rate: 7.5,
            isDefault: true,
          },
          {
            id: 'region_2',
            name: 'New York',
            rate: 8.25,
            isDefault: false,
          },
        ],
      },
      payment: {
        defaultTerms: 'Net 30',
        lateFeeEnabled: false,
        lateFeeAmount: 0,
        lateFeeType: 'fixed',
        lateFeeGracePeriod: 5,
        discountEnabled: true,
        earlyPaymentDiscount: 2,
        discountDays: 10,
        allowPartialPayments: true,
        minimumPaymentAmount: 0,
      },
      numbering: {
        prefix: 'INV',
        suffix: '',
        format: 'sequential',
        startNumber: 1,
        padZeros: 3,
        resetAnnually: false,
        separators: {
          beforeNumber: '-',
          afterNumber: '',
          dateFormat: 'YYYY',
        },
      },
      notifications: {
        sendReminders: true,
        reminderDays: [7, 3, 1],
        reminderFrequency: 'once',
        autoMarkOverdue: true,
        overdueDays: 30,
        escalationEnabled: false,
        escalationDays: 60,
        ccOnReminders: [],
        sendConfirmations: true,
        sendReceiptNotifications: true,
      },
      branding: {
        logoWidth: 150,
        logoHeight: 75,
        primaryColor: '#2563eb',
        secondaryColor: '#64748b',
        socialLinks: {
          linkedin: 'https://linkedin.com/company/moonlight-design',
          twitter: 'https://twitter.com/moonlightdesign',
          facebook: undefined,
          instagram: undefined,
          website: 'https://moonlightdesign.com',
        },
        showPoweredBy: true,
      },
      preferences: {
        defaultDueDays: 30,
        showItemCodes: false,
        showLineNumbers: false,
        defaultItemTaxable: true,
        showClientNotes: true,
        showInternalNotes: false,
        autoCalculateDiscount: true,
        roundTotals: true,
        language: 'en',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12',
        timezone: 'America/New_York',
      },
      integrations: {
        quickbooksEnabled: false,
        xeroEnabled: false,
        zapierWebhooks: [],
        customWebhooks: [],
      },
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    },
    {
      id: 'defaults_2',
      organizationId: '2',
      currency: {
        code: 'USD',
        symbol: '$',
        position: 'before',
        decimalPlaces: 2,
        thousandsSeparator: ',',
        decimalSeparator: '.',
      },
      taxes: {
        defaultRate: 8.5,
        taxName: 'Sales Tax',
        inclusive: false,
        enabled: true,
        compoundTax: false,
        taxOnShipping: false,
        exemptDescription: 'Tax Exempt',
        regions: [],
      },
      payment: {
        defaultTerms: 'Net 30',
        lateFeeEnabled: true,
        lateFeeAmount: 25,
        lateFeeType: 'fixed',
        lateFeeGracePeriod: 0,
        discountEnabled: false,
        earlyPaymentDiscount: 0,
        discountDays: 10,
        allowPartialPayments: false,
        minimumPaymentAmount: 0,
      },
      numbering: {
        prefix: 'INV',
        suffix: '',
        format: 'sequential',
        startNumber: 1,
        padZeros: 3,
        resetAnnually: false,
        separators: {
          beforeNumber: '-',
          afterNumber: '',
          dateFormat: 'YYYY',
        },
      },
      notifications: {
        sendReminders: true,
        reminderDays: [7, 3, 1],
        reminderFrequency: 'once',
        autoMarkOverdue: true,
        overdueDays: 30,
        escalationEnabled: false,
        escalationDays: 60,
        ccOnReminders: [],
        sendConfirmations: true,
        sendReceiptNotifications: true,
      },
      branding: {
        logoWidth: 150,
        logoHeight: 75,
        primaryColor: '#7c3aed',
        secondaryColor: '#a855f7',
        socialLinks: {
          linkedin: undefined,
          twitter: undefined,
          facebook: undefined,
          instagram: undefined,
          website: 'https://stellarweb.com',
        },
        showPoweredBy: true,
      },
      preferences: {
        defaultDueDays: 30,
        showItemCodes: true,
        showLineNumbers: true,
        defaultItemTaxable: true,
        showClientNotes: true,
        showInternalNotes: true,
        autoCalculateDiscount: true,
        roundTotals: true,
        language: 'en',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12',
        timezone: 'America/Los_Angeles',
      },
      integrations: {
        quickbooksEnabled: false,
        xeroEnabled: false,
        zapierWebhooks: [],
        customWebhooks: [],
      },
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    },
  ];

  // Supported Currencies
  static supportedCurrencies: SupportedCurrency[] = [
    { code: 'USD', name: 'US Dollar', symbol: '$', country: 'United States', isPopular: true },
    { code: 'EUR', name: 'Euro', symbol: '€', country: 'European Union', isPopular: true },
    { code: 'GBP', name: 'British Pound', symbol: '£', country: 'United Kingdom', isPopular: true },
    { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$', country: 'Canada', isPopular: true },
    { code: 'AUD', name: 'Australian Dollar', symbol: 'A$', country: 'Australia', isPopular: true },
    { code: 'JPY', name: 'Japanese Yen', symbol: '¥', country: 'Japan', isPopular: true },
    { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF', country: 'Switzerland', isPopular: false },
    { code: 'SEK', name: 'Swedish Krona', symbol: 'kr', country: 'Sweden', isPopular: false },
    { code: 'NOK', name: 'Norwegian Krone', symbol: 'kr', country: 'Norway', isPopular: false },
    { code: 'DKK', name: 'Danish Krone', symbol: 'kr', country: 'Denmark', isPopular: false },
  ];

  // Help Articles
  static helpArticles: HelpArticle[] = [
    {
      id: 'help_001',
      title: 'Getting Started with Invoice Plus',
      slug: 'getting-started',
      content: `Welcome to Invoice Plus! This guide will help you set up your account and create your first invoice.

## Step 1: Set Up Your Organization
First, you'll need to create an organization. This represents your business or company.

1. Navigate to Settings > Organizations
2. Click "Add Organization"
3. Fill in your business details
4. Save your organization

## Step 2: Add Your First Client
Before creating an invoice, you'll need to add a client.

1. Go to the Clients tab
2. Click "Add Client"
3. Enter client details including name, email, and address
4. Save the client

## Step 3: Create Your First Invoice
Now you're ready to create an invoice!

1. Go to the Invoices tab
2. Click "Create Invoice"
3. Select your client
4. Add line items for products or services
5. Review and send the invoice

That's it! You've successfully created your first invoice.`,
      excerpt: 'Learn how to set up your account and create your first invoice in just a few simple steps.',
      categoryId: 'cat_getting_started',
      tags: ['getting-started', 'setup', 'first-invoice'],
      difficulty: 'beginner',
      estimatedReadTime: 3,
      views: 1250,
      helpful: 45,
      notHelpful: 2,
      featured: true,
      searchKeywords: ['setup', 'organization', 'client', 'invoice', 'getting started'],
      relatedArticles: ['help_002', 'help_003'],
      lastReviewed: new Date('2024-01-01'),
      reviewedBy: 'admin',
      version: '1.0.0',
      isPublished: true,
      publishedAt: new Date('2023-01-01'),
      authorId: 'admin',
      attachments: [],
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2024-01-01'),
    },
    {
      id: 'help_002',
      title: 'Managing Your Clients',
      slug: 'managing-clients',
      content: `Learn how to effectively manage your client database for better invoice organization.

## Adding New Clients
When adding clients, make sure to include:
- Full business name
- Contact person
- Email address
- Billing address
- Phone number (optional)

## Organizing Clients
Use tags and custom fields to organize your clients by:
- Industry type
- Payment terms
- Geographic location
- Project status

## Client Communication
Track all communication with your clients through:
- Invoice comments
- Payment notes
- Follow-up reminders`,
      excerpt: 'Best practices for organizing and managing your client database.',
      categoryId: 'cat_clients',
      tags: ['clients', 'organization', 'management'],
      difficulty: 'beginner',
      estimatedReadTime: 5,
      views: 890,
      helpful: 32,
      notHelpful: 1,
      featured: false,
      searchKeywords: ['clients', 'contacts', 'organization', 'management'],
      relatedArticles: ['help_001', 'help_004'],
      lastReviewed: new Date('2024-01-01'),
      reviewedBy: 'admin',
      version: '1.0.0',
      isPublished: true,
      publishedAt: new Date('2023-01-15'),
      authorId: 'admin',
      attachments: [],
      createdAt: new Date('2023-01-15'),
      updatedAt: new Date('2024-01-01'),
    },
  ];

  // Help Categories
  static helpCategories: HelpCategory[] = [
    {
      id: 'cat_getting_started',
      name: 'Getting Started',
      description: 'Everything you need to know to get up and running',
      icon: 'rocket',
      color: '#10b981',
      sortOrder: 1,
      isActive: true,
    },
    {
      id: 'cat_invoices',
      name: 'Invoices',
      description: 'Creating, managing, and customizing invoices',
      icon: 'document-text',
      color: '#3b82f6',
      sortOrder: 2,
      isActive: true,
    },
    {
      id: 'cat_clients',
      name: 'Clients',
      description: 'Managing your client database',
      icon: 'users',
      color: '#8b5cf6',
      sortOrder: 3,
      isActive: true,
    },
    {
      id: 'cat_payments',
      name: 'Payments',
      description: 'Payment processing and tracking',
      icon: 'credit-card',
      color: '#f59e0b',
      sortOrder: 4,
      isActive: true,
    },
    {
      id: 'cat_settings',
      name: 'Settings',
      description: 'Customizing your account and preferences',
      icon: 'cog',
      color: '#6b7280',
      sortOrder: 5,
      isActive: true,
    },
  ];

  // FAQs
  static faqs: FAQ[] = [
    {
      id: 'faq_001',
      question: 'How do I change my invoice template?',
      answer: 'You can change your invoice template by going to Settings > Invoice Design. Select from our collection of professional templates or create your own custom design.',
      categoryId: 'cat_invoices',
      priority: 1,
      tags: ['template', 'design', 'customization'],
      views: 456,
      helpful: 23,
      notHelpful: 1,
      isPublished: true,
      searchKeywords: ['template', 'design', 'change', 'customize'],
      relatedFAQs: ['faq_002'],
      relatedArticles: ['help_003'],
      lastUpdated: new Date('2024-01-01'),
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2024-01-01'),
    },
    {
      id: 'faq_002',
      question: 'Can I set up automatic payment reminders?',
      answer: 'Yes! Go to Settings > Defaults > Notifications to set up automatic payment reminders. You can customize when reminders are sent and the frequency.',
      categoryId: 'cat_payments',
      priority: 2,
      tags: ['reminders', 'automation', 'payments'],
      views: 324,
      helpful: 18,
      notHelpful: 0,
      isPublished: true,
      searchKeywords: ['reminders', 'automatic', 'payments', 'notifications'],
      relatedFAQs: ['faq_003'],
      relatedArticles: ['help_005'],
      lastUpdated: new Date('2024-01-01'),
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2024-01-01'),
    },
  ];

  // Feedback Categories
  static feedbackCategories: FeedbackCategory[] = [
    {
      id: 'fb_bug',
      name: 'Bug Report',
      description: 'Report issues or problems with the app',
      icon: 'bug',
      color: '#ef4444',
      sortOrder: 1,
      isActive: true,
    },
    {
      id: 'fb_feature',
      name: 'Feature Request',
      description: 'Suggest new features or improvements',
      icon: 'lightbulb',
      color: '#f59e0b',
      sortOrder: 2,
      isActive: true,
    },
    {
      id: 'fb_general',
      name: 'General Feedback',
      description: 'General comments or suggestions',
      icon: 'chat',
      color: '#6b7280',
      sortOrder: 3,
      isActive: true,
    },
  ];

  // Sample Feedback
  static feedback: Feedback[] = [
    {
      id: 'feedback_001',
      userId: 'user_001',
      category: 'feature',
      subject: 'Add dark mode support',
      message: 'It would be great to have a dark mode option for the app, especially for users who work late hours. This would reduce eye strain and provide a better user experience.',
      priority: 'medium',
      status: 'open',
      tags: ['ui', 'accessibility', 'dark-mode'],
      deviceInfo: {
        platform: 'iOS',
        version: '17.2',
        model: 'iPhone 15 Pro',
        osVersion: '17.2',
      },
      appInfo: {
        version: '1.0.0',
        buildNumber: '100',
        environment: 'production',
      },
      attachments: [],
      followUpRequired: false,
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15'),
    },
  ];
} 