import { useQuery } from '@tanstack/react-query';

// Import services from the service layer
import { useCancelSubscription, useChangeSubscription, useSubscriptionPlans, useUserSubscription } from '@/services/subscription/subscription';
import { useUserProfile } from '@/services/user/profile';
import { useUpdateUserProfile, useUploadAvatar } from '@/services/user/update';
import { useCurrentUserId } from '@/stores/user-selectors';

// ==========================================
// USER PROFILE HOOKS - Re-export from service layer
// ==========================================

export { useUpdateUserProfile, useUploadAvatar, useUserProfile };

// ==========================================
// SECURITY SETTINGS HOOKS  
// ==========================================

export const useSecuritySettings = () => {
  const currentUserId = useCurrentUserId();
  
  return useQuery({
    queryKey: ['securitySettings', currentUserId],
    queryFn: async () => {
      // Mock security settings for now - would come from provider in real app
      return {
        id: 'security_001',
        userId: currentUserId,
        twoFactorEnabled: false,
        loginNotifications: true,
        sessionTimeout: 30,
        lastPasswordChange: new Date('2023-01-01'),
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-01'),
      };
    },
    enabled: !!currentUserId,
  });
};

// ==========================================
// SUBSCRIPTION HOOKS - Re-export from service layer
// ==========================================

export { useCancelSubscription, useChangeSubscription, useSubscriptionPlans, useUserSubscription };

// ==========================================
// COMBINED HOOKS FOR COMMON USE CASES
// ==========================================

export const useAccountData = () => {
  const userProfile = useUserProfile();
  const securitySettings = useSecuritySettings();
  
  return {
    userProfile,
    securitySettings,
    isLoading: userProfile.loading || securitySettings.isLoading,
    isError: userProfile.error || securitySettings.isError,
    error: userProfile.error || securitySettings.error,
  };
};

export const useSubscriptionData = () => {
  const subscription = useUserSubscription();
  const plans = useSubscriptionPlans();
  
  return {
    subscription,
    plans,
    isLoading: subscription.loading || plans.loading,
    isError: subscription.error || plans.error,
    error: subscription.error || plans.error,
  };
}; 