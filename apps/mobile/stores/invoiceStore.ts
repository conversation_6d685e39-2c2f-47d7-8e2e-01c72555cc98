import { taxOptions } from '@/constants/data';
import { create } from 'zustand';

// Tax method enum for better type safety
export enum TaxMethod {
  NONE = 'none',
  ON_TOTAL = 'on_total',
  PER_ITEM = 'per_item',
  AS_DEDUCTION = 'as_deduction'
}

export interface LineItem {
  id: string;
  description: string;
  quantity: string;
  price: string;
  total: string;
  serviceId?: string;
  unit?: 'fixed' | 'hour' | 'month' | 'custom';
  // New tax fields for per-item tax
  taxRate?: number;
  taxAmount?: number;
  taxable?: boolean; // Whether this item should have tax applied
  selectedTaxId?: string; // Reference to selected tax option for this item
  itemDescription?: string; // Additional description for the item
  // New discount fields
  discount?: string; // Discount amount or percentage
  discountType?: 'percentage' | 'fixed'; // Type of discount
}

// New tax configuration interface
export interface TaxConfiguration {
  method: TaxMethod;
  rate: number;
  taxId: string;
  inclusive: boolean;
}

// Attachment interface for invoice store
export interface Attachment {
  id: string;
  name: string;
  size: number;
  type: string;
  uri: string;
  uploadedAt: Date;
}

// New payment methods interface for invoice
export interface InvoicePaymentMethods {
  selectedGatewayId?: string; // Selected payment gateway for this invoice
  selectedCustomInstructionIds: string[]; // Selected custom instructions for this invoice
  isManualSelected: boolean; // Whether manual payment method is selected
}

interface InvoiceStore {
  // Form state
  invoiceNumber: string;
  invoiceDate: Date;
  dueDate: Date;
  selectedClientId: string | undefined;
  notes: string;
  terms: string;
  lineItems: LineItem[];
  // Updated tax configuration
  taxConfig: TaxConfiguration;
  // Attachments (max 3)
  attachments: Attachment[];
  // Payment methods selection for this invoice
  paymentMethods: InvoicePaymentMethods;
  
  // UI state
  notesExpanded: boolean;
  termsExpanded: boolean;
  
  // Actions
  setInvoiceNumber: (number: string) => void;
  setInvoiceDate: (date: Date) => void;
  setDueDate: (date: Date) => void;
  setSelectedClient: (clientId: string | undefined) => void;
  setNotes: (notes: string) => void;
  setTerms: (terms: string) => void;
  setNotesExpanded: (expanded: boolean) => void;
  setTermsExpanded: (expanded: boolean) => void;
  // Updated tax methods
  setTaxConfiguration: (config: Partial<TaxConfiguration>) => void;
  setItemTaxable: (itemId: string, taxable: boolean) => void;
  setItemTaxRate: (itemId: string, rate: number) => void;
  setItemSelectedTaxId: (itemId: string, taxId: string) => void;
  
  // Payment methods actions
  setSelectedGateway: (gatewayId: string | undefined) => void;
  setSelectedCustomInstructions: (instructionIds: string[]) => void;
  toggleCustomInstruction: (instructionId: string) => void;
  setManualSelected: (isSelected: boolean) => void;
  
  // Payment methods getters
  getSelectedGatewayId: () => string | undefined;
  getSelectedCustomInstructionIds: () => string[];
  getTotalSelectedPaymentMethods: () => number;
  
  // Attachments
  addAttachment: (attachment: Attachment) => void;
  removeAttachment: (attachmentId: string) => void;
  
  // Line items
  addLineItem: () => void;
  updateLineItem: (id: string, field: keyof LineItem, value: string) => void;
  deleteLineItem: (id: string) => void;
  addServiceAsLineItem: (service: any) => void;
  
  // Updated calculations
  getSubtotal: () => number;
  getTaxAmount: () => number;
  getItemTaxAmount: (itemId: string) => number;
  getPreTaxAmount: () => number;
  getTotalBeforeTax: () => number;
  getTotal: () => number;
  
  // Validation
  isFormValid: () => boolean;
  
  // Load existing invoice data
  loadInvoiceData: (invoice: any) => void;
  
  // Reset
  resetForm: () => void;
  
  // Generate next invoice number
  generateNextInvoiceNumber: (organizationNickname: string, invoiceCount: number) => string;
}

const defaultDueDate = () => {
  const date = new Date();
  date.setDate(date.getDate() + 30);
  return date;
};

const createEmptyLineItem = (): LineItem => ({
  id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  description: '',
  quantity: '1',
  price: '0',
  total: '0.00',
});

export const useInvoiceStore = create<InvoiceStore>((set, get) => ({
  // Initial state
  invoiceNumber: 'INV-ORG-001',
  invoiceDate: new Date(),
  dueDate: defaultDueDate(),
  selectedClientId: undefined,
  notes: '',
  terms: 'Payment due within 30 days.',
  lineItems: [createEmptyLineItem()],
  taxConfig: {
    method: TaxMethod.NONE,
    rate: 0,
    taxId: taxOptions.find(t => t.rate === 7.5)?.id || taxOptions[0].id,
    inclusive: false,
  },
  attachments: [],
  notesExpanded: false,
  termsExpanded: false,
  paymentMethods: {
    selectedGatewayId: undefined,
    selectedCustomInstructionIds: [],
    isManualSelected: false,
  },

  // Basic setters
  setInvoiceNumber: (number: string) => set({ invoiceNumber: number }),
  setInvoiceDate: (date: Date) => set({ invoiceDate: date }),
  setDueDate: (date: Date) => set({ dueDate: date }),
  setSelectedClient: (clientId: string | undefined) => set({ selectedClientId: clientId }),
  setNotes: (notes: string) => set({ notes }),
  setTerms: (terms: string) => set({ terms }),
  setNotesExpanded: (expanded: boolean) => set({ notesExpanded: expanded }),
  setTermsExpanded: (expanded: boolean) => set({ termsExpanded: expanded }),
  setTaxConfiguration: (config: Partial<TaxConfiguration>) => set({ taxConfig: { ...get().taxConfig, ...config } }),
  setItemTaxable: (itemId: string, taxable: boolean) => {
    set((state) => ({
      lineItems: state.lineItems.map(item => {
        if (item.id !== itemId) return item;
        return { ...item, taxable };
      })
    }));
  },
  setItemTaxRate: (itemId: string, rate: number) => {
    set((state) => ({
      lineItems: state.lineItems.map(item => {
        if (item.id !== itemId) return item;
        return { ...item, taxRate: rate };
      })
    }));
  },
  setItemSelectedTaxId: (itemId: string, taxId: string) => {
    set((state) => ({
      lineItems: state.lineItems.map(item => {
        if (item.id !== itemId) return item;
        return { ...item, selectedTaxId: taxId };
      })
    }));
  },

  // Payment methods actions
  setSelectedGateway: (gatewayId: string | undefined) => {
    set((state) => ({
      paymentMethods: {
        ...state.paymentMethods,
        selectedGatewayId: gatewayId,
        // Clear manual selection when gateway is selected
        isManualSelected: gatewayId ? false : state.paymentMethods.isManualSelected
      }
    }));
  },
  setSelectedCustomInstructions: (instructionIds: string[]) => {
    set((state) => ({
      paymentMethods: {
        ...state.paymentMethods,
        selectedCustomInstructionIds: instructionIds
      }
    }));
  },
  toggleCustomInstruction: (instructionId: string) => {
    set((state) => ({
      paymentMethods: {
        ...state.paymentMethods,
        selectedCustomInstructionIds: state.paymentMethods.selectedCustomInstructionIds.includes(instructionId)
          ? state.paymentMethods.selectedCustomInstructionIds.filter((id: string) => id !== instructionId)
          : [...state.paymentMethods.selectedCustomInstructionIds, instructionId]
      }
    }));
  },
  setManualSelected: (isSelected: boolean) => {
    set((state) => ({
      paymentMethods: {
        ...state.paymentMethods,
        isManualSelected: isSelected,
        // Clear gateway selection when manual is selected
        selectedGatewayId: isSelected ? undefined : state.paymentMethods.selectedGatewayId,
        // Clear custom instructions when manual is deselected
        selectedCustomInstructionIds: isSelected ? state.paymentMethods.selectedCustomInstructionIds : []
      }
    }));
  },

  // Payment methods getters
  getSelectedGatewayId: () => get().paymentMethods.selectedGatewayId,
  getSelectedCustomInstructionIds: () => get().paymentMethods.selectedCustomInstructionIds,
  getTotalSelectedPaymentMethods: () => {
    const state = get();
    const hasGateway = state.paymentMethods.selectedGatewayId ? 1 : 0;
    const hasManualWithInstructions = state.paymentMethods.isManualSelected && state.paymentMethods.selectedCustomInstructionIds.length > 0 ? 1 : 0;
    return hasGateway + hasManualWithInstructions;
  },

  // Attachments
  addAttachment: (attachment: Attachment) => {
    set((state) => ({
      attachments: [...state.attachments, attachment]
    }));
  },

  removeAttachment: (attachmentId: string) => {
    set((state) => ({
      attachments: state.attachments.filter(a => a.id !== attachmentId)
    }));
  },

  // Line item actions
  addLineItem: () => {
    set((state) => ({
      lineItems: [...state.lineItems, createEmptyLineItem()]
    }));
  },

  updateLineItem: (id: string, field: keyof LineItem, value: string) => {
    set((state) => ({
      lineItems: state.lineItems.map(item => {
        if (item.id !== id) return item;
        
        const updatedItem = { ...item, [field]: value };
        
        // Calculate total if quantity, price, discount, or discountType changes
        if (field === 'quantity' || field === 'price' || field === 'discount' || field === 'discountType') {
          const quantity = parseFloat(field === 'quantity' ? value : item.quantity) || 0;
          const price = parseFloat(field === 'price' ? value : item.price) || 0;
          const discountValue = parseFloat(field === 'discount' ? value : item.discount || '0') || 0;
          const discountType = field === 'discountType' ? value : item.discountType || 'percentage';
          
          let subtotal = quantity * price;
          
          // Apply discount
          if (discountValue > 0) {
            if (discountType === 'percentage') {
              subtotal = subtotal * (1 - discountValue / 100);
            } else {
              subtotal = Math.max(0, subtotal - discountValue);
            }
          }
          
          updatedItem.total = subtotal.toFixed(2);
        }
        
        return updatedItem;
      })
    }));
  },

  deleteLineItem: (id: string) => {
    const state = get();
    if (state.lineItems.length > 1) {
      set({
        lineItems: state.lineItems.filter(item => item.id !== id)
      });
    }
  },

  addServiceAsLineItem: (service: any) => {
    const serviceRate = service.pricing?.rate || service.rate || 0;
    const serviceUnit = service.pricing?.unit || service.unit || 'fixed';
    
    const newItem: LineItem = {
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      description: service.name,
      itemDescription: service.description,
      quantity: '1',
      price: serviceRate.toString(),
      total: serviceRate.toFixed(2),
      serviceId: service.id,
      unit: serviceUnit as 'fixed' | 'hour' | 'month' | 'custom',
    };
    
    set((state) => ({
      lineItems: [...state.lineItems, newItem]
    }));
  },

  // Calculations
  getSubtotal: () => {
    const state = get();
    return state.lineItems.reduce((sum, item) => sum + parseFloat(item.total || '0'), 0);
  },

  getTaxAmount: () => {
    const state = get();
    const subtotal = get().getSubtotal();
    
    switch (state.taxConfig.method) {
      case TaxMethod.NONE:
        return 0;
        
      case TaxMethod.ON_TOTAL:
        const tax = taxOptions.find(t => t.id === state.taxConfig.taxId);
        const taxRate = (tax?.rate || 0) / 100;
        
        if (state.taxConfig.inclusive) {
          // Extract tax from inclusive price: tax = total - (total / (1 + taxRate))
          return subtotal - (subtotal / (1 + taxRate));
        } else {
          // Add tax to exclusive price: tax = subtotal * taxRate
          return subtotal * taxRate;
        }
        
      case TaxMethod.PER_ITEM:
        // Sum up individual item taxes
        return state.lineItems.reduce((sum, item) => {
          if (item.taxable && item.taxRate) {
            const itemSubtotal = parseFloat(item.total || '0');
            const itemTaxRate = item.taxRate / 100;
            
            if (state.taxConfig.inclusive) {
              // Extract tax from inclusive price
              return sum + (itemSubtotal - (itemSubtotal / (1 + itemTaxRate)));
            } else {
              // Add tax to exclusive price
              return sum + (itemSubtotal * itemTaxRate);
            }
          }
          return sum;
        }, 0);
        
      case TaxMethod.AS_DEDUCTION:
        // Negative tax (deduction) - always works the same way
        const deductionTax = taxOptions.find(t => t.id === state.taxConfig.taxId);
        const deductionRate = (deductionTax?.rate || 0) / 100;
        return -(subtotal * deductionRate);
        
      default:
        return 0;
    }
  },

  getItemTaxAmount: (itemId: string) => {
    const state = get();
    const item = state.lineItems.find(i => i.id === itemId);
    
    if (!item || !item.taxable || state.taxConfig.method !== TaxMethod.PER_ITEM) {
      return 0;
    }
    
    const itemSubtotal = parseFloat(item.total || '0');
    const itemTaxRate = (item.taxRate || 0) / 100;
    
    if (state.taxConfig.inclusive) {
      // Extract tax from inclusive price: tax = total - (total / (1 + taxRate))
      return itemSubtotal - (itemSubtotal / (1 + itemTaxRate));
    } else {
      // Add tax to exclusive price: tax = subtotal * taxRate
      return itemSubtotal * itemTaxRate;
    }
  },

  getPreTaxAmount: () => {
    const state = get();
    const subtotal = get().getSubtotal();
    
    if (!state.taxConfig.inclusive) {
      // For tax exclusive, pre-tax amount is the same as subtotal
      return subtotal;
    }
    
    switch (state.taxConfig.method) {
      case TaxMethod.NONE:
        return subtotal;
        
      case TaxMethod.ON_TOTAL:
        const tax = taxOptions.find(t => t.id === state.taxConfig.taxId);
        const taxRate = (tax?.rate || 0) / 100;
        // For inclusive: pre-tax = total / (1 + taxRate)
        return subtotal / (1 + taxRate);
        
      case TaxMethod.PER_ITEM:
        // Sum up individual item pre-tax amounts
        return state.lineItems.reduce((sum, item) => {
          const itemSubtotal = parseFloat(item.total || '0');
          
          if (item.taxable && item.taxRate) {
            const itemTaxRate = item.taxRate / 100;
            // For inclusive: pre-tax = total / (1 + taxRate)
            return sum + (itemSubtotal / (1 + itemTaxRate));
          } else {
            // No tax on this item
            return sum + itemSubtotal;
          }
        }, 0);
        
      case TaxMethod.AS_DEDUCTION:
        // Deduction doesn't change the subtotal
        return subtotal;
        
      default:
        return subtotal;
    }
  },

  getTotalBeforeTax: () => {
    return get().getSubtotal();
  },

  getTotal: () => {
    const state = get();
    
    if (state.taxConfig.inclusive && state.taxConfig.method !== TaxMethod.NONE && state.taxConfig.method !== TaxMethod.AS_DEDUCTION) {
      // For tax inclusive, the line item prices already include tax, so total = subtotal
      return get().getSubtotal();
    } else {
      // For tax exclusive or deductions, add/subtract tax from subtotal
      return get().getSubtotal() + get().getTaxAmount();
    }
  },

  isFormValid: () => {
    const state = get();
    return state.invoiceNumber.trim() !== '' && 
           state.selectedClientId !== undefined && 
           state.lineItems.some(item => item.description.trim() !== '');
  },

  loadInvoiceData: (invoice: any) => {
    // Convert invoice line items to store format
    const convertedLineItems: LineItem[] = invoice.lineItems?.map((item: any) => ({
      id: item.id || `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      description: item.description || '',
      itemDescription: item.itemDescription || '',
      quantity: item.quantity?.toString() || '1',
      price: item.unitPrice?.toString() || '0',
      total: item.total?.toString() || '0',
      serviceId: item.serviceId,
      unit: item.unit || 'fixed',
      taxRate: item.taxRate,
      taxable: item.taxable !== undefined ? item.taxable : true,
      selectedTaxId: item.selectedTaxId,
      discount: item.discount?.toString() || '',
      discountType: item.discountType || 'percentage',
    })) || [createEmptyLineItem()];

    // Convert tax configuration
    let taxConfig: TaxConfiguration = {
      method: TaxMethod.NONE,
      rate: 0,
      taxId: taxOptions.find(t => t.rate === 7.5)?.id || taxOptions[0].id,
      inclusive: false,
    };

    if (invoice.taxInfo?.enabled) {
      taxConfig = {
        method: TaxMethod.ON_TOTAL, // Default to on total for existing invoices
        rate: invoice.taxInfo.defaultRate || 0,
        taxId: invoice.taxInfo.taxId || taxOptions.find(t => t.rate === invoice.taxInfo.defaultRate)?.id || taxOptions[0].id,
        inclusive: invoice.taxInfo.inclusive || false,
      };
    }

    // Set all the invoice data
    set({
      invoiceNumber: invoice.invoiceNumber || 'INV-ORG-001',
      invoiceDate: invoice.issueDate ? new Date(invoice.issueDate) : new Date(),
      dueDate: invoice.dueDate ? new Date(invoice.dueDate) : defaultDueDate(),
      selectedClientId: invoice.clientId,
      notes: invoice.notes || '',
      terms: invoice.terms || 'Payment due within 30 days.',
      lineItems: convertedLineItems,
      taxConfig,
      attachments: invoice.attachments?.map((att: any) => ({
        id: att.id,
        name: att.name,
        size: att.size,
        type: att.type,
        uri: att.uri,
        uploadedAt: new Date(att.uploadedAt),
      })) || [],
      notesExpanded: false,
      termsExpanded: false,
      paymentMethods: {
        selectedGatewayId: invoice.paymentMethods?.selectedGatewayId,
        selectedCustomInstructionIds: invoice.paymentMethods?.selectedCustomInstructionIds || [],
        isManualSelected: false,
      },
    });
  },

  resetForm: () => {
    set({
      invoiceNumber: 'INV-ORG-001', // Will be set properly when form is loaded
      invoiceDate: new Date(),
      dueDate: defaultDueDate(),
      selectedClientId: undefined,
      notes: '',
      terms: 'Payment due within 30 days.',
      lineItems: [createEmptyLineItem()],
      taxConfig: {
        method: TaxMethod.NONE,
        rate: 0,
        taxId: taxOptions.find(t => t.rate === 7.5)?.id || taxOptions[0].id,
        inclusive: false,
      },
      attachments: [],
      notesExpanded: false,
      termsExpanded: false,
      paymentMethods: {
        selectedGatewayId: undefined,
        selectedCustomInstructionIds: [],
        isManualSelected: false,
      },
    });
  },

  // Method to generate next invoice number based on organization and invoice count
  generateNextInvoiceNumber: (organizationNickname: string, invoiceCount: number) => {
    const nextNumber = (invoiceCount + 1).toString().padStart(3, '0');
    const invoiceNumber = `INV-${organizationNickname}-${nextNumber}`;
    set({ invoiceNumber });
    return invoiceNumber;
  },
})); 