// Export all stores and their types
export { TaxMethod, useInvoiceStore, type LineItem, type TaxConfiguration } from './invoiceStore';
export { generateNickname, useOrganizationStore, type Organization } from './organization';
export {
    CURRENCIES, formatCurrency,
    formatCurrencyInput, getCurrencyCode, getCurrencySymbol, useSettingsStore, type UIPreferences
} from './settingsStore';

// Re-export Currency type from consolidated location
export { type Currency } from '@/defs/common';

// Export organization selectors for clean usage
export { useActiveOrganization, useActiveOrganizationId, useHasActiveOrganization } from './organization-selectors';

// Export user selectors for clean usage
export { useCurrentUser, useCurrentUserId, useIsAuthenticated } from './user-selectors';

