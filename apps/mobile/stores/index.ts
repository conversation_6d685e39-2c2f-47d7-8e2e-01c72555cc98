// Export shared stores directly from @repo/stores
export {
  // Organization store
  useOrganizationStore,
  generateNickname,

  // User store
  useUserStore,

  // Invoice store
  useInvoiceStore,
  TaxMethod,
  type LineItem,
  type TaxConfiguration,
  type InvoiceStore,
  type Attachment,
  type InvoicePaymentMethods
} from '@repo/stores';

// Export mobile-specific stores
export {
  formatCurrency,
  formatCurrencyInput,
  getCurrencyCode,
  getCurrencySymbol,
  useSettingsStore,
  type UIPreferences
} from './settingsStore';

// Export mobile-specific selectors
export * from './selectors';

// Re-export consolidated types and constants from shared packages
export { type Currency } from '@repo/schemas';
export { type Organization } from '@repo/schemas';
export { type User } from '@repo/stores';
export { CURRENCIES } from '@repo/constants';

