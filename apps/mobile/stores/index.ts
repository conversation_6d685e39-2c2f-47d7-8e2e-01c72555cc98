// Export all stores and their types
export { TaxMethod, useInvoiceStore, type LineItem, type TaxConfiguration } from './invoiceStore';
export {
  generateNickname,
  useOrganizationStore,
  type Organization as StoreOrganization
} from './organization';
export {
    formatCurrency,
    formatCurrencyInput, getCurrencyCode, getCurrencySymbol, useSettingsStore, type UIPreferences
} from './settingsStore';

// Re-export consolidated types and constants from shared packages
export { type Currency } from '@repo/schemas';
export { type Organization } from '@repo/schemas';
export { CURRENCIES } from '@repo/constants';

// Export organization selectors for clean usage
export { useActiveOrganization, useActiveOrganizationId, useHasActiveOrganization } from './organization-selectors';

// Export user selectors for clean usage
export { useCurrentUser, useCurrentUserId, useIsAuthenticated } from './user-selectors';

