import {
  useUserStore,
  initializeUserStore,
  type User
} from '@repo/stores';

// Re-export types from shared package
export type { User } from '@repo/stores';

// Mock current user for development - in production this would come from auth
const mockCurrentUser: User = {
  id: 'user_001',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  avatar: undefined,
};

// Initialize the shared store with mobile app data
initializeUserStore(
  mockCurrentUser, // In production, this would be null initially
  true // In production, this would be false initially
);

// Export the shared store instance
export { useUserStore };

// Export individual selectors for backward compatibility
export const useCurrentUserId = () =>
  useUserStore((state) => state.currentUser?.id);

export const useIsAuthenticated = () =>
  useUserStore((state) => state.isAuthenticated);

export const useCurrentUser = () =>
  useUserStore((state) => state.currentUser);

// Additional selectors using the store directly
export const useIsLoading = () =>
  useUserStore((state) => state.isLoading);

export const useUserFullName = () => {
  return useUserStore((state) => {
    const user = state.currentUser;
    return user ? `${user.firstName} ${user.lastName}` : '';
  });
};

export const useUserInitials = () => {
  return useUserStore((state) => {
    const user = state.currentUser;
    return user ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase() : '';
  });
};