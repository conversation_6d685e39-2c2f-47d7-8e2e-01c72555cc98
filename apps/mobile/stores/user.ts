import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { type User } from '@repo/stores/user';

// Re-export types from shared package
export type { User } from '@repo/stores/user';

type UserState = {
  currentUser: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
};

type UserActions = {
  setCurrentUser: (user: User | null) => void;
  updateCurrentUser: (updates: Partial<User>) => void;
  setAuthenticated: (authenticated: boolean) => void;
  setLoading: (loading: boolean) => void;
  logout: () => void;
};

// Mock current user for development - in production this would come from auth
const mockCurrentUser: User = {
  id: 'user_001',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  avatar: undefined,
};

// Create the user store using standard Zustand pattern
export const useUserStore = create<UserState & UserActions>()(
  persist(
    (set) => ({
      currentUser: mockCurrentUser, // In production, this would be null initially
      isAuthenticated: true, // In production, this would be false initially
      isLoading: false,

      setCurrentUser: (user) => {
        set({
          currentUser: user,
          isAuthenticated: !!user,
        });
      },

      updateCurrentUser: (updates) => {
        set((state) => ({
          currentUser: state.currentUser
            ? { ...state.currentUser, ...updates }
            : null
        }));
      },

      setAuthenticated: (authenticated) => {
        set({ isAuthenticated: authenticated });
      },

      setLoading: (isLoading) => set({ isLoading }),

      logout: () => {
        set({
          currentUser: null,
          isAuthenticated: false,
        });
      },
    }),
    {
      name: "user",
      partialize: (state) => ({
        currentUser: state.currentUser,
        isAuthenticated: state.isAuthenticated,
      })
    },
  ),
);

// Export individual selectors for backward compatibility
export const useCurrentUserId = () =>
  useUserStore((state) => state.currentUser?.id);

export const useIsAuthenticated = () =>
  useUserStore((state) => state.isAuthenticated);

export const useCurrentUser = () =>
  useUserStore((state) => state.currentUser);

// Additional selectors using the store directly
export const useIsLoading = () =>
  useUserStore((state) => state.isLoading);

export const useUserFullName = () => {
  return useUserStore((state) => {
    const user = state.currentUser;
    return user ? `${user.firstName} ${user.lastName}` : '';
  });
};

export const useUserInitials = () => {
  return useUserStore((state) => {
    const user = state.currentUser;
    return user ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase() : '';
  });
};