import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  avatar?: string;
}

type UserState = {
  currentUser: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
};

type UserActions = {
  setCurrentUser: (user: User | null) => void;
  updateCurrentUser: (updates: Partial<User>) => void;
  setAuthenticated: (authenticated: boolean) => void;
  setLoading: (loading: boolean) => void;
  logout: () => void;
};

// Mock current user for development - in production this would come from auth
const mockCurrentUser: User = {
  id: 'user_001',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  avatar: undefined,
};

export const useUserStore = create<UserState & UserActions>()(
  persist(
    (set, get) => ({
      currentUser: mockCurrentUser, // In production, this would be null initially
      isAuthenticated: true, // In production, this would be false initially
      isLoading: false,
      
      setCurrentUser: (user) => {
        set({ 
          currentUser: user,
          isAuthenticated: !!user,
        });
      },
      
      updateCurrentUser: (updates) => {
        set((state) => ({
          currentUser: state.currentUser 
            ? { ...state.currentUser, ...updates }
            : null
        }));
      },
      
      setAuthenticated: (authenticated) => {
        set({ isAuthenticated: authenticated });
      },
      
      setLoading: (isLoading) => set({ isLoading }),
      
      logout: () => {
        set({ 
          currentUser: null,
          isAuthenticated: false,
        });
      },
    }),
    { 
      name: "user",
      partialize: (state) => ({ 
        currentUser: state.currentUser,
        isAuthenticated: state.isAuthenticated,
      })
    },
  ),
);

// Computed selectors for clean usage
export const useCurrentUserId = () => 
  useUserStore((state) => state.currentUser?.id);

export const useIsAuthenticated = () => 
  useUserStore((state) => state.isAuthenticated);

export const useCurrentUser = () =>
  useUserStore((state) => state.currentUser); 