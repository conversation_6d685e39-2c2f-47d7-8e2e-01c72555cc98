import { dashboardData } from '@/constants/data';
import { queryClient } from '@/core/query-client';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Organization as FullOrganization } from '@/defs/organization';

// Simplified Organization type for store compatibility (subset of full Organization)
export interface Organization {
  id: string;
  name: string;
  nickname: string;
  logo: string | null;
  description?: string;
  isDefault?: boolean;
}

// Helper to convert full Organization to store Organization
export const toStoreOrganization = (org: FullOrganization): Organization => ({
  id: org.id,
  name: org.name,
  nickname: org.nickname,
  logo: org.logo,
  description: org.description,
  isDefault: false, // This field doesn't exist in full schema
});

// Dashboard data interfaces (moved from old store)
interface Activity {
  id: string;
  title: string;
  date: string;
  amount: string;
  type: string;
  vendor: string;
  avatar: string | null;
  company?: string;
}

interface OrganizationDashboardData {
  invoiceStats: {
    pending: { count: number; amount: string };
    paid: { count: number; amount: string };
    overdue: { count: number; amount: string };
  };
  summary: {
    revenue: { value: string; percentChange: number };
    totalInvoices: { value: string; percentChange: number };
  };
  recentActivity: Activity[];
}

type OrganizationState = {
  activeOrganization: Organization | null;
  organizations: Organization[];
  dashboardData: { [key: string]: OrganizationDashboardData };
  isLoading: boolean;
};

type OrganizationActions = {
  setActiveOrganization: (organization: Organization | null) => void;
  setOrganizations: (organizations: Organization[]) => void;
  addOrganization: (organization: Organization) => void;
  updateOrganization: (id: string, updates: Partial<Organization>) => void;
  removeOrganization: (id: string) => void;
  setLoading: (loading: boolean) => void;
  
  // Legacy methods for compatibility
  getOrganizations: () => Organization[];
  getOrganizationById: (organizationId: string) => Organization | undefined;
  getDashboardData: (organizationId: string) => OrganizationDashboardData;
  deleteOrganization: (organizationId: string) => void;
};

// Helper function to generate nickname from organization name
export const generateNickname = (name: string): string => {
  const words = name
    .replace(/[^\w\s]/g, '') // Remove special characters
    .split(/\s+/) // Split by whitespace
    .filter(word => word.length > 0);
  
  if (words.length === 1) {
    // Single word: take first 4 characters
    return words[0].slice(0, 4).toUpperCase();
  } else if (words.length <= 4) {
    // Multiple words (up to 4): take first letter of each
    return words.map(word => word[0]).join('').toUpperCase();
  } else {
    // More than 4 words: take first letter of first 4 words
    return words.slice(0, 4).map(word => word[0]).join('').toUpperCase();
  }
};

// Create default dashboard data for new organizations
const createDefaultDashboardData = (): OrganizationDashboardData => ({
  invoiceStats: {
    pending: { count: 0, amount: '$0' },
    paid: { count: 0, amount: '$0' },
    overdue: { count: 0, amount: '$0' },
  },
  summary: {
    revenue: { value: '$0', percentChange: 0 },
    totalInvoices: { value: '0', percentChange: 0 },
  },
  recentActivity: [],
});

// Initialize with existing data for backward compatibility
const initialOrganizations: Organization[] = dashboardData.user.companies.map(company => ({
  id: company.id,
  name: company.name,
  nickname: company.nickname,
  logo: company.logo,
  isDefault: company.isDefault
}));

const initialActiveOrganization = initialOrganizations.find(org => org.id === dashboardData.user.activeCompanyId) || null;

export const useOrganizationStore = create<OrganizationState & OrganizationActions>()(
  persist(
    (set, get) => ({
      activeOrganization: initialActiveOrganization,
      organizations: initialOrganizations,
      dashboardData: dashboardData.companyData,
      isLoading: false,
      
      setActiveOrganization: (organization) => {
        set({ activeOrganization: organization });
        
        // Clear cache when switching organizations
        try {
          queryClient.removeQueries({ queryKey: ['invoices'] });
          queryClient.removeQueries({ queryKey: ['clients'] });
          queryClient.removeQueries({ queryKey: ['services'] });
        } catch (error) {
          // QueryClient might not be initialized yet (during app startup)
          console.warn('QueryClient not available for cache invalidation:', error);
        }
      },
      
      setOrganizations: (organizations) => {
        set({ organizations });
        // Auto-select first organization if none selected
        const current = get();
        if (!current.activeOrganization && organizations.length > 0) {
          current.setActiveOrganization(organizations[0]);
        }
      },
      
      addOrganization: (organization) => {
        set((state) => ({
          organizations: [...state.organizations, organization],
          activeOrganization: state.activeOrganization || organization,
          dashboardData: {
            ...state.dashboardData,
            [organization.id]: createDefaultDashboardData(),
          },
        }));
      },
      
      updateOrganization: (id, updates) => {
        set((state) => ({
          organizations: state.organizations.map(org =>
            org.id === id ? { ...org, ...updates } : org
          ),
          activeOrganization: state.activeOrganization?.id === id
            ? { ...state.activeOrganization, ...updates }
            : state.activeOrganization
        }));
      },
      
      removeOrganization: (id) => {
        set((state) => {
          const newOrganizations = state.organizations.filter(org => org.id !== id);
          const newActiveOrganization = state.activeOrganization?.id === id
            ? newOrganizations[0] || null
            : state.activeOrganization;
          
          const { [id]: deletedData, ...remainingDashboardData } = state.dashboardData;
          
          return {
            organizations: newOrganizations,
            activeOrganization: newActiveOrganization,
            dashboardData: remainingDashboardData,
          };
        });
      },
      
      setLoading: (isLoading) => set({ isLoading }),
      
      // Legacy methods for backward compatibility
      getOrganizations: () => {
        return get().organizations;
      },
      
      getOrganizationById: (organizationId: string) => {
        return get().organizations.find(org => org.id === organizationId);
      },
      
      getDashboardData: (organizationId: string) => {
        const dashboardData = get().dashboardData[organizationId];
        return dashboardData || createDefaultDashboardData();
      },
      
      deleteOrganization: (organizationId: string) => {
        get().removeOrganization(organizationId);
      },
    }),
    { 
      name: "organization",
      partialize: (state) => ({ 
        activeOrganization: state.activeOrganization,
        organizations: state.organizations,
        dashboardData: state.dashboardData,
      })
    },
  ),
);

// Computed selectors for clean usage
export const useActiveOrganizationId = () => 
  useOrganizationStore((state) => state.activeOrganization?.id);

export const useHasActiveOrganization = () => 
  useOrganizationStore((state) => !!state.activeOrganization);

export const useActiveOrganization = () =>
  useOrganizationStore((state) => state.activeOrganization); 