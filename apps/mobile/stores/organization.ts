import { dashboardData, taxOptions } from '@/constants/data';
import { queryClient } from '@/core/query-client';
import {
  useOrganizationStore,
  initializeOrganizationStore,
  setCacheInvalidationCallback,
  initializeInvoiceStore,
  type Organization
} from '@repo/stores';

// Re-export types and utilities from shared package
export type { Organization } from '@repo/stores';
export { toStoreOrganization, generateNickname } from '@repo/stores';

// Initialize with existing data for backward compatibility
const initialOrganizations: Organization[] = dashboardData.user.companies.map(company => ({
  id: company.id,
  name: company.name,
  nickname: company.nickname,
  logo: company.logo,
  isDefault: company.isDefault
}));

const initialActiveOrganization = initialOrganizations.find(org => org.id === dashboardData.user.activeCompanyId) || null;

// Set up cache invalidation callback for mobile app
setCacheInvalidationCallback(() => {
  try {
    queryClient.removeQueries({ queryKey: ['invoices'] });
    queryClient.removeQueries({ queryKey: ['clients'] });
    queryClient.removeQueries({ queryKey: ['services'] });
  } catch (error) {
    // QueryClient might not be initialized yet (during app startup)
    console.warn('QueryClient not available for cache invalidation:', error);
  }
});

// Initialize the shared store with mobile app data
initializeOrganizationStore({
  organizations: initialOrganizations,
  activeOrganization: initialActiveOrganization,
  dashboardData: dashboardData.companyData,
});

// Initialize the invoice store with mobile app's tax options
const simplifiedTaxOptions = taxOptions.map(option => ({
  id: option.id,
  name: option.name,
  rate: option.rate,
}));
initializeInvoiceStore(simplifiedTaxOptions);

// Export the shared store instance
export { useOrganizationStore };

// Export individual selectors for backward compatibility
export const useActiveOrganizationId = () =>
  useOrganizationStore((state) => state.activeOrganization?.id);

export const useHasActiveOrganization = () =>
  useOrganizationStore((state) => !!state.activeOrganization);

export const useActiveOrganization = () =>
  useOrganizationStore((state) => state.activeOrganization);

// Additional selectors using the store directly
export const useOrganizations = () =>
  useOrganizationStore((state) => state.organizations);

export const useIsLoading = () =>
  useOrganizationStore((state) => state.isLoading);

export const useOrganizationById = (id: string) =>
  useOrganizationStore((state) => state.organizations.find(org => org.id === id));

export const useDashboardData = (organizationId?: string) => {
  return useOrganizationStore((state) => {
    const id = organizationId || state.activeOrganization?.id;
    return id ? state.getDashboardData(id) : null;
  });
};

export const useActiveDashboardData = () => {
  return useOrganizationStore((state) => {
    const activeId = state.activeOrganization?.id;
    return activeId ? state.getDashboardData(activeId) : null;
  });
};