import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { createJSONStorage, persist, subscribeWithSelector } from 'zustand/middleware';
import { Currency } from '@repo/schemas';
import { CURRENCIES } from '@repo/constants';
import {
  formatCurrency as formatCurrencyUtil,
  formatCurrencyInput as formatCurrencyInputUtil,
  getCurrencyByCode,
  getCurrencySymbol as getCurrencySymbolUtil,
  getCurrencyCode as getCurrencyCodeUtil
} from '@repo/utils';

// UI preferences for the invoice creation screen
export interface UIPreferences {
  // Collapsible section states
  isItemsCollapsed: boolean;
  isTaxCollapsed: boolean;
  
  // Footer behavior
  isFooterPinned: boolean;
  
  // Other UI preferences can be added here
  // e.g., theme preference, default view mode, etc.
}

// Predefined currencies - can be expanded
export const CURRENCIES: Currency[] = [
  {
    code: 'USD',
    symbol: '$',
    name: 'US Dollar',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'EUR',
    symbol: '€',
    name: 'Euro',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'GBP',
    symbol: '£',
    name: 'British Pound',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'CAD',
    symbol: 'C$',
    name: 'Canadian Dollar',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'AUD',
    symbol: 'A$',
    name: 'Australian Dollar',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'JPY',
    symbol: '¥',
    name: 'Japanese Yen',
    decimalPlaces: 0,
    symbolPosition: 'before',
  },
  {
    code: 'CNY',
    symbol: '¥',
    name: 'Chinese Yuan',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'INR',
    symbol: '₹',
    name: 'Indian Rupee',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'KRW',
    symbol: '₩',
    name: 'South Korean Won',
    decimalPlaces: 0,
    symbolPosition: 'before',
  },
  {
    code: 'BRL',
    symbol: 'R$',
    name: 'Brazilian Real',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'MXN',
    symbol: '$',
    name: 'Mexican Peso',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
  {
    code: 'CHF',
    symbol: 'CHF',
    name: 'Swiss Franc',
    decimalPlaces: 2,
    symbolPosition: 'before',
  },
];

// Currency utility functions
export const getCurrencyByCode = (code: string): Currency | undefined => {
  return CURRENCIES.find(currency => currency.code === code);
};

export const getCurrencySymbol = (code: string): string => {
  const currency = getCurrencyByCode(code);
  return currency?.symbol || '$';
};

export const getCurrencyCode = (code?: string): string => {
  return code || 'USD';
};

export const formatCurrency = (amount: number, currencyCode: string = 'USD'): string => {
  const currency = getCurrencyByCode(currencyCode);
  if (!currency) {
    return `$${amount.toFixed(2)}`;
  }
  
  const formattedAmount = amount.toFixed(currency.decimalPlaces);
  
  if (currency.symbolPosition === 'before') {
    return `${currency.symbol}${formattedAmount}`;
  } else {
    return `${formattedAmount}${currency.symbol}`;
  }
};

export const formatCurrencyInput = (value: string, currencyCode: string = 'USD'): string => {
  const currency = getCurrencyByCode(currencyCode);
  const decimalPlaces = currency?.decimalPlaces || 2;
  
  // Remove non-numeric characters except decimal point
  const numericValue = value.replace(/[^0-9.]/g, '');
  
  // Ensure only one decimal point
  const parts = numericValue.split('.');
  if (parts.length > 2) {
    return `${parts[0]}.${parts.slice(1).join('')}`;
  }
  
  // Limit decimal places
  if (parts.length === 2 && parts[1].length > decimalPlaces) {
    return `${parts[0]}.${parts[1].substring(0, decimalPlaces)}`;
  }
  
  return numericValue;
};

// Settings UI State Interface
interface SettingsState {
  // Navigation & UI State
  activeSection: SettingsSection;
  previousSection: SettingsSection | null;
  isLoading: boolean;
  hasUnsavedChanges: boolean;
  lastSavedAt: Date | null;
  
  // Modal States
  activeModal: SettingsModal | null;
  modalData: any;
  
  // Form States
  formErrors: Record<string, string>;
  fieldValidation: Record<string, boolean>;
  touchedFields: Set<string>;
  
  // Feature Flags & Permissions
  availableFeatures: SettingsFeature[];
  subscriptionLimits: SubscriptionLimits | null;
  
  // UI Preferences for invoice creation and other screens
  uiPreferences: UIPreferences;
  
  // User Preferences (Cross-component state)
  viewPreferences: {
    compactMode: boolean;
    showHelpText: boolean;
    animationsEnabled: boolean;
    autoSave: boolean;
  };
  
  // Search & Filter State
  searchQuery: string;
  searchResults: SearchResult[];
  searchFilters: SearchFilters;
  
  // Actions
  setActiveSection: (section: SettingsSection) => void;
  navigateBack: () => void;
  
  // Modal Actions
  openModal: (modal: SettingsModal, data?: any) => void;
  closeModal: () => void;
  
  // Form Actions  
  setFormError: (field: string, error: string) => void;
  clearFormError: (field: string) => void;
  clearAllErrors: () => void;
  setFieldValidation: (field: string, isValid: boolean) => void;
  markFieldTouched: (field: string) => void;
  resetFormState: () => void;
  
  // Loading & Save State
  setLoading: (isLoading: boolean) => void;
  markUnsavedChanges: (hasChanges: boolean) => void;
  markSaved: () => void;
  
  // View Preferences
  updateViewPreferences: (preferences: Partial<SettingsState['viewPreferences']>) => void;
  
  // Search Actions
  setSearchQuery: (query: string) => void;
  setSearchResults: (results: SearchResult[]) => void;
  updateSearchFilters: (filters: Partial<SearchFilters>) => void;
  clearSearch: () => void;
  
  // Feature Management
  setAvailableFeatures: (features: SettingsFeature[]) => void;
  setSubscriptionLimits: (limits: SubscriptionLimits) => void;
  
  // UI Preferences Actions
  setItemsCollapsed: (collapsed: boolean) => void;
  setTaxCollapsed: (collapsed: boolean) => void;
  setFooterPinned: (pinned: boolean) => void;
  updateUIPreferences: (preferences: Partial<UIPreferences>) => void;
  
  // Reset Actions
  resetSection: () => void;
  resetAll: () => void;
}

// Types
export type SettingsSection = 
  | 'account'
  | 'subscription' 
  | 'invoice-design'
  | 'defaults'
  | 'help'
  | 'feedback'
  | 'rate-app';

export type SettingsModal =
  | 'avatar-upload'
  | 'password-change'
  | 'two-factor-setup'
  | 'subscription-change'
  | 'subscription-cancel'
  | 'template-preview'
  | 'template-editor'
  | 'color-picker'
  | 'feedback-form'
  | 'help-article'
  | 'confirmation'
  | 'success';

export type SettingsFeature =
  | 'custom-templates'
  | 'advanced-branding'
  | 'api-access'
  | 'integrations'
  | 'white-label'
  | 'priority-support'
  | 'custom-fields'
  | 'advanced-reporting';

export interface SubscriptionLimits {
  organizations: number;
  invoices: number;
  clients: number;
  storage: number; // in MB
  templates: number;
  customFields: number;
}

export interface SearchResult {
  id: string;
  title: string;
  excerpt: string;
  type: 'article' | 'faq' | 'setting';
  category: string;
  relevanceScore: number;
}

export interface SearchFilters {
  category: string | null;
  difficulty: 'beginner' | 'intermediate' | 'advanced' | null;
  type: 'article' | 'faq' | 'setting' | null;
}

// Initial State
const initialState = {
  activeSection: 'account' as SettingsSection,
  previousSection: null,
  isLoading: false,
  hasUnsavedChanges: false,
  lastSavedAt: null,
  
  activeModal: null,
  modalData: null,
  
  formErrors: {},
  fieldValidation: {},
  touchedFields: new Set<string>(),
  
  availableFeatures: [],
  subscriptionLimits: null,
  
  uiPreferences: {
    isItemsCollapsed: false,
    isTaxCollapsed: false,
    isFooterPinned: false,
  },
  
  viewPreferences: {
    compactMode: false,
    showHelpText: true,
    animationsEnabled: true,
    autoSave: true,
  },
  
  searchQuery: '',
  searchResults: [],
  searchFilters: {
    category: null,
    difficulty: null,
    type: null,
  },
};

// Store
export const useSettingsStore = create<SettingsState>()(
  persist(
    subscribeWithSelector((set, get) => ({
      ...initialState,
      
      // Navigation Actions
      setActiveSection: (section) => {
        const currentSection = get().activeSection;
        set({ 
          previousSection: currentSection,
          activeSection: section,
          // Clear section-specific state when navigating
          formErrors: {},
          fieldValidation: {},
          touchedFields: new Set(),
          hasUnsavedChanges: false,
        });
      },
      
      navigateBack: () => {
        const { previousSection } = get();
        if (previousSection) {
          set({ 
            activeSection: previousSection,
            previousSection: null,
          });
        }
      },
      
      // Modal Actions
      openModal: (modal, data = null) => {
        set({ 
          activeModal: modal,
          modalData: data,
        });
      },
      
      closeModal: () => {
        set({ 
          activeModal: null,
          modalData: null,
        });
      },
      
      // Form State Management
      setFormError: (field, error) => {
        set((state) => ({
          formErrors: {
            ...state.formErrors,
            [field]: error,
          },
          fieldValidation: {
            ...state.fieldValidation,
            [field]: false,
          },
        }));
      },
      
      clearFormError: (field) => {
        set((state) => {
          const newErrors = { ...state.formErrors };
          delete newErrors[field];
          
          const newValidation = { ...state.fieldValidation };
          delete newValidation[field];
          
          return {
            formErrors: newErrors,
            fieldValidation: newValidation,
          };
        });
      },
      
      clearAllErrors: () => {
        set({
          formErrors: {},
          fieldValidation: {},
        });
      },
      
      setFieldValidation: (field, isValid) => {
        set((state) => ({
          fieldValidation: {
            ...state.fieldValidation,
            [field]: isValid,
          },
        }));
      },
      
      markFieldTouched: (field) => {
        set((state) => ({
          touchedFields: new Set([...state.touchedFields, field]),
        }));
      },
      
      resetFormState: () => {
        set({
          formErrors: {},
          fieldValidation: {},
          touchedFields: new Set(),
          hasUnsavedChanges: false,
        });
      },
      
      // Loading & Save State
      setLoading: (isLoading) => {
        set({ isLoading });
      },
      
      markUnsavedChanges: (hasChanges) => {
        set({ hasUnsavedChanges: hasChanges });
      },
      
      markSaved: () => {
        set({ 
          hasUnsavedChanges: false,
          lastSavedAt: new Date(),
        });
      },
      
      // View Preferences
      updateViewPreferences: (preferences) => {
        set((state) => ({
          viewPreferences: {
            ...state.viewPreferences,
            ...preferences,
          },
        }));
      },
      
      // Search Actions
      setSearchQuery: (query) => {
        set({ searchQuery: query });
      },
      
      setSearchResults: (results) => {
        set({ searchResults: results });
      },
      
      updateSearchFilters: (filters) => {
        set((state) => ({
          searchFilters: {
            ...state.searchFilters,
            ...filters,
          },
        }));
      },
      
      clearSearch: () => {
        set({
          searchQuery: '',
          searchResults: [],
          searchFilters: {
            category: null,
            difficulty: null,
            type: null,
          },
        });
      },
      
      // Feature Management
      setAvailableFeatures: (features) => {
        set({ availableFeatures: features });
      },
      
      setSubscriptionLimits: (limits) => {
        set({ subscriptionLimits: limits });
      },
      
      // UI Preferences Actions
      setItemsCollapsed: (collapsed) => {
        set((state) => ({
          uiPreferences: {
            ...state.uiPreferences,
            isItemsCollapsed: collapsed,
          },
        }));
      },
      
      setTaxCollapsed: (collapsed) => {
        set((state) => ({
          uiPreferences: {
            ...state.uiPreferences,
            isTaxCollapsed: collapsed,
          },
        }));
      },
      
      setFooterPinned: (pinned) => {
        set((state) => ({
          uiPreferences: {
            ...state.uiPreferences,
            isFooterPinned: pinned,
          },
        }));
      },
      
      updateUIPreferences: (preferences) => {
        set((state) => ({
          uiPreferences: {
            ...state.uiPreferences,
            ...preferences,
          },
        }));
      },
      
      // Reset Actions
      resetSection: () => {
        set({
          formErrors: {},
          fieldValidation: {},
          touchedFields: new Set(),
          hasUnsavedChanges: false,
          activeModal: null,
          modalData: null,
        });
      },
      
      resetAll: () => {
        set({
          ...initialState,
          touchedFields: new Set(),
          searchFilters: {
            category: null,
            difficulty: null,
            type: null,
          },
        });
      },
    })),
    {
      name: 'settings-storage',
      storage: createJSONStorage(() => AsyncStorage),
      // Only persist certain values, not all UI state
      partialize: (state) => ({
        viewPreferences: state.viewPreferences,
        searchFilters: state.searchFilters,
        availableFeatures: state.availableFeatures,
        subscriptionLimits: state.subscriptionLimits,
      }),
    }
  )
);

// ==============================================
// COMPUTED SELECTORS - Clean, no parameters
// ==============================================

// Navigation Selectors
export const useCurrentSection = () => useSettingsStore(state => state.activeSection);
export const useCanNavigateBack = () => useSettingsStore(state => !!state.previousSection);
export const useIsLoading = () => useSettingsStore(state => state.isLoading);

// Modal Selectors
export const useActiveModal = () => useSettingsStore(state => ({
  modal: state.activeModal,
  data: state.modalData,
}));

export const useIsModalOpen = () => useSettingsStore(state => !!state.activeModal);

// Form State Selectors
export const useFormErrors = () => useSettingsStore(state => state.formErrors);
export const useHasFormErrors = () => useSettingsStore(state => Object.keys(state.formErrors).length > 0);
export const useFieldValidation = () => useSettingsStore(state => state.fieldValidation);
export const useTouchedFields = () => useSettingsStore(state => state.touchedFields);

export const useFormState = () => useSettingsStore(state => ({
  errors: state.formErrors,
  validation: state.fieldValidation,
  touchedFields: state.touchedFields,
  hasErrors: Object.keys(state.formErrors).length > 0,
  hasUnsavedChanges: state.hasUnsavedChanges,
  lastSavedAt: state.lastSavedAt,
}));

// Save State Selectors
export const useHasUnsavedChanges = () => useSettingsStore(state => state.hasUnsavedChanges);
export const useLastSavedAt = () => useSettingsStore(state => state.lastSavedAt);

export const useSaveState = () => useSettingsStore(state => ({
  hasUnsavedChanges: state.hasUnsavedChanges,
  lastSavedAt: state.lastSavedAt,
  isLoading: state.isLoading,
}));

// Feature & Subscription Selectors
export const useAvailableFeatures = () => useSettingsStore(state => state.availableFeatures);
export const useSubscriptionLimits = () => useSettingsStore(state => state.subscriptionLimits);

export const useFeatureAccess = () => useSettingsStore(state => ({
  features: state.availableFeatures,
  limits: state.subscriptionLimits,
  hasFeature: (feature: SettingsFeature) => state.availableFeatures.includes(feature),
}));

// View Preferences Selectors
export const useViewPreferences = () => useSettingsStore(state => state.viewPreferences);
export const useCompactMode = () => useSettingsStore(state => state.viewPreferences.compactMode);
export const useShowHelpText = () => useSettingsStore(state => state.viewPreferences.showHelpText);
export const useAnimationsEnabled = () => useSettingsStore(state => state.viewPreferences.animationsEnabled);
export const useAutoSave = () => useSettingsStore(state => state.viewPreferences.autoSave);

// Search Selectors
export const useSearchState = () => useSettingsStore(state => ({
  query: state.searchQuery,
  results: state.searchResults,
  filters: state.searchFilters,
  hasResults: state.searchResults.length > 0,
  isSearching: state.searchQuery.length > 0,
}));

export const useSearchQuery = () => useSettingsStore(state => state.searchQuery);
export const useSearchResults = () => useSettingsStore(state => state.searchResults);
export const useSearchFilters = () => useSettingsStore(state => state.searchFilters);

// Computed UI State Selectors
export const useSettingsNavigation = () => useSettingsStore(state => ({
  activeSection: state.activeSection,
  canGoBack: !!state.previousSection,
  hasUnsavedChanges: state.hasUnsavedChanges,
  isLoading: state.isLoading,
}));

export const useSettingsModal = () => useSettingsStore(state => ({
  isOpen: !!state.activeModal,
  modal: state.activeModal,
  data: state.modalData,
}));

// Section-specific computed selectors
export const useAccountSectionState = () => useSettingsStore(state => ({
  isActive: state.activeSection === 'account',
  hasErrors: Object.keys(state.formErrors).length > 0,
  hasUnsavedChanges: state.hasUnsavedChanges,
  isLoading: state.isLoading,
}));

export const useSubscriptionSectionState = () => useSettingsStore(state => ({
  isActive: state.activeSection === 'subscription',
  features: state.availableFeatures,
  limits: state.subscriptionLimits,
  hasUnsavedChanges: state.hasUnsavedChanges,
  isLoading: state.isLoading,
}));

export const useInvoiceDesignSectionState = () => useSettingsStore(state => ({
  isActive: state.activeSection === 'invoice-design',
  hasUnsavedChanges: state.hasUnsavedChanges,
  isLoading: state.isLoading,
  compactMode: state.viewPreferences.compactMode,
  animationsEnabled: state.viewPreferences.animationsEnabled,
}));

export const useDefaultsSectionState = () => useSettingsStore(state => ({
  isActive: state.activeSection === 'defaults',
  hasErrors: Object.keys(state.formErrors).length > 0,
  hasUnsavedChanges: state.hasUnsavedChanges,
  isLoading: state.isLoading,
  autoSave: state.viewPreferences.autoSave,
}));

export const useHelpSectionState = () => useSettingsStore(state => ({
  isActive: state.activeSection === 'help',
  searchQuery: state.searchQuery,
  searchResults: state.searchResults,
  searchFilters: state.searchFilters,
  hasResults: state.searchResults.length > 0,
  showHelpText: state.viewPreferences.showHelpText,
}));

export const useFeedbackSectionState = () => useSettingsStore(state => ({
  isActive: state.activeSection === 'feedback',
  hasErrors: Object.keys(state.formErrors).length > 0,
  touchedFields: state.touchedFields,
  isLoading: state.isLoading,
}));

// Actions Selectors (for easy destructuring)
export const useSettingsActions = () => useSettingsStore(state => ({
  setActiveSection: state.setActiveSection,
  navigateBack: state.navigateBack,
  openModal: state.openModal,
  closeModal: state.closeModal,
  setLoading: state.setLoading,
  markUnsavedChanges: state.markUnsavedChanges,
  markSaved: state.markSaved,
  resetSection: state.resetSection,
  resetAll: state.resetAll,
}));

export const useFormActions = () => useSettingsStore(state => ({
  setFormError: state.setFormError,
  clearFormError: state.clearFormError,
  clearAllErrors: state.clearAllErrors,
  setFieldValidation: state.setFieldValidation,
  markFieldTouched: state.markFieldTouched,
  resetFormState: state.resetFormState,
}));

export const useSearchActions = () => useSettingsStore(state => ({
  setSearchQuery: state.setSearchQuery,
  setSearchResults: state.setSearchResults,
  updateSearchFilters: state.updateSearchFilters,
  clearSearch: state.clearSearch,
}));

export const useViewActions = () => useSettingsStore(state => ({
  updateViewPreferences: state.updateViewPreferences,
  setAvailableFeatures: state.setAvailableFeatures,
  setSubscriptionLimits: state.setSubscriptionLimits,
}));

// Utility selector for debugging
export const useSettingsDebug = () => useSettingsStore(state => ({
  activeSection: state.activeSection,
  previousSection: state.previousSection,
  isLoading: state.isLoading,
  hasUnsavedChanges: state.hasUnsavedChanges,
  activeModal: state.activeModal,
  formErrors: state.formErrors,
  searchQuery: state.searchQuery,
  availableFeatures: state.availableFeatures,
})); 