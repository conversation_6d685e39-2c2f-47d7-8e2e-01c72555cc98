{"name": "mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"dev": "expo start", "start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo export", "lint": "eslint . --max-warnings 0"}, "dependencies": {"@babel/runtime": "^7.27.6", "@repo/shared": "workspace:*", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "expo": "~53.0.11", "expo-modules-core": "^2.4.0", "expo-status-bar": "~2.2.3", "nativewind": "^2.0.11", "react": "19.0.0", "react-native": "0.79.3", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.14.0", "react-native-screens": "~4.1.0", "tailwind-merge": "^2.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/react": "~19.0.10", "eslint": "catalog:", "tailwindcss": "catalog:", "typescript": "catalog:"}, "private": true}