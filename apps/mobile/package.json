{"name": "mobile", "main": "index.ts", "version": "1.0.0", "scripts": {"dev": "expo run:android", "start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo export", "lint": "eslint . --max-warnings 0", "build:android:prod": "NODE_ENV=production pnpx expo prebuild --clean -p android && eas build --platform android --profile production --local", "build:ios:prod": "NODE_ENV=production pnpx expo prebuild --clean -p ios && eas build --platform ios --profile production --local"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@repo/shared": "workspace:*", "@repo/schemas": "workspace:*", "@repo/dtos": "workspace:*", "@repo/utils": "workspace:*", "@repo/constants": "workspace:*", "@repo/stores": "workspace:*", "@repo/queries": "workspace:*", "@tanstack/react-query": "^5.79.0", "expo": "~53.0.11", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-document-picker": "~13.1.5", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.2.1", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-router": "~5.0.6", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "lodash": "^4.17.21", "react": "catalog:", "react-dom": "catalog:", "react-native": "0.79.3", "react-native-gesture-handler": "~2.24.0", "react-native-keyboard-controller": "^1.17.3", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "zod": "^3.25.42", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/lodash": "^4.17.17", "@types/react": "catalog:", "eslint": "catalog:", "eslint-config-expo": "~9.2.0", "typescript": "^5.8.3"}, "private": true}