{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-jsx", "lib": ["es2017"], "moduleResolution": "bundler", "noEmit": true, "target": "esnext", "resolveJsonModule": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@repo/shared": ["../../packages/shared/src/index"], "@repo/shared/*": ["../../packages/shared/src/*"]}}, "include": ["**/*.ts", "**/*.tsx"]}