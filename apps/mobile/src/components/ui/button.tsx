import * as React from "react";
import { Pressable, Text, StyleSheet, View, type PressableProps } from "react-native";

type ButtonVariant = "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
type ButtonSize = "default" | "sm" | "lg" | "icon";

export interface ButtonProps extends PressableProps {
  children: React.ReactNode;
  variant?: ButtonVariant;
  size?: ButtonSize;
}

const Button = React.forwardRef<View, ButtonProps>(
  ({ variant = "default", size = "default", children, style, ...props }, ref) => {
    const buttonStyle = [
      styles.base,
      styles[`variant_${variant}` as keyof typeof styles],
      styles[`size_${size}` as keyof typeof styles],
      style,
    ];

    const textStyle = [
      styles.text,
      styles[`text_${variant}` as keyof typeof styles],
    ];

    return (
      <Pressable
        style={buttonStyle as any}
        ref={ref}
        {...props}
      >
        <Text style={textStyle}>
          {children}
        </Text>
      </Pressable>
    );
  }
);
Button.displayName = "Button";

const styles = StyleSheet.create({
  base: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 6,
    paddingHorizontal: 16,
    paddingVertical: 8,
    minHeight: 40,
  },
  // Variants
  variant_default: {
    backgroundColor: '#18181b', // zinc-900
  },
  variant_destructive: {
    backgroundColor: '#dc2626', // red-600
  },
  variant_outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#e4e4e7', // zinc-200
  },
  variant_secondary: {
    backgroundColor: '#f4f4f5', // zinc-100
  },
  variant_ghost: {
    backgroundColor: 'transparent',
  },
  variant_link: {
    backgroundColor: 'transparent',
  },
  // Sizes
  size_default: {
    minHeight: 40,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  size_sm: {
    minHeight: 36,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  size_lg: {
    minHeight: 44,
    paddingHorizontal: 32,
    paddingVertical: 12,
  },
  size_icon: {
    width: 40,
    height: 40,
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  // Text styles
  text: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  text_default: {
    color: '#fafafa', // zinc-50
  },
  text_destructive: {
    color: '#fafafa', // zinc-50
  },
  text_outline: {
    color: '#18181b', // zinc-900
  },
  text_secondary: {
    color: '#18181b', // zinc-900
  },
  text_ghost: {
    color: '#18181b', // zinc-900
  },
  text_link: {
    color: '#3b82f6', // blue-500
    textDecorationLine: 'underline',
  },
});

export { Button };
