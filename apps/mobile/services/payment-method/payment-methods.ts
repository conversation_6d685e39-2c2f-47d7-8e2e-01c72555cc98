import { getApiProvider } from '@/core/providers/provider-factory';
import { CustomPaymentInstruction, OrganizationPaymentMethods } from '@/defs/payment-method';
import { useActiveOrganizationId } from '@/stores';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

const QUERY_KEYS = {
  organizationPaymentMethods: (orgId: string) => ['organizationPaymentMethods', orgId],
  customPaymentInstructions: (orgId: string) => ['customPaymentInstructions', orgId],
};

// Hook to get organization payment methods configuration
export function useOrganizationPaymentMethods() {
  const organizationId = useActiveOrganizationId();
  
  return useQuery({
    queryKey: QUERY_KEYS.organizationPaymentMethods(organizationId || ''),
    queryFn: async (): Promise<OrganizationPaymentMethods | null> => {
      if (!organizationId) return null;
      
      const provider = getApiProvider();
      return provider.getOrganizationPaymentMethods(organizationId);
    },
    enabled: !!organizationId,
  });
}

// Hook to get custom payment instructions for organization
export function useCustomPaymentInstructions() {
  const organizationId = useActiveOrganizationId();
  
  return useQuery({
    queryKey: QUERY_KEYS.customPaymentInstructions(organizationId || ''),
    queryFn: async (): Promise<CustomPaymentInstruction[]> => {
      if (!organizationId) return [];
      
      const provider = getApiProvider();
      return provider.getCustomPaymentInstructions(organizationId);
    },
    enabled: !!organizationId,
  });
}

// Hook to create custom payment instruction
export function useCreateCustomPaymentInstruction() {
  const queryClient = useQueryClient();
  const organizationId = useActiveOrganizationId();

  return useMutation({
    mutationFn: async (data: { title: string; instructions: string }) => {
      if (!organizationId) throw new Error('No organization selected');
      
      const provider = getApiProvider();
      return provider.createCustomPaymentInstruction({
        organizationId,
        title: data.title,
        instructions: data.instructions,
        isActive: true,
        sortOrder: 0,
      });
    },
    onSuccess: () => {
      if (organizationId) {
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.customPaymentInstructions(organizationId),
        });
      }
    },
  });
}

// Hook to update custom payment instruction
export function useUpdateCustomPaymentInstruction() {
  const queryClient = useQueryClient();
  const organizationId = useActiveOrganizationId();

  return useMutation({
    mutationFn: async (data: { id: string; title?: string; instructions?: string; isActive?: boolean }) => {
      const provider = getApiProvider();
      return provider.updateCustomPaymentInstruction(data);
    },
    onSuccess: () => {
      if (organizationId) {
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.customPaymentInstructions(organizationId),
        });
      }
    },
  });
}

// Hook to delete custom payment instruction
export function useDeleteCustomPaymentInstruction() {
  const queryClient = useQueryClient();
  const organizationId = useActiveOrganizationId();

  return useMutation({
    mutationFn: async (instructionId: string) => {
      const provider = getApiProvider();
      return provider.deleteCustomPaymentInstruction(instructionId);
    },
    onSuccess: () => {
      if (organizationId) {
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.customPaymentInstructions(organizationId),
        });
      }
    },
  });
}

// Hook to update organization payment methods configuration
export function useUpdateOrganizationPaymentMethods() {
  const queryClient = useQueryClient();
  const organizationId = useActiveOrganizationId();

  return useMutation({
    mutationFn: async (data: {
      activeGateway?: string;
      paymentGateways?: any[];
      customInstructions?: string[];
      allowPartialPayments?: boolean;
      autoCreatePaymentLinks?: boolean;
    }) => {
      if (!organizationId) throw new Error('No organization selected');
      
      const provider = getApiProvider();
      return provider.updateOrganizationPaymentMethods({
        organizationId,
        ...data,
      });
    },
    onSuccess: () => {
      if (organizationId) {
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.organizationPaymentMethods(organizationId),
        });
      }
    },
  });
} 