import { getApiProvider } from '@/core/providers/provider-factory';
import { CreateInvoiceTemplateInput, InvoiceTemplate, UpdateInvoiceTemplateInput } from '@/defs/invoice-template';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

const provider = getApiProvider();

export const fetchInvoiceTemplates = async (organizationId?: string): Promise<InvoiceTemplate[]> => {
  return await provider.getInvoiceTemplates(organizationId);
};

export const fetchInvoiceTemplate = async (templateId: string): Promise<InvoiceTemplate | null> => {
  return await provider.getInvoiceTemplate(templateId);
};

export const createInvoiceTemplate = async (data: CreateInvoiceTemplateInput & { organizationId: string }): Promise<InvoiceTemplate> => {
  const { organizationId, ...template } = data;
  const response = await provider.createInvoiceTemplate(organizationId, template);
  return response;
};

export const updateInvoiceTemplate = async (data: UpdateInvoiceTemplateInput & { templateId: string }): Promise<InvoiceTemplate> => {
  const { templateId, ...updates } = data;
  const response = await provider.updateInvoiceTemplate(templateId, updates);
  return response;
};

export const deleteInvoiceTemplate = async (templateId: string): Promise<void> => {
  return await provider.deleteInvoiceTemplate(templateId);
};

export const useInvoiceTemplates = (organizationId?: string) => {
  const {
    error,
    isPending: loading,
    data: templates,
  } = useQuery({
    queryKey: ['invoice-templates', { organizationId }],
    queryFn: () => fetchInvoiceTemplates(organizationId),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });

  return { templates, loading, error };
};

export const useInvoiceTemplate = (templateId: string) => {
  const {
    error,
    isPending: loading,
    data: template,
  } = useQuery({
    queryKey: ['invoice-template', { templateId }],
    queryFn: () => fetchInvoiceTemplate(templateId),
    enabled: !!templateId,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });

  return { template, loading, error };
};

export const useCreateInvoiceTemplate = () => {
  const queryClient = useQueryClient();
  
  const {
    error,
    isPending: loading,
    mutateAsync: createTemplateFn,
  } = useMutation({
    mutationFn: (data: CreateInvoiceTemplateInput & { organizationId: string }) =>
      createInvoiceTemplate(data),
    onSuccess: (newTemplate) => {
      // Invalidate templates queries
      queryClient.invalidateQueries({
        queryKey: ['invoice-templates']
      });
      
      // Add the new template to the cache
      queryClient.setQueryData(['invoice-template', { templateId: newTemplate.id }], newTemplate);
    },
    onError: (error) => {
      console.error('Failed to create invoice template:', error);
    },
  });

  return { createTemplate: createTemplateFn, loading, error };
};

export const useUpdateInvoiceTemplate = () => {
  const queryClient = useQueryClient();
  
  const {
    error,
    isPending: loading,
    mutateAsync: updateTemplateFn,
  } = useMutation({
    mutationFn: (data: UpdateInvoiceTemplateInput & { templateId: string }) =>
      updateInvoiceTemplate(data),
    onSuccess: (updatedTemplate) => {
      // Update the specific template cache
      queryClient.setQueryData(['invoice-template', { templateId: updatedTemplate.id }], updatedTemplate);
      
      // Invalidate templates list queries
      queryClient.invalidateQueries({
        queryKey: ['invoice-templates']
      });
    },
    onError: (error) => {
      console.error('Failed to update invoice template:', error);
    },
  });

  return { updateTemplate: updateTemplateFn, loading, error };
};

export const useDeleteInvoiceTemplate = () => {
  const queryClient = useQueryClient();
  
  const {
    error,
    isPending: loading,
    mutateAsync: deleteTemplateFn,
  } = useMutation({
    mutationFn: (templateId: string) => deleteInvoiceTemplate(templateId),
    onSuccess: (_, templateId) => {
      // Remove from cache
      queryClient.removeQueries({
        queryKey: ['invoice-template', { templateId }]
      });
      
      // Invalidate templates list queries
      queryClient.invalidateQueries({
        queryKey: ['invoice-templates']
      });
    },
    onError: (error) => {
      console.error('Failed to delete invoice template:', error);
    },
  });

  return { deleteTemplate: deleteTemplateFn, loading, error };
}; 