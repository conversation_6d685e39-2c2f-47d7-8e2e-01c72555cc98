import { INVOICES_KEY, INVOICE_KEY } from '@/constants/query-keys';
import { CreateInvoiceInput as ProviderCreateInvoiceInput } from '@/core/providers/api-provider-interface';
import { getApiProvider } from '@/core/providers/provider-factory';
import { queryClient } from '@/core/query-client';
import { Invoice, CreateInvoiceInput as ZodCreateInvoiceInput } from '@/defs/invoice';
import { useActiveOrganizationId } from '@/stores';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const createInvoice = async (organizationId: string, data: ZodCreateInvoiceInput): Promise<Invoice> => {
  // Convert Zod input to provider input format
  const providerData: ProviderCreateInvoiceInput = {
    ...data,
    totals: {}, // Will be calculated by the provider/backend
  };
  
  const response = await provider.createInvoice(organizationId, providerData);
  
  // Immediate cache updates
  queryClient.setQueryData(INVOICE_KEY(response.id, organizationId), response);
  queryClient.setQueryData(INVOICES_KEY(organizationId), (cache: Invoice[]) => {
    if (!cache) return [response];
    return [...cache, response];
  });
  
  return response;
};

export const useCreateInvoice = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: createInvoiceFn,
  } = useMutation({
    mutationFn: (data: ZodCreateInvoiceInput) => 
      createInvoice(organizationId!, data),
    onSuccess: (data) => {
      // Additional success handling if needed
    },
  });

  return { createInvoice: createInvoiceFn, loading, error };
}; 