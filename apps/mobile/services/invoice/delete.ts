import { INVOICES_KEY, INVOICE_KEY } from '@/constants/query-keys';
import { getApiProvider } from '@/core/providers/provider-factory';
import { queryClient } from '@/core/query-client';
import { Invoice } from '@/defs/invoice';
import { useActiveOrganizationId } from '@/stores';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const deleteInvoice = async (organizationId: string, invoiceId: string): Promise<void> => {
  await provider.deleteInvoice(organizationId, invoiceId);
  
  // Immediate cache updates
  queryClient.removeQueries({ queryKey: INVOICE_KEY(invoiceId, organizationId) });
  queryClient.setQueryData(INVOICES_KEY(organizationId), (cache: Invoice[]) => {
    if (!cache) return [];
    return cache.filter((invoice) => invoice.id !== invoiceId);
  });
};

export const useDeleteInvoice = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: deleteInvoiceFn,
  } = useMutation({
    mutationFn: (invoiceId: string) => 
      deleteInvoice(organizationId!, invoiceId),
  });

  return { deleteInvoice: deleteInvoiceFn, loading, error };
}; 