import { INVOICES_KEY, INVOICE_KEY } from '@/constants/query-keys';
import { getApiProvider } from '@/core/providers/provider-factory';
import { queryClient } from '@/core/query-client';
import { Invoice, UpdateInvoiceDto } from '@/defs/invoice';
import { useActiveOrganizationId } from '@/stores';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const updateInvoice = async (data: UpdateInvoiceDto): Promise<Invoice> => {
  const response = await provider.updateInvoice(data.organizationId, data.id, data);
  
  // Immediate cache updates
  queryClient.setQueryData(INVOICE_KEY(response.id, data.organizationId), response);
  queryClient.setQueryData(INVOICES_KEY(data.organizationId), (cache: Invoice[]) => {
    if (!cache) return [response];
    return cache.map((invoice) => invoice.id === response.id ? response : invoice);
  });
  
  return response;
};

export const useUpdateInvoice = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: updateInvoiceFn,
  } = useMutation({
    mutationFn: (data: UpdateInvoiceDto) => updateInvoice(data),
    onSuccess: (data) => {
      // Additional success handling if needed
    },
  });

  return { updateInvoice: updateInvoiceFn, loading, error };
}; 