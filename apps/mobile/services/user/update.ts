import { getApiProvider } from '@/core/providers/provider-factory';
import { UpdateUserProfileInput, UserProfile } from '@/defs/user';
import { useCurrentUserId } from '@/stores/user-selectors';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const provider = getApiProvider();

export const updateUserProfile = async (data: UpdateUserProfileInput & { userId: string }): Promise<UserProfile> => {
  const { userId, ...updates } = data;
  const response = await provider.updateUserProfile(userId, updates);
  return response;
};

export const uploadUserAvatar = async (data: { userId: string; avatarFile: { uri: string; type: string; name: string; size: number } }): Promise<string> => {
  const { userId, avatarFile } = data;
  const response = await provider.uploadAvatar(userId, avatarFile);
  return response;
};

export const useUpdateUserProfile = () => {
  const queryClient = useQueryClient();
  const currentUserId = useCurrentUserId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: updateProfileFn,
  } = useMutation({
    mutationFn: (data: UpdateUserProfileInput) => 
      updateUserProfile({ ...data, userId: currentUserId! }),
    onSuccess: (updatedProfile) => {
      // Update cache with new profile data
      queryClient.setQueryData(['user-profile', { userId: currentUserId }], updatedProfile);
      
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['user-profile']
      });
    },
    onError: (error) => {
      console.error('Failed to update user profile:', error);
    },
  });

  return { updateProfile: updateProfileFn, loading, error };
};

export const useUploadAvatar = () => {
  const queryClient = useQueryClient();
  const currentUserId = useCurrentUserId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: uploadAvatarFn,
  } = useMutation({
    mutationFn: (avatarFile: { uri: string; type: string; name: string; size: number }) =>
      uploadUserAvatar({ userId: currentUserId!, avatarFile }),
    onSuccess: (avatarUrl) => {
      // Update the user profile cache with new avatar URL
      queryClient.setQueryData(['user-profile', { userId: currentUserId }], (oldData: UserProfile | undefined) => {
        if (oldData) {
          return { ...oldData, avatar: avatarUrl };
        }
        return oldData;
      });
      
      // Invalidate profile queries
      queryClient.invalidateQueries({
        queryKey: ['user-profile']
      });
    },
    onError: (error) => {
      console.error('Failed to upload avatar:', error);
    },
  });

  return { uploadAvatar: uploadAvatarFn, loading, error };
}; 