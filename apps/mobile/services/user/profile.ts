import { getApiProvider } from '@/core/providers/provider-factory';
import { UserProfile } from '@/defs/user';
import { useCurrentUserId } from '@/stores';
import { useQuery } from '@tanstack/react-query';

const provider = getApiProvider();

export const fetchUserProfile = async (userId: string): Promise<UserProfile | null> => {
  return await provider.getUserProfile(userId);
};

export const useUserProfile = (userId?: string) => {
  const currentUserId = useCurrentUserId();
  const targetUserId = userId || currentUserId;
  
  const {
    error,
    isPending: loading,
    data: profile,
  } = useQuery({
    queryKey: ['user-profile', { userId: targetUserId }],
    queryFn: () => fetchUserProfile(targetUserId!),
    enabled: !!targetUserId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  return { profile, loading, error };
}; 