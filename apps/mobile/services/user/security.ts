import { getApiProvider } from '@/core/providers/provider-factory';
import { SecuritySettings, UpdateSecuritySettingsInput } from '@/defs/user';
import { useCurrentUserId } from '@/stores';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

const provider = getApiProvider();

export const fetchSecuritySettings = async (userId: string): Promise<SecuritySettings | null> => {
  return await provider.getSecuritySettings(userId);
};

export const updateSecuritySettings = async (data: UpdateSecuritySettingsInput & { userId: string }): Promise<SecuritySettings> => {
  const { userId, ...updates } = data;
  const response = await provider.updateSecuritySettings(userId, updates);
  return response;
};

export const useSecuritySettings = (userId?: string) => {
  const currentUserId = useCurrentUserId();
  const targetUserId = userId || currentUserId;
  
  const {
    error,
    isPending: loading,
    data: securitySettings,
  } = useQuery({
    queryKey: ['security-settings', { userId: targetUserId }],
    queryFn: () => fetchSecuritySettings(targetUserId!),
    enabled: !!targetUserId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  return { securitySettings, loading, error };
};

export const useUpdateSecuritySettings = () => {
  const queryClient = useQueryClient();
  const currentUserId = useCurrentUserId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: updateSettingsFn,
  } = useMutation({
    mutationFn: (data: UpdateSecuritySettingsInput) => 
      updateSecuritySettings({ ...data, userId: currentUserId! }),
    onSuccess: (updatedSettings) => {
      // Update cache with new security settings
      queryClient.setQueryData(['security-settings', { userId: currentUserId }], updatedSettings);
      
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['security-settings']
      });
    },
    onError: (error) => {
      console.error('Failed to update security settings:', error);
    },
  });

  return { updateSecuritySettings: updateSettingsFn, loading, error };
}; 