import { SERVICES_KEY, SERVICE_KEY } from '@/constants/query-keys';
import { getApiProvider } from '@/core/providers/provider-factory';
import { queryClient } from '@/core/query-client';
import { Service, UpdateServiceDto } from '@/defs/service';
import { useActiveOrganizationId } from '@/stores';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const updateServiceService = async (data: UpdateServiceDto): Promise<Service> => {
  const response = await provider.updateService(data.organizationId, data.id, data.updates);
  
  // Immediate cache updates
  queryClient.setQueryData(SERVICE_KEY(response.id, data.organizationId), response);
  queryClient.setQueryData(SERVICES_KEY(data.organizationId), (cache: Service[]) => {
    if (!cache) return [response];
    return cache.map((service) => service.id === response.id ? response : service);
  });
  
  return response;
};

export const useUpdateService = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: updateServiceFn,
  } = useMutation({
    mutationFn: ({ serviceId, updates }: { serviceId: string; updates: Partial<Service> }) =>
      updateServiceService({ organizationId: organizationId!, id: serviceId, updates }),
    onMutate: async ({ serviceId, updates }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: SERVICE_KEY(serviceId, organizationId!) });
      
      // Snapshot the previous value
      const previousService = queryClient.getQueryData<Service>(
        SERVICE_KEY(serviceId, organizationId!)
      );
      
      // Optimistically update to the new value
      if (previousService) {
        queryClient.setQueryData<Service>(
          SERVICE_KEY(serviceId, organizationId!),
          { ...previousService, ...updates }
        );
      }
      
      // Return a context object with the snapshotted value
      return { previousService };
    },
    onSuccess: (updatedService, { serviceId }) => {
      // Invalidate service activities cache to show new activity records
      queryClient.invalidateQueries({ 
        queryKey: ['service-activities', organizationId, serviceId] 
      });
    },
    onError: (error, { serviceId }, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousService) {
        queryClient.setQueryData(
          SERVICE_KEY(serviceId, organizationId!),
          context.previousService
        );
      }
      console.error('Failed to update service:', error);
    },
  });

  return { updateService: updateServiceFn, loading, error };
}; 