import { SERVICES_KEY, SERVICE_KEY } from '@/constants/query-keys';
import { getApiProvider } from '@/core/providers/provider-factory';
import { queryClient } from '@/core/query-client';
import { Service } from '@/defs/service';
import { useActiveOrganizationId } from '@/stores';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const deleteService = async (organizationId: string, serviceId: string): Promise<void> => {
  await provider.deleteService(organizationId, serviceId);
  
  // Immediate cache updates
  queryClient.removeQueries({ queryKey: SERVICE_KEY(serviceId, organizationId) });
  queryClient.setQueryData(SERVICES_KEY(organizationId), (cache: Service[]) => {
    if (!cache) return [];
    return cache.filter((service) => service.id !== serviceId);
  });
};

export const useDeleteService = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: deleteServiceFn,
  } = useMutation({
    mutationFn: (serviceId: string) => 
      deleteService(organizationId!, serviceId),
    onMutate: async (serviceId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: SERVICES_KEY(organizationId!) });
      
      // Snapshot the previous value
      const previousServices = queryClient.getQueryData<Service[]>(
        SERVICES_KEY(organizationId!)
      );
      
      // Optimistically update to the new value
      queryClient.setQueryData<Service[]>(
        SERVICES_KEY(organizationId!),
        (old) => old ? old.filter(service => service.id !== serviceId) : []
      );
      
      // Return a context object with the snapshotted value
      return { previousServices };
    },
    onError: (error, serviceId, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousServices) {
        queryClient.setQueryData(
          SERVICES_KEY(organizationId!),
          context.previousServices
        );
      }
      console.error('Failed to delete service:', error);
    },
  });

  return { deleteService: deleteServiceFn, loading, error };
}; 