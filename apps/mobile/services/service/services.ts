import { SERVICES_KEY, SERVICE_KEY } from '@/constants/query-keys';
import { getApiProvider } from '@/core/providers/provider-factory';
import { queryClient } from '@/core/query-client';
import { Service, UpdateServiceDto } from '@/defs/service';
import { useActiveOrganizationId } from '@/stores/organization-selectors';
import { useQuery } from '@tanstack/react-query';
import { debounce } from 'lodash';

const serviceProvider = getApiProvider();

// Service functions
export const fetchServices = async (organizationId: string): Promise<Service[]> => {
  const response = await serviceProvider.getServices(organizationId);
  return response;
};

export const fetchService = async (organizationId: string, serviceId: string): Promise<Service> => {
  const response = await serviceProvider.getService(organizationId, serviceId);
  return response;
};

export const updateService = async (data: UpdateServiceDto): Promise<Service> => {
  const response = await serviceProvider.updateService(data.organizationId, data.id, data);
  
  // Immediate cache updates
  queryClient.setQueryData(SERVICE_KEY(response.id, data.organizationId), response);
  queryClient.setQueryData(SERVICES_KEY(data.organizationId), (cache: Service[]) => {
    if (!cache) return [response];
    return cache.map((service) => service.id === response.id ? response : service);
  });

  // Invalidate service activities cache to show new activity records
  queryClient.invalidateQueries({ 
    queryKey: ['service-activities', data.organizationId, data.id] 
  });
  
  return response;
};

// Debounced version for real-time editing
export const debouncedUpdateService = debounce(updateService, 1000);

// Clean hooks
export const useServices = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    data: services,
  } = useQuery({
    queryKey: SERVICES_KEY(organizationId!),
    queryFn: () => fetchServices(organizationId!),
    enabled: !!organizationId,
  });

  return { services, loading, error };
};

export const useService = (serviceId: string) => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    data: service,
  } = useQuery({
    queryKey: SERVICE_KEY(serviceId, organizationId!),
    queryFn: () => fetchService(organizationId!, serviceId),
    enabled: !!organizationId && !!serviceId,
  });

  return { service, loading, error };
}; 

// Hook to get service activities
export function useServiceActivities(serviceId: string) {
  const organizationId = useActiveOrganizationId();
  
  return useQuery({
    queryKey: ['service-activities', organizationId, serviceId],
    queryFn: async () => {
      if (!organizationId || !serviceId) return [];
      return await serviceProvider.getServiceActivities(organizationId, serviceId);
    },
    enabled: !!organizationId && !!serviceId,
  });
} 