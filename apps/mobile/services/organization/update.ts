// Update organization is not yet supported by the API provider
// This service will be implemented when the provider interface is updated

import { ORGANIZATIONS_KEY, ORGANIZATION_KEY } from '@/constants/query-keys';
import { getApiProvider } from '@/core/providers/provider-factory';
import { queryClient } from '@/core/query-client';
import { Organization, UpdateOrganizationInput } from '@/defs/organization';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const updateOrganization = async (organizationId: string, updates: UpdateOrganizationInput): Promise<Organization> => {
  const response = await provider.updateOrganization(organizationId, updates);
  
  // Update cache
  queryClient.setQueryData(ORGANIZATION_KEY(response.id), response);
  queryClient.setQueryData(ORGANIZATIONS_KEY, (cache: Organization[]) => {
    if (!cache) return [response];
    return cache.map((org) => org.id === response.id ? response : org);
  });
  
  return response;
};

export const useUpdateOrganization = () => {
  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: UpdateOrganizationInput }) => 
      updateOrganization(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ORGANIZATIONS_KEY });
    },
  });
}; 