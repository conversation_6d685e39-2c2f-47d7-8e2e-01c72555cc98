// Create organization is not yet supported by the API provider
// This service will be implemented when the provider interface is updated

import { ORGANIZATIONS_KEY } from '@/constants/query-keys';
import { getApiProvider } from '@/core/providers/provider-factory';
import { queryClient } from '@/core/query-client';
import { CreateOrganizationInput, Organization } from '@/defs/organization';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const createOrganization = async (data: CreateOrganizationInput): Promise<Organization> => {
  const response = await provider.createOrganization(data);
  
  // Update cache
  queryClient.setQueryData(ORGANIZATIONS_KEY, (cache: Organization[]) => {
    if (!cache) return [response];
    return [...cache, response];
  });
  
  return response;
};

export const useCreateOrganization = () => {
  return useMutation({
    mutationFn: createOrganization,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ORGANIZATIONS_KEY });
    },
  });
}; 