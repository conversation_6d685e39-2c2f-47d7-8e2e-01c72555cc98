import { ORGANIZATIONS_KEY, ORGANIZATION_KEY } from '@/constants/query-keys';
import { getApi<PERSON>rovider } from '@/core/providers/provider-factory';
import { Organization } from '@/defs/organization';
import { useQuery } from '@tanstack/react-query';

const provider = getApiProvider();

// Service functions
export const fetchOrganizations = async (): Promise<Organization[]> => {
  const response = await provider.getOrganizations();
  return response;
};

export const fetchOrganization = async (organizationId: string): Promise<Organization> => {
  const response = await provider.getOrganization(organizationId);
  return response;
};

// Clean hooks
export const useOrganizations = () => {
  const {
    error,
    isPending: loading,
    data: organizations,
  } = useQuery({
    queryKey: ORGANIZATIONS_KEY,
    queryFn: () => fetchOrganizations(),
    staleTime: 5 * 60 * 1000, // 5 minutes - organizations change rarely
  });

  return { organizations, loading, error };
};

export const useOrganization = (organizationId: string) => {
  const {
    error,
    isPending: loading,
    data: organization,
  } = useQuery({
    queryKey: ORGANIZATION_KEY(organizationId),
    queryFn: () => fetchOrganization(organizationId),
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes - organizations change rarely
  });

  return { organization, loading, error };
}; 