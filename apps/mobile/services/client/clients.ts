import { CLIENTS_KEY, CLIENT_KEY } from '@/constants/query-keys';
import { getApiProvider } from '@/core/providers/provider-factory';
import { queryClient } from '@/core/query-client';
import { Client, UpdateClientDto } from '@/defs/client';
import { useActiveOrganizationId } from '@/stores/organization-selectors';
import { useQuery } from '@tanstack/react-query';
import { debounce } from 'lodash';

const clientProvider = getApiProvider();

// Client functions
export const fetchClients = async (organizationId: string): Promise<Client[]> => {
  const response = await clientProvider.getClients(organizationId);
  return response;
};

export const fetchClient = async (organizationId: string, clientId: string): Promise<Client> => {
  const response = await clientProvider.getClient(organizationId, clientId);
  return response;
};

export const updateClient = async (data: UpdateClientDto): Promise<Client> => {
  const response = await clientProvider.updateClient(data.organizationId, data.id, data);
  
  // Immediate cache updates
  queryClient.setQueryData(CLIENT_KEY(response.id, data.organizationId), response);
  queryClient.setQueryData(CLIENTS_KEY(data.organizationId), (cache: Client[]) => {
    if (!cache) return [response];
    return cache.map((client) => client.id === response.id ? response : client);
  });
  
  return response;
};

// Debounced version for real-time editing
export const debouncedUpdateClient = debounce(updateClient, 1000);

// Invalidate clients cache after changes
export const invalidateClients = debounce((organizationId: string) => {
  queryClient.invalidateQueries({ queryKey: CLIENTS_KEY(organizationId) });
}, 100);

// Clean hooks
export const useClients = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    data: clients,
  } = useQuery({
    queryKey: CLIENTS_KEY(organizationId!),
    queryFn: () => fetchClients(organizationId!),
    enabled: !!organizationId,
  });

  return { clients, loading, error };
};

export const useClient = (clientId: string) => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    data: client,
  } = useQuery({
    queryKey: CLIENT_KEY(clientId, organizationId!),
    queryFn: () => fetchClient(organizationId!, clientId),
    enabled: !!organizationId && !!clientId,
  });

  return { client, loading, error };
};

// Hook to get client activities
export function useClientActivities(clientId: string) {
  const organizationId = useActiveOrganizationId();
  
  return useQuery({
    queryKey: ['client-activities', organizationId, clientId],
    queryFn: async () => {
      if (!organizationId || !clientId) return [];
      return await clientProvider.getClientActivities(organizationId, clientId);
    },
    enabled: !!organizationId && !!clientId,
  });
} 