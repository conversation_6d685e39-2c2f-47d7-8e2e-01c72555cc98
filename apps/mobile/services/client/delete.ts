import { CLIENTS_KEY, CLIENT_KEY } from '@/constants/query-keys';
import { getApiProvider } from '@/core/providers/provider-factory';
import { queryClient } from '@/core/query-client';
import { Client } from '@/defs/client';
import { useActiveOrganizationId } from '@/stores/organization-selectors';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const deleteClient = async (organizationId: string, clientId: string): Promise<void> => {
  await provider.deleteClient(organizationId, clientId);
  
  // Immediate cache updates
  queryClient.removeQueries({ queryKey: CLIENT_KEY(clientId, organizationId) });
  queryClient.setQueryData(CLIENTS_KEY(organizationId), (cache: Client[]) => {
    if (!cache) return [];
    return cache.filter((client) => client.id !== clientId);
  });
};

export const useDeleteClient = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: deleteClientFn,
  } = useMutation({
    mutationFn: (clientId: string) => 
      deleteClient(organizationId!, clientId),
    onMutate: async (clientId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: CLIENTS_KEY(organizationId!) });
      
      // Snapshot the previous value
      const previousClients = queryClient.getQueryData<Client[]>(
        CLIENTS_KEY(organizationId!)
      );
      
      // Optimistically update to the new value
      queryClient.setQueryData<Client[]>(
        CLIENTS_KEY(organizationId!),
        (old) => old ? old.filter(client => client.id !== clientId) : []
      );
      
      // Return a context object with the snapshotted value
      return { previousClients };
    },
    onError: (error, clientId, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousClients) {
        queryClient.setQueryData(
          CLIENTS_KEY(organizationId!),
          context.previousClients
        );
      }
      console.error('Failed to delete client:', error);
    },
  });

  return { deleteClient: deleteClientFn, loading, error };
}; 