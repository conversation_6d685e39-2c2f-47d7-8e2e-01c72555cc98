import { CLIENTS_KEY, CLIENT_KEY } from '@/constants/query-keys';
import { getApiProvider } from '@/core/providers/provider-factory';
import { queryClient } from '@/core/query-client';
import { Client, UpdateClientDto } from '@/defs/client';
import { useActiveOrganizationId } from '@/stores';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const updateClientService = async (data: UpdateClientDto): Promise<Client> => {
  const response = await provider.updateClient(data.organizationId, data.id, data.updates);
  
  // Immediate cache updates
  queryClient.setQueryData(CLIENT_KEY(response.id, data.organizationId), response);
  queryClient.setQueryData(CLIENTS_KEY(data.organizationId), (cache: Client[]) => {
    if (!cache) return [response];
    return cache.map((client) => client.id === response.id ? response : client);
  });
  
  return response;
};

export const useUpdateClient = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: updateClientFn,
  } = useMutation({
    mutationFn: ({ clientId, updates }: { clientId: string; updates: Partial<Client> }) =>
      updateClientService({ organizationId: organizationId!, id: clientId, updates }),
    onMutate: async ({ clientId, updates }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: CLIENT_KEY(clientId, organizationId!) });
      
      // Snapshot the previous value
      const previousClient = queryClient.getQueryData<Client>(
        CLIENT_KEY(clientId, organizationId!)
      );
      
      // Optimistically update to the new value
      if (previousClient) {
        queryClient.setQueryData<Client>(
          CLIENT_KEY(clientId, organizationId!),
          { ...previousClient, ...updates }
        );
      }
      
      // Return a context object with the snapshotted value
      return { previousClient };
    },
    onError: (error, { clientId }, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousClient) {
        queryClient.setQueryData(
          CLIENT_KEY(clientId, organizationId!),
          context.previousClient
        );
      }
      console.error('Failed to update client:', error);
    },
  });

  return { updateClient: updateClientFn, loading, error };
}; 