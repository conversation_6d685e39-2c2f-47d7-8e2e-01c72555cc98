import { CLIENTS_KEY, CLIENT_KEY } from '@/constants/query-keys';
import { CreateClientInput as ProviderCreateClientInput } from '@/core/providers/api-provider-interface';
import { getApiProvider } from '@/core/providers/provider-factory';
import { queryClient } from '@/core/query-client';
import { Client, CreateClientInput as ZodCreateClientInput } from '@/defs/client';
import { useActiveOrganizationId } from '@/stores';
import { useMutation } from '@tanstack/react-query';

const provider = getApiProvider();

export const createClient = async (organizationId: string, data: ZodCreateClientInput): Promise<Client> => {
  // Convert Zod input to provider input format
  const providerData: ProviderCreateClientInput = {
    name: data.name,
    company: data.company,
    contact: {
      email: data.contact.email,
      phone: data.contact.phone,
    },
    address: {
      fullAddress: data.address?.fullAddress,
    },
    photo: data.photo,
    isActive: data.isActive,
    defaultTaxExempt: data.defaultTaxExempt,
  };
  
  const response = await provider.createClient(organizationId, providerData);
  
  // Immediate cache updates
  queryClient.setQueryData(CLIENT_KEY(response.id, organizationId), response);
  queryClient.setQueryData(CLIENTS_KEY(organizationId), (cache: Client[]) => {
    if (!cache) return [response];
    return [...cache, response];
  });
  
  return response;
};

export const useCreateClient = () => {
  const organizationId = useActiveOrganizationId();
  
  const {
    error,
    isPending: loading,
    mutateAsync: createClientFn,
  } = useMutation({
    mutationFn: (data: ZodCreateClientInput) => 
      createClient(organizationId!, data),
    onSuccess: (data) => {
      // Additional success handling if needed
    },
  });

  return { createClient: createClientFn, loading, error };
}; 