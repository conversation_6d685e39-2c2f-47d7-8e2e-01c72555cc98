import { useQuery } from '@tanstack/react-query';
import { getApiProvider } from '../providers/provider-factory';

const provider = getApiProvider();

// Query Keys
export const taxOptionKeys = {
  all: ['taxOptions'] as const,
  lists: () => [...taxOptionKeys.all, 'list'] as const,
  details: () => [...taxOptionKeys.all, 'detail'] as const,
  detail: (id: string) => [...taxOptionKeys.details(), id] as const,
};

// Queries
export function useTaxOptions() {
  return useQuery({
    queryKey: taxOptionKeys.lists(),
    queryFn: () => provider.getTaxOptions(),
    staleTime: 10 * 60 * 1000, // 10 minutes - tax options change rarely
  });
}

export function useTaxOption(id: string) {
  return useQuery({
    queryKey: taxOptionKeys.detail(id),
    queryFn: () => provider.getTaxOption(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes - tax options change rarely
  });
} 