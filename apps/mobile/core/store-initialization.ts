// Mobile app store initialization
// This file handles initialization of shared stores with mobile-specific data

import { dashboardData, taxOptions } from '@/constants/data';
import { queryClient } from '@/core/query-client';
import {
  initializeOrganizationStore,
  initializeUserStore,
  initializeInvoiceStore,
  setCacheInvalidationCallback,
  type Organization,
  type User
} from '@repo/stores';

// Initialize with existing data for backward compatibility
const initialOrganizations: Organization[] = dashboardData.user.companies.map(company => ({
  id: company.id,
  name: company.name,
  nickname: company.nickname,
  description: company.description || '',
  logo: company.logo,
  contact: {
    email: '',
    phone: '',
    website: '',
    address: '',
  },
  branding: undefined,
  settings: {
    currency: 'USD',
    dateFormat: 'MM/DD/YYYY',
    timezone: 'UTC',
    fiscalYearStart: '01/01',
    invoicePrefix: 'INV',
    defaultPaymentTerms: 'Net 30',
  },
  isActive: true,
  businessNumber: undefined,
  taxNumber: undefined,
  registrationNumber: undefined,
  createdAt: new Date(),
  updatedAt: new Date(),
}));

const initialActiveOrganization = initialOrganizations.find(org => org.id === dashboardData.user.activeCompanyId) || null;

// Mock current user for development - in production this would come from auth
const mockCurrentUser: User = {
  id: 'user_001',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  avatar: undefined,
};

/**
 * Initialize all shared stores with mobile app data
 * This should be called once during app startup
 */
export function initializeSharedStores() {
  // Set up cache invalidation callback for mobile app
  setCacheInvalidationCallback(() => {
    try {
      queryClient.removeQueries({ queryKey: ['invoices'] });
      queryClient.removeQueries({ queryKey: ['clients'] });
      queryClient.removeQueries({ queryKey: ['services'] });
    } catch (error) {
      // QueryClient might not be initialized yet (during app startup)
      console.warn('QueryClient not available for cache invalidation:', error);
    }
  });

  // Initialize the organization store with mobile app data
  initializeOrganizationStore({
    organizations: initialOrganizations,
    activeOrganization: initialActiveOrganization,
    dashboardData: dashboardData.companyData,
  });

  // Initialize the user store with mobile app data
  initializeUserStore(
    mockCurrentUser, // In production, this would be null initially
    true // In production, this would be false initially
  );

  // Initialize the invoice store with mobile app's tax options
  const simplifiedTaxOptions = taxOptions.map(option => ({
    id: option.id,
    name: option.name,
    rate: option.rate,
  }));
  initializeInvoiceStore(simplifiedTaxOptions);

  console.log('[Store Initialization] All shared stores initialized successfully');
}

// Export initialization function
export default initializeSharedStores;
