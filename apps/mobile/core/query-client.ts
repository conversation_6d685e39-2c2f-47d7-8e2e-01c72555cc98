import { QueryClient } from "@tanstack/react-query";

export const queryClient = new QueryClient({
  defaultOptions: {
          mutations: {
        retry: false,
      },
          queries: {
        retry: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchOnWindowFocus: false,
        staleTime: 1000 * 60, // 1 minute
        gcTime: 1000 * 60 * 5, // 5 minutes
      },
  },
});

// Settings-specific query configurations
export const settingsQueryDefaults = {
  user: {
    staleTime: 1000 * 60 * 5, // 5 minutes - user data changes less frequently
    gcTime: 1000 * 60 * 10, // 10 minutes
  },
  subscription: {
    staleTime: 1000 * 60 * 30, // 30 minutes - subscription data changes infrequently
    gcTime: 1000 * 60 * 60, // 1 hour
  },
  templates: {
    staleTime: 1000 * 60 * 10, // 10 minutes - templates change occasionally
    gcTime: 1000 * 60 * 30, // 30 minutes
  },
  defaults: {
    staleTime: 1000 * 60 * 10, // 10 minutes - defaults change occasionally
    gcTime: 1000 * 60 * 30, // 30 minutes
  },
  support: {
    staleTime: 1000 * 60 * 30, // 30 minutes - help content changes rarely
    gcTime: 1000 * 60 * 60, // 1 hour
  },
  feedback: {
    staleTime: 1000 * 60 * 5, // 5 minutes - feedback lists update more frequently
    gcTime: 1000 * 60 * 15, // 15 minutes
  },
} as const;
