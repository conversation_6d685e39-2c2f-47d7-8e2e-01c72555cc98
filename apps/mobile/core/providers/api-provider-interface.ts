import { Client, ClientActivity } from '../../defs/client';
import { Invoice, InvoiceActivity } from '../../defs/invoice';
import { CreateOrganizationInput, Organization, UpdateOrganizationInput } from '../../defs/organization';
import { CreatePayment, Payment, PaymentSummary } from '../../defs/payment';
import {
    CreateCustomPaymentInstructionInput,
    CustomPaymentInstruction,
    OrganizationPaymentMethods,
    UpdateCustomPaymentInstructionInput,
    UpdateOrganizationPaymentMethodsInput
} from '../../defs/payment-method';
import { Service, ServiceActivity } from '../../defs/service';
import { TaxOption } from '../../defs/tax';

// Settings imports
import { AppDefaults, BusinessTypeTemplate, SupportedCurrency, UpdateAppDefaultsInput } from '../../defs/app-defaults';
import { CreateFeedbackInput, Feedback, FeedbackCategory, UpdateFeedbackInput } from '../../defs/feedback';
import { CreateInvoiceTemplateInput, InvoiceTemplate, UpdateInvoiceTemplateInput } from '../../defs/invoice-template';
import { Subscription, SubscriptionChangeRequest, SubscriptionPlan } from '../../defs/subscription';
import { ContactFormInput, FAQ, HelpArticle, HelpCategory, KnowledgeBaseSearchInput, SearchResult } from '../../defs/support';
import { SecuritySettings, UpdateSecuritySettingsInput, UpdateUserProfileInput, UserProfile } from '../../defs/user';

// Create types for request inputs
export interface CreateInvoiceInput {
  invoiceNumber: string;
  status: 'draft' | 'pending' | 'paid' | 'overdue';
  issueDate: Date;
  dueDate: Date;
  clientId: string;
  clientName: string;
  lineItems: any[];
  taxInfo?: any;
  payment: any;
  totals: any;
  notes?: string;
  terms?: string;
}

export interface CreateClientInput {
  name: string;
  company?: string;
  contact: {
    email?: string;
    phone?: string;
  };
  address: {
    fullAddress?: string;
  };
  photo?: string;
  isActive: boolean;
  defaultTaxExempt: boolean;
}

export interface CreateServiceInput {
  name: string;
  description: string;
  pricing: {
    rate: number;
    unit: 'fixed' | 'hour' | 'day' | 'month' | 'project';
    currency: string;
  };
  isActive: boolean;
  taxable: boolean;
  tags: string[];
}

export interface IApiProvider {
  // Organizations
  getOrganizations(): Promise<Organization[]>;
  getOrganization(id: string): Promise<Organization>;
  createOrganization(organization: CreateOrganizationInput): Promise<Organization>;
  updateOrganization(id: string, updates: UpdateOrganizationInput): Promise<Organization>;
  
  // Invoices  
  getInvoices(organizationId: string): Promise<Invoice[]>;
  getInvoice(organizationId: string, invoiceId: string): Promise<Invoice>;
  createInvoice(organizationId: string, invoice: CreateInvoiceInput): Promise<Invoice>;
  updateInvoice(organizationId: string, invoiceId: string, updates: Partial<Invoice>): Promise<Invoice>;
  deleteInvoice(organizationId: string, invoiceId: string): Promise<void>;
  getInvoiceActivities(organizationId: string, invoiceId: string): Promise<InvoiceActivity[]>;
  
  // Clients
  getClients(organizationId: string): Promise<Client[]>;
  getClient(organizationId: string, clientId: string): Promise<Client>;
  createClient(organizationId: string, client: CreateClientInput): Promise<Client>;
  updateClient(organizationId: string, clientId: string, updates: Partial<Client>): Promise<Client>;
  deleteClient(organizationId: string, clientId: string): Promise<void>;
  getClientActivities(organizationId: string, clientId: string): Promise<ClientActivity[]>;
  
  // Services
  getServices(organizationId: string): Promise<Service[]>;
  getService(organizationId: string, serviceId: string): Promise<Service>;
  createService(organizationId: string, service: CreateServiceInput): Promise<Service>;
  updateService(organizationId: string, serviceId: string, updates: Partial<Service>): Promise<Service>;
  deleteService(organizationId: string, serviceId: string): Promise<void>;
  getServiceActivities(organizationId: string, serviceId: string): Promise<ServiceActivity[]>;
  
  // Tax Options
  getTaxOptions(): Promise<TaxOption[]>;
  getTaxOption(id: string): Promise<TaxOption>;

  // Payment operations
  getInvoicePayments(organizationId: string, invoiceId: string): Promise<Payment[]>;
  getPaymentSummary(organizationId: string, invoiceId: string): Promise<PaymentSummary>;
  createPayment(payment: CreatePayment): Promise<Payment>;
  updatePayment(organizationId: string, paymentId: string, updates: Partial<Payment>): Promise<Payment>;
  deletePayment(organizationId: string, paymentId: string): Promise<void>;

  // Payment Methods
  getOrganizationPaymentMethods(organizationId: string): Promise<OrganizationPaymentMethods | null>;
  updateOrganizationPaymentMethods(input: UpdateOrganizationPaymentMethodsInput): Promise<OrganizationPaymentMethods>;
  getCustomPaymentInstructions(organizationId: string): Promise<CustomPaymentInstruction[]>;
  createCustomPaymentInstruction(input: CreateCustomPaymentInstructionInput): Promise<CustomPaymentInstruction>;
  updateCustomPaymentInstruction(input: UpdateCustomPaymentInstructionInput): Promise<CustomPaymentInstruction>;
  deleteCustomPaymentInstruction(instructionId: string): Promise<void>;

  // Payment Gateway Connections
  connectStripeAccount(organizationId: string, authCode: string): Promise<string>; // Returns account ID
  connectPayPalAccount(organizationId: string, authCode: string): Promise<string>; // Returns merchant ID
  disconnectPaymentGateway(organizationId: string, gatewayId: string): Promise<void>;
  createPaymentLink(invoiceId: string): Promise<string>; // Returns payment URL

  // Settings - User Profile
  getUserProfile(userId: string): Promise<UserProfile | null>;
  updateUserProfile(userId: string, updates: UpdateUserProfileInput): Promise<UserProfile>;
  uploadAvatar(userId: string, avatarFile: { uri: string; type: string; name: string; size: number }): Promise<string>;

  // Settings - Security
  getSecuritySettings(userId: string): Promise<SecuritySettings | null>;
  updateSecuritySettings(userId: string, updates: UpdateSecuritySettingsInput): Promise<SecuritySettings>;

  // Settings - Subscription
  getSubscriptionPlans(): Promise<SubscriptionPlan[]>;
  getUserSubscription(userId: string): Promise<Subscription | null>;
  changeSubscription(userId: string, changeRequest: SubscriptionChangeRequest): Promise<Subscription>;
  cancelSubscription(userId: string, cancelAtPeriodEnd?: boolean): Promise<Subscription>;

  // Settings - Invoice Templates
  getInvoiceTemplates(organizationId?: string): Promise<InvoiceTemplate[]>;
  getInvoiceTemplate(templateId: string): Promise<InvoiceTemplate | null>;
  createInvoiceTemplate(organizationId: string, template: CreateInvoiceTemplateInput): Promise<InvoiceTemplate>;
  updateInvoiceTemplate(templateId: string, updates: UpdateInvoiceTemplateInput): Promise<InvoiceTemplate>;
  deleteInvoiceTemplate(templateId: string): Promise<void>;

  // Settings - App Defaults
  getAppDefaults(organizationId: string): Promise<AppDefaults | null>;
  updateAppDefaults(organizationId: string, updates: UpdateAppDefaultsInput): Promise<AppDefaults>;
  getSupportedCurrencies(): Promise<SupportedCurrency[]>;
  getBusinessTypeTemplates(): Promise<BusinessTypeTemplate[]>;

  // Settings - Feedback
  getFeedbackCategories(): Promise<FeedbackCategory[]>;
  submitFeedback(userId: string, feedback: CreateFeedbackInput): Promise<Feedback>;
  getUserFeedback(userId: string): Promise<Feedback[]>;
  updateFeedback(feedbackId: string, updates: UpdateFeedbackInput): Promise<Feedback>;

  // Settings - Help & Support
  getHelpCategories(): Promise<HelpCategory[]>;
  getHelpArticles(categoryId?: string): Promise<HelpArticle[]>;
  getHelpArticle(articleId: string): Promise<HelpArticle | null>;
  getFAQs(categoryId?: string): Promise<FAQ[]>;
  searchKnowledgeBase(searchInput: KnowledgeBaseSearchInput): Promise<SearchResult[]>;
  submitContactForm(contactForm: ContactFormInput): Promise<void>;
  rateApp(rating: number, review?: string): Promise<void>;
} 