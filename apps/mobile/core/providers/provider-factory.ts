import { IApiProvider } from './api-provider-interface';
import { MockProvider } from './mock-provider';

let providerInstance: IApiProvider | null = null;

export function getApiProvider(): IApiProvider {
  if (!providerInstance) {
    if (process.env.NODE_ENV === 'development') {
      providerInstance = new MockProvider();
    } else {
      // In production, you would use the real API provider
      // providerInstance = new ApiProvider();
      // For now, fallback to mock provider
      providerInstance = new MockProvider();
    }
  }
  return providerInstance;
}

// For testing purposes, allow resetting the provider
export function resetProvider(): void {
  providerInstance = null;
} 