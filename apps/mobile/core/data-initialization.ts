/**
 * Data initialization system for the mobile app
 * Loads initial data from the mock provider into stores
 */

import { initializeOrganizationStore } from '@repo/stores';
import { MockProvider } from './providers/mock-provider';

// Create provider instance
const mockProvider = new MockProvider();

/**
 * Initialize all stores with data from the mock provider
 * This should be called once when the app starts
 */
export const initializeAppData = async () => {
  try {
    console.log('🚀 Initializing app data...');

    // Load organizations from mock provider
    const organizations = await mockProvider.getOrganizations();
    console.log(`📊 Loaded ${organizations.length} organizations:`, organizations.map(o => o.name));

    // Initialize organization store
    initializeOrganizationStore({
      organizations,
      activeOrganization: organizations.length > 0 ? organizations[0] : null,
    });

    console.log('✅ App data initialization complete');
    
    return {
      success: true,
      organizationsCount: organizations.length,
    };
  } catch (error) {
    console.error('❌ Failed to initialize app data:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

/**
 * Get the mock provider instance for use in services
 */
export const getMockProvider = () => mockProvider;
