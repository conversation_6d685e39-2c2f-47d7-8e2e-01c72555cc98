import { FloatingActionButton } from '@/components/FloatingActionButton';
import { PageHeader } from '@/components/PageHeader';
import { Card, ConfirmationDialog, EmptyState, SearchInput, Toast, Typography } from '@/components/ui';
import { ActionMenu, ActionMenuItem } from '@/components/ui/ActionMenu';
import { StatusBadge } from '@/components/ui/StatusIndicator';
import { colors } from '@/constants/Colors';
import { useClients } from '@/services/client/clients';
import { useDeleteInvoice } from '@/services/invoice/delete';
import { useInvoices } from '@/services/invoice/invoices';
import { useHasActiveOrganization } from '@/stores';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useMemo, useState } from 'react';
import { ActivityIndicator, Pressable, ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

export default function InvoicesScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [invoiceToDelete, setInvoiceToDelete] = useState<any>(null);
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error' | 'info'>('success');
  const insets = useSafeAreaInsets();
  
  // Clean organization guards
  const hasActiveOrganization = useHasActiveOrganization();
  
  // Use clean service hooks - no parameters, computed selectors
  const { 
    invoices = [], 
    loading, 
    error 
  } = useInvoices();

  const { clients = [] } = useClients();
  const { deleteInvoice } = useDeleteInvoice();

  // Get client display name by clientId
  const getClientDisplayName = (clientId: string, fallbackName: string) => {
    const client = clients.find(c => c.id === clientId);
    return client?.displayName || client?.name || fallbackName;
  };

  // Filter invoices based on search query and active filter
  const filteredInvoices = useMemo(() => {
    return invoices.filter(invoice => {
      const clientDisplayName = getClientDisplayName(invoice.clientId, invoice.clientName);
      const matchesSearch = 
        clientDisplayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        invoice.invoiceNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (invoice.amount || '').includes(searchQuery);
      
      if (activeFilter === 'all') {
        return matchesSearch;
      } else {
        return matchesSearch && invoice.status === activeFilter;
      }
    });
  }, [invoices, searchQuery, activeFilter, clients, getClientDisplayName]);

  // Calculate invoice counts for each status
  const getStatusCount = (status: string) => {
    if (status === 'all') return invoices.length;
    return invoices.filter(invoice => invoice.status === status).length;
  };

  // Format date to "Month DD" format
  const formatShortDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const handleCreateInvoice = () => {
    router.push('/create-invoice-collapsible');
  };

  const handleViewInvoice = (invoiceId: string) => {
    router.push(`/invoice-detail?invoiceId=${invoiceId}`);
  };

  const handlePreviewInvoice = (invoiceId: string) => {
    // Navigate to invoice detail for preview
    router.push(`/invoice-detail?invoiceId=${invoiceId}`);
  };

  const handleEditInvoice = (invoiceId: string) => {
    // Navigate to edit invoice screen
    router.push(`/create-invoice-collapsible?editInvoiceId=${invoiceId}`);
  };

  const handleDeleteInvoice = (invoiceId: string) => {
    const invoice = invoices.find(inv => inv.id === invoiceId);
    if (!invoice) return;

    setInvoiceToDelete(invoice);
    setDeleteDialogVisible(true);
  };

  const confirmDeleteInvoice = async () => {
    if (!invoiceToDelete) return;

    try {
      await deleteInvoice(invoiceToDelete.id);
      setDeleteDialogVisible(false);
      setInvoiceToDelete(null);
      setToastMessage('Invoice has been deleted.');
      setToastType('success');
      setToastVisible(true);
    } catch (error: any) {
      setDeleteDialogVisible(false);
      setInvoiceToDelete(null);
      setToastMessage('Failed to delete invoice. Please try again.');
      setToastType('error');
      setToastVisible(true);
      console.error('Delete invoice error:', error);
    }
  };

  const cancelDeleteInvoice = () => {
    setDeleteDialogVisible(false);
    setInvoiceToDelete(null);
  };

  const getInvoiceActions = (invoiceId: string): ActionMenuItem[] => [
    {
      id: 'preview',
      title: 'Preview',
      icon: 'eye-outline',
      onPress: () => handlePreviewInvoice(invoiceId),
    },
    {
      id: 'view',
      title: 'View Details',
      icon: 'document-text-outline',
      onPress: () => handleViewInvoice(invoiceId),
    },
    {
      id: 'edit',
      title: 'Edit',
      icon: 'create-outline',
      onPress: () => handleEditInvoice(invoiceId),
    },
    {
      id: 'delete',
      title: 'Delete',
      icon: 'trash-outline',
      onPress: () => handleDeleteInvoice(invoiceId),
      destructive: true,
    },
  ];

  // Organization selection guard
  if (!hasActiveOrganization) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary }}>
        <StatusBar style="light" />
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <Typography variant="h3" color="secondary" style={{ marginBottom: 8 }}>
            Select an Organization
          </Typography>
          <Typography variant="body" color="secondary" center>
            Please select an organization to view invoices
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  // Loading state
  if (loading) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary }}>
        <StatusBar style="light" />
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <ActivityIndicator size="large" color={colors.background} />
          <Typography variant="body" color="secondary" style={{ marginTop: 16 }}>
            Loading invoices...
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  // Error state
  if (error) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary }}>
        <StatusBar style="light" />
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <Typography variant="h3" color="error" style={{ marginBottom: 8 }}>
            Error Loading Invoices
          </Typography>
          <Typography variant="body" color="secondary" center>
            {error?.message || 'Something went wrong'}
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary }}>
      <StatusBar style="light" />
      
      <ScrollView 
        style={styles.container}
        contentContainerStyle={[
          styles.contentContainer, 
          { paddingBottom: Math.max(32, insets.bottom + 70) }
        ]}
        showsVerticalScrollIndicator={false}
      >
        {/* Header with Organization Selector */}
        <PageHeader 
          title="Invoices"
          subtitle="Manage your invoices"
        />
        
        {/* Search Bar - Using reusable SearchInput */}
        <SearchInput
          placeholder="Search invoices..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        
        {/* Filter Tabs */}
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false} 
          style={styles.filterScrollContainer}
          contentContainerStyle={styles.filterScrollContent}
        >
          {['all', 'draft', 'pending', 'paid', 'overdue'].map((filter) => {
            const count = getStatusCount(filter);
            const filterLabel = filter === 'all' ? 'All' : 
              filter.charAt(0).toUpperCase() + filter.slice(1);
            
            return (
              <Pressable 
                key={filter}
                style={[styles.filterPill, activeFilter === filter && styles.activeFilterPill]}
                onPress={() => setActiveFilter(filter)}
              >
                <Typography 
                  variant="label" 
                  color={activeFilter === filter ? 'white' : 'primary'}
                  style={styles.filterText}
                >
                  {filterLabel} ({count})
                </Typography>
              </Pressable>
            );
          })}
        </ScrollView>
        
        {/* Invoice List */}
        <View style={styles.invoiceList}>
          {filteredInvoices.length === 0 ? (
            <EmptyState 
              iconName="document-text-outline"
              title={searchQuery.length > 0 ? 'No invoices match your search' : 'No invoices added yet'}
              message="Create your first invoice using the + button below"
            />
          ) : (
            filteredInvoices.map(invoice => (
              <Pressable
                key={invoice.id}
                onPress={() => handleViewInvoice(invoice.id)}
                style={({ pressed }) => [
                  styles.invoiceCardContainer,
                  pressed && styles.invoiceCardPressed
                ]}
              >
                <Card style={styles.invoiceCard}>
                  <View style={styles.cardHeader}>
                    <Typography variant="body" bold color="primary" style={styles.invoiceNumber}>
                      {invoice.invoiceNumber}
                    </Typography>
                    <ActionMenu 
                      items={getInvoiceActions(invoice.id)}
                      style={styles.actionMenu}
                    />
                  </View>
                  
                  <View style={styles.clientRow}>
                    <Typography variant="body" bold style={styles.clientNameCard}>
                      {getClientDisplayName(invoice.clientId, invoice.clientName)}
                    </Typography>
                    <StatusBadge 
                      status={invoice.status} 
                      size="small"
                      style={styles.statusBadgeCard} 
                    />
                  </View>
                  
                  <View style={styles.cardFooter}>
                    <View style={styles.leftSection}>
                      <Typography variant="caption" color="secondary" style={styles.label}>
                        Amount
                      </Typography>
                      <Typography variant="body" bold style={styles.amount}>
                        {invoice.amount || `$${invoice.totals?.total?.toFixed(2) || '0.00'}`}
                      </Typography>
                    </View>
                    
                    <View style={styles.rightSection}>
                      <Typography variant="caption" color="secondary" style={styles.label}>
                        Due Date
                      </Typography>
                      <Typography variant="bodySmall" style={styles.dueDate}>
                        {formatShortDate(invoice.dueDate)}
                      </Typography>
                    </View>
                  </View>
                </Card>
              </Pressable>
            ))
          )}
        </View>
      </ScrollView>
      
      {/* Using reusable FloatingActionButton component */}
      <FloatingActionButton
        onPress={handleCreateInvoice}
        iconName="add"
        secondaryIconName="document-text"
        color={colors.primary}
        size={65}
        bottom={90}
        right={20}
      />

      <ConfirmationDialog
        visible={deleteDialogVisible}
        title="Delete Invoice"
        message={`Are you sure you want to delete invoice ${invoiceToDelete?.invoiceNumber}? This action cannot be undone.`}
        confirmText="Delete"
        destructive={true}
        onConfirm={confirmDeleteInvoice}
        onCancel={cancelDeleteInvoice}
      />

      <Toast
        visible={toastVisible}
        message={toastMessage}
        type={toastType}
        onHide={() => setToastVisible(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  contentContainer: {
    paddingBottom: 32,
  },
  filterScrollContainer: {
    marginTop: 16,
  },
  filterScrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 12,
  },
  filterPill: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 50,
    backgroundColor: 'rgba(52, 144, 243, 0.08)',
    marginRight: 10,
  },
  activeFilterPill: {
    backgroundColor: colors.primary,
  },
  invoiceList: {
    paddingHorizontal: 20,
    marginTop: 10,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 2,
    overflow: 'visible',
  },
  invoiceNumber: {
    fontSize: 16,
    fontWeight: '400',
    color: colors.primary,
  },
  clientRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  clientNameCard: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.primary,
    flex: 1,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  leftSection: {
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  rightSection: {
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  label: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.text.secondary,
    marginBottom: 4,
    textTransform: 'uppercase',
  },
  amount: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.primary,
  },
  dueDate: {
    fontSize: 14,
    color: colors.text.primary,
    fontWeight: '500',
  },
  statusBadgeCard: {
    alignSelf: 'flex-end',
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
  },
  invoiceCardContainer: {
    borderRadius: 16,
  },
  invoiceCardPressed: {
    opacity: 0.7,
    transform: [{ scale: 0.98 }],
  },
  actionMenu: {
    padding: 8,
  },
  invoiceCard: {
    borderRadius: 16,
    overflow: 'visible',
  },
});