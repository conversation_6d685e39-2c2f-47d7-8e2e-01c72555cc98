import { Client<PERSON><PERSON><PERSON>Form, KeyboardAwareView, Toast, Typography } from '@/components/ui';
import { colors } from '@/constants/Colors';
import { useCreateClient } from '@/services/client/create';
import { useActiveOrganizationId } from '@/stores/organization-selectors';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export default function CreateClientScreen() {
  const insets = useSafeAreaInsets();
  const { returnToInvoice } = useLocalSearchParams();
  const activeOrganizationId = useActiveOrganizationId();
  const { createClient } = useCreateClient();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error'>('success');

  const handleSubmit = async (formData: any) => {
    if (isSubmitting || !activeOrganizationId) return;
    
    setIsSubmitting(true);
    
    try {
      const newClientData = {
        name: formData.name,
        displayName: formData.displayName,
        company: formData.company,
        organizationId: activeOrganizationId,
        contact: {
          email: formData.email,
          phone: formData.phone || undefined,
        },
        address: {
          fullAddress: formData.address || undefined,
        },
        photo: formData.photo,
        notes: formData.notes,
        isActive: true,
        defaultTaxExempt: false,
      };

      // Add client using clean service hook
      await createClient(newClientData);
      
      // If returning to invoice, select this client automatically
      if (returnToInvoice === 'create-invoice') {
        // Assuming you have a way to select the client in the store
      }
      
      setToastMessage('Client created successfully!');
      setToastType('success');
      setToastVisible(true);
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      console.error('Error creating client:', error);
      setToastMessage('Failed to create client. Please try again.');
      setToastType('error');
      setToastVisible(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <View style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar style="dark" />
      
      {/* Header */}
      <View 
        style={[
          styles.header,
          { paddingTop: insets.top + 16 }
        ]}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleCancel}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Typography variant="body" style={styles.headerTitle}>
          New Client
        </Typography>
        <View style={styles.headerRightPlaceholder} />
      </View>

      {/* Content */}
      <KeyboardAwareView
        style={styles.scrollContainer}
        contentContainerStyle={styles.contentContainer}
      >
          <View style={styles.formContainer}>
            <Typography variant="h3" style={styles.title}>
              Create New Client
            </Typography>
            <Typography variant="body" color="secondary" style={styles.subtitle}>
              Add a new client to your contact list
            </Typography>
            
            <ClientCreationForm
              onSubmit={handleSubmit}
              onCancel={handleCancel}
              submitButtonText="Save Client"
              cancelButtonText="Cancel"
            />
          </View>
      </KeyboardAwareView>
      
      <Toast
        visible={toastVisible}
        message={toastMessage}
        type={toastType}
        onHide={() => setToastVisible(false)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.cardBackground,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 6,
    backgroundColor: colors.cardBackground,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
  },
  backButton: {
    padding: 4,
    marginLeft: -4,
  },
  headerRightPlaceholder: {
    width: 24 + 8,
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  formContainer: {
    flex: 1,
  },
  title: {
    marginBottom: 8,
  },
  subtitle: {
    marginBottom: 24,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
}); 