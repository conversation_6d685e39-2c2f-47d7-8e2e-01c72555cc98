import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import React, { useEffect, useState } from 'react';
import { Alert, Image, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Components
import { Button, Card, EmptyState, FormSection, Input, KeyboardAwareView, Modal, Toast, Typography } from '@/components/ui';
import { colors } from '@/constants/Colors';

// Services
import { useCreateInvoiceTemplate, useInvoiceTemplates, useUpdateInvoiceTemplate } from '@/services/templates/templates';

// Types
import { ColorScheme, InvoiceTemplate, UpdateInvoiceTemplateInput } from '@/defs/invoice-template';

// Mock organization ID - would come from auth context in real app
const ORGANIZATION_ID = 'org_001';

// Predefined color palettes
const COLOR_PALETTES: { id: string; name: string; colors: ColorScheme }[] = [
  {
    id: 'professional_blue',
    name: 'Professional Blue',
    colors: {
      primary: '#2563eb',
      secondary: '#64748b',
      accent: '#0ea5e9',
      text: '#1e293b',
      background: '#ffffff',
      border: '#e2e8f0',
    }
  },
  {
    id: 'elegant_green',
    name: 'Elegant Green',
    colors: {
      primary: '#059669',
      secondary: '#6b7280',
      accent: '#10b981',
      text: '#111827',
      background: '#ffffff',
      border: '#d1d5db',
    }
  },
  {
    id: 'creative_purple',
    name: 'Creative Purple',
    colors: {
      primary: '#7c3aed',
      secondary: '#64748b',
      accent: '#a855f7',
      text: '#1f2937',
      background: '#ffffff',
      border: '#e5e7eb',
    }
  },
];

export default function InvoiceDesignScreen() {
  const { templates, loading, error } = useInvoiceTemplates(ORGANIZATION_ID);
  const { updateTemplate, loading: updating } = useUpdateInvoiceTemplate();
  const { createTemplate, loading: creating } = useCreateInvoiceTemplate();

  // UI State
  const [selectedTemplate, setSelectedTemplate] = useState<InvoiceTemplate | null>(null);
  const [activeTab, setActiveTab] = useState<'templates' | 'customize' | 'preview'>('templates');
  const [showColorModal, setShowColorModal] = useState(false);
  const [filterCategory, setFilterCategory] = useState<string>('all');
  
  // Toast state
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error'>('success');

  // Form state for customization
  const [customization, setCustomization] = useState<UpdateInvoiceTemplateInput>({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Initialize customization form when template is selected
  useEffect(() => {
    if (selectedTemplate) {
      setCustomization({
        name: selectedTemplate.name,
        description: selectedTemplate.description,
        colorScheme: selectedTemplate.colorScheme,
        layout: selectedTemplate.layout,
        customization: selectedTemplate.customization,
      });
      setHasUnsavedChanges(false);
    }
  }, [selectedTemplate]);

  const showToast = (message: string, type: 'success' | 'error' = 'success') => {
    setToastMessage(message);
    setToastType(type);
    setToastVisible(true);
  };

  // Update customization field
  const updateCustomizationField = (field: keyof UpdateInvoiceTemplateInput, value: any) => {
    setCustomization(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);
  };

  // Apply color palette
  const applyColorPalette = (palette: ColorScheme) => {
    updateCustomizationField('colorScheme', palette);
    setShowColorModal(false);
    showToast('Color palette applied successfully!');
  };

  // Save template changes
  const handleSaveChanges = async () => {
    if (!selectedTemplate) return;

    try {
      await updateTemplate({
        templateId: selectedTemplate.id,
        ...customization,
      });
      setHasUnsavedChanges(false);
      showToast('Template updated successfully!');
    } catch (error) {
      showToast('Failed to update template. Please try again.', 'error');
    }
  };

  // Upload logo
  const handleLogoUpload = async () => {
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (!permissionResult.granted) {
      showToast('Please grant camera roll permissions to upload a logo.', 'error');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [3, 1],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      setCustomization(prev => ({
        ...prev,
        customization: {
          ...(prev.customization || {}),
          logoUrl: result.assets[0].uri,
        } as any
      }));
      setHasUnsavedChanges(true);
      showToast('Logo uploaded successfully!');
    }
  };

  // Filter templates by category
  const filteredTemplates = templates?.filter(template => 
    filterCategory === 'all' || template.category === filterCategory
  ) || [];

  // Get categories for filter
  const categories = [
    { id: 'all', name: 'All Templates' },
    { id: 'business', name: 'Business' },
    { id: 'creative', name: 'Creative' },
    { id: 'professional', name: 'Professional' },
    { id: 'minimal', name: 'Minimal' },
    { id: 'modern', name: 'Modern' },
  ];

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <EmptyState
          iconName="refresh"
          title="Loading Templates"
          message="Please wait while we load your invoice templates..."
        />
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <EmptyState
          iconName="alert-circle-outline"
          title="Error Loading Templates"
          message="Unable to load invoice templates. Please try again."
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {[
          { id: 'templates', label: 'Templates', icon: 'grid-outline' },
          { id: 'customize', label: 'Customize', icon: 'color-palette-outline' },
          { id: 'preview', label: 'Preview', icon: 'eye-outline' },
        ].map((tab) => (
          <TouchableOpacity
            key={tab.id}
            style={[styles.tab, activeTab === tab.id && styles.activeTab]}
            onPress={() => setActiveTab(tab.id as any)}
            disabled={tab.id !== 'templates' && !selectedTemplate}
          >
            <Ionicons 
              name={tab.icon as any} 
              size={20} 
              color={activeTab === tab.id ? 'white' : colors.text.secondary} 
            />
            <Typography 
              variant="caption" 
              style={{
                fontWeight: '500',
                color: activeTab === tab.id ? 'white' : (tab.id !== 'templates' && !selectedTemplate ? colors.disabled : colors.text.secondary)
              }}
            >
              {tab.label}
            </Typography>
          </TouchableOpacity>
        ))}
      </View>

      <KeyboardAwareView style={styles.scrollContainer}>
        {/* Templates Tab */}
        {activeTab === 'templates' && (
          <View style={styles.tabContent}>
            {/* Category Filter */}
            <View style={styles.filterContainer}>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View style={styles.filterButtons}>
                  {categories.map((category) => (
                    <TouchableOpacity
                      key={category.id}
                      style={[
                        styles.filterButton,
                        filterCategory === category.id && styles.activeFilterButton
                      ]}
                      onPress={() => setFilterCategory(category.id)}
                    >
                      <Typography 
                        variant="caption" 
                        style={{
                          fontWeight: '500',
                          color: filterCategory === category.id ? 'white' : colors.text.secondary
                        }}
                      >
                        {category.name}
                      </Typography>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            </View>

            {/* Templates Grid - Fixed for proper 2-column layout */}
            <View style={styles.templatesContainer}>
              <View style={styles.templatesGrid}>
                {filteredTemplates.map((template) => (
                  <TouchableOpacity
                    key={template.id}
                    style={[
                      styles.templateCard,
                      selectedTemplate?.id === template.id && styles.selectedTemplateCard
                    ]}
                    onPress={() => {
                      setSelectedTemplate(template);
                      setActiveTab('customize');
                    }}
                  >
                    <View style={styles.templateImageContainer}>
                      <Image source={{ uri: template.thumbnail }} style={styles.templateThumbnail} />
                      {selectedTemplate?.id === template.id && (
                        <View style={styles.selectedOverlay}>
                          <Ionicons name="checkmark-circle" size={24} color={colors.primary} />
                        </View>
                      )}
                    </View>
                    
                    <View style={styles.templateInfo}>
                      <Typography variant="body" bold numberOfLines={1}>
                        {template.name}
                      </Typography>
                      <Typography variant="caption" color="secondary" numberOfLines={2}>
                        {template.description}
                      </Typography>
                      
                      <View style={styles.templateBadges}>
                        {template.isPremium && (
                          <View style={styles.premiumBadge}>
                            <Typography variant="caption" style={styles.badgeText}>PRO</Typography>
                          </View>
                        )}
                        {template.isDefault && (
                          <View style={styles.defaultBadge}>
                            <Typography variant="caption" style={styles.badgeText}>DEFAULT</Typography>
                          </View>
                        )}
                      </View>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>
        )}

        {/* Customize Tab */}
        {activeTab === 'customize' && selectedTemplate && (
          <View style={styles.tabContent}>
            {/* Template Details Section */}
            <FormSection title="Template Details">
              <Input
                label="Template Name"
                value={customization.name || ''}
                onChangeText={(value) => updateCustomizationField('name', value)}
                placeholder="Enter template name"
              />
              <Input
                label="Description"
                value={customization.description || ''}
                onChangeText={(value) => updateCustomizationField('description', value)}
                placeholder="Enter template description"
                multiline
                numberOfLines={3}
              />
            </FormSection>

            {/* Color Scheme Section */}
            <View style={styles.sectionWithAction}>
              <Typography variant="h4" style={styles.sectionTitle}>Color Scheme</Typography>
              <Button
                title="Palettes"
                onPress={() => setShowColorModal(true)}
                variant="outline"
                size="small"
              />
            </View>
            <Card style={styles.colorCard}>
              {Object.entries(customization.colorScheme || selectedTemplate.colorScheme).map(([key, color]) => (
                <TouchableOpacity
                  key={key}
                  style={styles.colorRow}
                  onPress={() => {
                    Alert.alert('Color Picker', `Would open color picker for ${key}`);
                  }}
                >
                  <Typography variant="body" style={styles.colorLabel}>
                    {key.charAt(0).toUpperCase() + key.slice(1)}
                  </Typography>
                  <View style={[styles.colorSwatch, { backgroundColor: color }]} />
                </TouchableOpacity>
              ))}
            </Card>

            {/* Logo & Branding Section */}
            <FormSection title="Logo & Branding">
              <TouchableOpacity style={styles.logoUpload} onPress={handleLogoUpload}>
                {customization.customization?.logoUrl ? (
                  <Image 
                    source={{ uri: customization.customization.logoUrl }} 
                    style={styles.logoPreview} 
                  />
                ) : (
                  <View style={styles.logoPlaceholder}>
                    <Ionicons name="image-outline" size={32} color={colors.text.tertiary} />
                    <Typography variant="caption" color="tertiary" style={styles.logoPlaceholderText}>
                      Tap to upload logo
                    </Typography>
                  </View>
                )}
              </TouchableOpacity>

              <Input
                label="Footer Text"
                value={customization.customization?.footerText || ''}
                onChangeText={(value) => {
                  setCustomization(prev => ({
                    ...prev,
                    customization: {
                      ...(prev.customization || selectedTemplate?.customization),
                      footerText: value,
                    }
                  }));
                  setHasUnsavedChanges(true);
                }}
                placeholder="Thank you for your business!"
              />
            </FormSection>

            {/* Action Buttons */}
            <View style={styles.actionContainer}>
              <Button
                title={updating ? "Saving..." : "Save Changes"}
                onPress={handleSaveChanges}
                disabled={!hasUnsavedChanges || updating}
              />
            </View>
          </View>
        )}

        {/* Preview Tab */}
        {activeTab === 'preview' && selectedTemplate && (
          <View style={styles.tabContent}>
            <FormSection title="Template Preview">
              <View style={styles.previewHeader}>
                <Typography variant="h4">
                  {selectedTemplate.name} Preview
                </Typography>
                <View style={[styles.statusBadge, { backgroundColor: selectedTemplate.isActive ? colors.success : colors.warning }]}>
                  <Typography variant="caption" style={styles.statusText}>
                    {selectedTemplate.isActive ? "Active" : "Inactive"}
                  </Typography>
                </View>
              </View>
              
              <View style={styles.previewContent}>
                <View style={[styles.mockInvoice, { 
                  backgroundColor: customization.colorScheme?.background || selectedTemplate.colorScheme.background 
                }]}>
                  {/* Mock Invoice Header */}
                  <View style={[styles.mockHeader, { 
                    backgroundColor: customization.colorScheme?.primary || selectedTemplate.colorScheme.primary 
                  }]}>
                    <Typography variant="h4" style={{ color: 'white' }}>
                      INVOICE
                    </Typography>
                    <Typography variant="caption" style={{ color: 'rgba(255,255,255,0.8)' }}>
                      #{Math.floor(Math.random() * 10000)}
                    </Typography>
                  </View>
                  
                  {/* Mock Invoice Body */}
                  <View style={styles.mockBody}>
                    <View style={styles.mockSection}>
                      <Typography variant="caption" color="secondary">FROM</Typography>
                      <Typography variant="body" bold>Your Company</Typography>
                      <Typography variant="caption" color="secondary">123 Business St</Typography>
                    </View>
                    
                    <View style={styles.mockSection}>
                      <Typography variant="caption" color="secondary">TO</Typography>
                      <Typography variant="body" bold>Client Name</Typography>
                      <Typography variant="caption" color="secondary">456 Client Ave</Typography>
                    </View>
                    
                    <View style={styles.mockLine} />
                    <View style={styles.mockLineItem}>
                      <Typography variant="body">Service Description</Typography>
                      <Typography variant="body" bold>$100.00</Typography>
                    </View>
                    <View style={styles.mockLine} />
                    
                    <View style={styles.mockTotal}>
                      <Typography variant="body" bold>Total: $100.00</Typography>
                    </View>
                  </View>
                  
                  {/* Footer */}
                  <View style={styles.mockFooter}>
                    <Typography variant="caption" color="secondary" style={{ textAlign: 'center' }}>
                      {customization.customization?.footerText || selectedTemplate.customization?.footerText || 'Thank you for your business!'}
                    </Typography>
                  </View>
                </View>
              </View>
              
              <Button
                title="View Full Screen Preview"
                onPress={() => Alert.alert('Preview', 'Full screen preview would open here')}
                variant="outline"
              />
            </FormSection>
          </View>
        )}
      </KeyboardAwareView>

      {/* Color Palette Modal */}
      <Modal
        visible={showColorModal}
        onClose={() => setShowColorModal(false)}
      >
        <View style={styles.modalContent}>
          <Typography variant="h4" style={styles.modalTitle}>Color Palettes</Typography>
          <ScrollView>
            {COLOR_PALETTES.map((palette) => (
            <TouchableOpacity
              key={palette.id}
              style={styles.paletteItem}
              onPress={() => applyColorPalette(palette.colors)}
            >
              <View style={styles.paletteInfo}>
                <Typography variant="body" bold>{palette.name}</Typography>
                <View style={styles.paletteColors}>
                  {Object.values(palette.colors).slice(0, 4).map((color, index) => (
                    <View
                      key={index}
                      style={[styles.paletteColorSwatch, { backgroundColor: color }]}
                    />
                  ))}
                </View>
              </View>
            </TouchableOpacity>
                      ))}
          </ScrollView>
        </View>
      </Modal>

      {/* Toast */}
      <Toast
        visible={toastVisible}
        message={toastMessage}
        type={toastType}
        onHide={() => setToastVisible(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  
  // Header
  header: {
    padding: 20,
    paddingBottom: 16,
  },
  
  // Tabs
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: colors.cardBackground,
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 4,
    marginBottom: 20,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    gap: 6,
  },
  activeTab: {
    backgroundColor: colors.primary,
  },
  
  scrollContainer: {
    flex: 1,
  },
  tabContent: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  
  // Templates
  filterContainer: {
    marginBottom: 20,
  },
  filterButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: colors.cardBackground,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  activeFilterButton: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  
  // Templates Grid - FIXED
  templatesContainer: {
    flex: 1,
  },
  templatesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  templateCard: {
    width: '48%',
    backgroundColor: colors.cardBackground,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: colors.divider,
    marginBottom: 16,
  },
  selectedTemplateCard: {
    borderColor: colors.primary,
  },
  templateImageContainer: {
    position: 'relative',
  },
  templateThumbnail: {
    width: '100%',
    height: 120,
    backgroundColor: colors.background,
  },
  selectedOverlay: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 2,
  },
  templateInfo: {
    padding: 12,
  },
  templateBadges: {
    flexDirection: 'row',
    gap: 6,
    marginTop: 8,
  },
  premiumBadge: {
    backgroundColor: colors.warning,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  defaultBadge: {
    backgroundColor: colors.success,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  badgeText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 10,
  },
  
  // Customization
  sectionWithAction: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  colorCard: {
    padding: 0,
    marginBottom: 20,
  },
  colorRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  colorLabel: {
    flex: 1,
  },
  colorSwatch: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: colors.divider,
  },
  
  // Logo Upload
  logoUpload: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  logoPreview: {
    width: '100%',
    height: 80,
    backgroundColor: colors.background,
  },
  logoPlaceholder: {
    height: 80,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.divider,
    borderStyle: 'dashed',
    borderRadius: 12,
  },
  logoPlaceholderText: {
    marginTop: 4,
    textAlign: 'center',
  },
  
  // Actions
  actionContainer: {
    gap: 12,
    marginTop: 20,
  },
  
  // Preview
  previewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    color: 'white',
    fontWeight: '600',
  },
  previewContent: {
    marginBottom: 20,
  },
  mockInvoice: {
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors.divider,
  },
  mockHeader: {
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  mockBody: {
    padding: 20,
  },
  mockSection: {
    marginBottom: 16,
  },
  mockLine: {
    height: 1,
    backgroundColor: colors.divider,
    marginVertical: 8,
  },
  mockLineItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  mockTotal: {
    alignItems: 'flex-end',
    marginTop: 8,
  },
  mockFooter: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: colors.divider,
  },
  
  // Modal
  modalContent: {
    maxHeight: 400,
  },
  modalTitle: {
    marginBottom: 16,
  },
  paletteItem: {
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  paletteInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  paletteColors: {
    flexDirection: 'row',
    gap: 4,
  },
  paletteColorSwatch: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: colors.divider,
  },
}); 