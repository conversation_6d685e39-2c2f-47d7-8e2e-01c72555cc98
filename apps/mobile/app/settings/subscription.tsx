import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router, Stack } from 'expo-router';
import React, { useState } from 'react';
import { Dimensions, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// UI Components
import {
  ConfirmationDialog,
  EmptyState,
  KeyboardAwareView,
  Toast
} from '@/components/ui';
import { colors } from '@/constants/Colors';

// Services
import { useCancelSubscription, useChangeSubscription, useUserSubscription } from '@/services/subscription/subscription';

// Stores and Hooks
import { useActiveOrganizationId, useHasActiveOrganization } from '@/stores/organization-selectors';

const { width } = Dimensions.get('window');

interface PlanFeature {
  text: string;
  included: boolean;
}

interface Plan {
  id: string;
  name: string;
  price: number;
  interval: string;
  features: PlanFeature[];
  popular?: boolean;
  color?: string;
  gradient?: string[];
  limits: {
    invoicesPerMonth: number | 'unlimited';
    clients: number | 'unlimited';
  };
}

const PLANS: Plan[] = [
  {
    id: 'plan_starter',
    name: 'Free',
    price: 0,
    interval: 'month',
    color: colors.text.secondary,
    gradient: [colors.text.secondary, colors.text.tertiary],
    features: [
      { text: '5 invoices per month', included: true },
      { text: 'Up to 3 clients', included: true },
      { text: 'Basic templates', included: true },
      { text: 'Email support', included: true },
      { text: 'Mobile app access', included: true },
      { text: 'Custom branding', included: false },
      { text: 'Advanced reporting', included: false },
    ],
    limits: { invoicesPerMonth: 5, clients: 3 }
  },
  {
    id: 'plan_professional',
    name: 'Professional',
    price: 19.99,
    interval: 'month',
    popular: true,
    color: colors.primary,
    gradient: [colors.primary, colors.primaryLight],
    features: [
      { text: 'Unlimited invoices', included: true },
      { text: 'Up to 50 clients', included: true },
      { text: 'Premium templates', included: true },
      { text: 'Priority support', included: true },
      { text: 'Custom branding', included: true },
      { text: 'Advanced reporting', included: true },
      { text: 'API access', included: true },
    ],
    limits: { invoicesPerMonth: 'unlimited', clients: 50 }
  },
  {
    id: 'plan_enterprise',
    name: 'Enterprise',
    price: 99,
    interval: 'month',
    color: colors.primaryLighter,
    gradient: [colors.primaryLighter, colors.primary],
    features: [
      { text: 'Everything in Professional', included: true },
      { text: 'Unlimited clients', included: true },
      { text: 'Team collaboration', included: true },
      { text: '24/7 dedicated support', included: true },
      { text: 'White-label branding', included: true },
      { text: 'Advanced analytics', included: true },
      { text: 'Full API access', included: true },
    ],
    limits: { invoicesPerMonth: 'unlimited', clients: 'unlimited' }
  }
];

export default function SubscriptionScreen() {
  const insets = useSafeAreaInsets();
  
  // Organization context
  const hasActiveOrganization = useHasActiveOrganization();
  const organizationId = useActiveOrganizationId();

  // Data hooks
  const { subscription, loading, error } = useUserSubscription();
  const { changeSubscription, loading: updating } = useChangeSubscription();
  const { cancelSubscription, loading: canceling } = useCancelSubscription();

  // UI state
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [showPlanChangeDialog, setShowPlanChangeDialog] = useState(false);
  const [selectedNewPlan, setSelectedNewPlan] = useState<string | null>(null);
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error'>('success');

  // Organization selection guard
  if (!hasActiveOrganization) {
    return (
      <View style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <EmptyState
          iconName="business-outline"
          title="Select an Organization"
          message="Please select an organization to view subscription details"
        />
      </View>
    );
  }

  const showToast = (message: string, type: 'success' | 'error' = 'success') => {
    setToastMessage(message);
    setToastType(type);
    setToastVisible(true);
  };

  // Handle subscription upgrade/downgrade
  const handlePlanChange = async (planId: string) => {
    if (subscription?.planId === planId) return;
    
    const targetPlan = PLANS.find(p => p.id === planId);
    if (!targetPlan) return;
    
    setSelectedNewPlan(planId);
    setShowPlanChangeDialog(true);
  };

  const confirmPlanChange = async () => {
    if (!selectedNewPlan) return;
    
    try {
      setShowPlanChangeDialog(false);
      await changeSubscription({
        newPlanId: selectedNewPlan,
        effectiveDate: 'immediately',
        prorationBehavior: 'create_prorations',
      });
      
      const plan = PLANS.find(p => p.id === selectedNewPlan);
      showToast(`Successfully ${subscription?.planId === 'plan_starter' ? 'upgraded' : 'changed'} to ${plan?.name} plan!`);
    } catch (error) {
      showToast('Failed to change subscription. Please try again.', 'error');
    } finally {
      setSelectedNewPlan(null);
    }
  };

  const cancelPlanChange = () => {
    setShowPlanChangeDialog(false);
    setSelectedNewPlan(null);
  };

  // Handle subscription cancellation
  const handleCancel = async () => {
    try {
      await cancelSubscription({ cancelAtPeriodEnd: true });
      showToast('Subscription canceled successfully. You\'ll have access until the end of your billing period.');
      setShowCancelDialog(false);
    } catch (error) {
      showToast('Failed to cancel subscription. Please try again.', 'error');
    }
  };

  // Handle billing portal
  const handleBillingPortal = async () => {
    try {
      // In development, show a mock dialog instead of opening browser
      showToast('Billing portal would open here in production. This is a mock implementation.', 'success');
    } catch (error) {
      showToast('Unable to open billing portal.', 'error');
    }
  };

  const getCurrentPlan = () => {
    return PLANS.find(plan => plan.id === subscription?.planId) || PLANS[0];
  };

  const getUsageData = () => {
    // Mock usage data - in real app this would come from the API
    return {
      invoicesSent: 3,
      clients: 2,
    };
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <EmptyState
          iconName="refresh"
          title="Loading Subscription"
          message="Please wait while we load your subscription details..."
        />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <EmptyState
          iconName="alert-circle-outline"
          title="Unable to Load Subscription"
          message="There was an error loading your subscription details. Please try again."
        />
      </View>
    );
  }

  const currentPlan = getCurrentPlan();
  const usage = getUsageData();
  const isCurrentPlan = (planId: string) => subscription?.planId === planId;

  return (
    <View style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />
      
      {/* Standard Header */}
      <View style={[styles.header, { paddingTop: insets.top }]}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>
            Subscription
          </Text>
        </View>
        <View style={styles.headerRight} />
      </View>

              <KeyboardAwareView 
          style={styles.scrollContainer}
          contentContainerStyle={{ paddingBottom: insets.bottom + 40 }}
          showsVerticalScrollIndicator={false}
        >
        {/* Elegant Current Plan Overview */}
        <View style={styles.currentPlanSection}>
          <View style={[styles.currentPlanCard, { backgroundColor: colors.primary }]}>
            <View style={styles.currentPlanContent}>
              <View style={styles.currentPlanHeader}>
                <View style={styles.planIcon}>
                  <Ionicons 
                    name={subscription?.status === 'active' ? 'checkmark-circle' : 'time'} 
                    size={24} 
                    color="white" 
                  />
                </View>
                <View style={styles.currentPlanInfo}>
                  <Text style={styles.currentPlanName}>{currentPlan.name} Plan</Text>
                  <Text style={styles.currentPlanPrice}>
                    ${currentPlan.price}<Text style={styles.currentPlanInterval}>/{currentPlan.interval}</Text>
                  </Text>
                  <View style={styles.statusIndicator}>
                    <View style={[styles.statusDot, { backgroundColor: subscription?.status === 'active' ? colors.success : colors.warning }]} />
                    <Text style={styles.statusText}>
                      {subscription?.status === 'active' ? 'Active' : 'Inactive'}
                    </Text>
                  </View>
                </View>
              </View>

              {/* Elegant Usage Statistics */}
              <View style={styles.usageContainer}>
                <View style={styles.usageCard}>
                  <Text style={styles.usageLabel}>Invoices</Text>
                  <Text style={styles.usageValue}>
                    {usage.invoicesSent}
                    <Text style={styles.usageLimit}>
                      /{currentPlan.limits.invoicesPerMonth === 'unlimited' ? '∞' : currentPlan.limits.invoicesPerMonth}
                    </Text>
                  </Text>
                  <View style={styles.usageBar}>
                    <View style={[styles.usageProgress, { 
                      width: currentPlan.limits.invoicesPerMonth === 'unlimited' ? '20%' : 
                        `${(usage.invoicesSent / (currentPlan.limits.invoicesPerMonth as number)) * 100}%` 
                    }]} />
                  </View>
                </View>
                
                <View style={styles.usageCard}>
                  <Text style={styles.usageLabel}>Clients</Text>
                  <Text style={styles.usageValue}>
                    {usage.clients}
                    <Text style={styles.usageLimit}>
                      /{currentPlan.limits.clients === 'unlimited' ? '∞' : currentPlan.limits.clients}
                    </Text>
                  </Text>
                  <View style={styles.usageBar}>
                    <View style={[styles.usageProgress, { 
                      width: currentPlan.limits.clients === 'unlimited' ? '20%' : 
                        `${(usage.clients / (currentPlan.limits.clients as number)) * 100}%` 
                    }]} />
                  </View>
                </View>
              </View>

              {/* Billing Info */}
              {subscription && subscription.planId !== 'plan_starter' && (
                <TouchableOpacity style={styles.billingButton} onPress={handleBillingPortal}>
                  <Ionicons name="card-outline" size={20} color="white" />
                  <Text style={styles.billingButtonText}>Manage Billing</Text>
                  <Ionicons name="chevron-forward" size={16} color="rgba(255,255,255,0.7)" />
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>

        {/* Elegant Plans Section */}
        <View style={styles.plansSection}>
          <Text style={styles.sectionTitle}>Choose Your Plan</Text>
          <Text style={styles.sectionSubtitle}>Select the plan that best fits your business needs</Text>
          
          {PLANS.map((plan, index) => {
            const isSelected = isCurrentPlan(plan.id);
            
            return (
              <View key={plan.id} style={styles.planWrapper}>
                {plan.popular && (
                  <View style={styles.popularBadge}>
                    <View style={[styles.popularBadgeGradient, { backgroundColor: colors.warning }]}>
                      <Ionicons name="star" size={12} color="white" />
                      <Text style={styles.popularText}>Most Popular</Text>
                    </View>
                  </View>
                )}
                
                <View style={[styles.planCard, isSelected && styles.selectedPlanCard]}>
                  <LinearGradient
                    colors={isSelected ? (plan.id === 'plan_starter' ? [colors.text.secondary, colors.text.tertiary] : [colors.primary, colors.primaryLight]) : ['#FFFFFF', '#FFFFFF']}
                    style={styles.planCardGradient}
                  >
                    <View style={styles.planCardContent}>
                      {/* Plan Header */}
                      <View style={styles.planCardHeader}>
                        <View style={[styles.planCardIcon, { backgroundColor: isSelected ? 'rgba(255,255,255,0.2)' : plan.color }]}>
                          <Ionicons 
                            name={plan.id === 'plan_starter' ? 'gift' : plan.id === 'plan_professional' ? 'rocket' : 'business'} 
                            size={20} 
                            color="white" 
                          />
                        </View>
                        <View style={styles.planCardTitleContainer}>
                          <Text style={[styles.planCardTitle, isSelected && styles.selectedPlanCardTitle]}>
                            {plan.name}
                          </Text>
                          <View style={styles.planCardPricing}>
                            <Text style={[styles.planCardPrice, isSelected && styles.selectedPlanCardPrice]}>
                              ${plan.price}
                            </Text>
                            <Text style={[styles.planCardInterval, isSelected && styles.selectedPlanCardInterval]}>
                              /{plan.interval}
                            </Text>
                          </View>
                        </View>
                                                                         {isSelected && (
                          <View style={styles.selectedBadge}>
                            <View style={styles.selectedBadgeIcon}>
                              <Ionicons name="checkmark" size={16} color={colors.primary} />
                            </View>
                          </View>
                        )}
                      </View>

                                             {/* Features List */}
                       <View style={styles.featuresList}>
                         {plan.features.map((feature, featureIndex) => (
                           <View key={featureIndex} style={styles.featureItem}>
                                                                                   <Ionicons 
                             name={feature.included ? "checkmark" : "close"}
                             size={16} 
                             color={feature.included ? (isSelected ? 'white' : '#2E7D32') : (isSelected ? 'rgba(255,255,255,0.6)' : colors.text.tertiary)}
                             style={styles.featureIcon}
                           />
                             <Text style={[
                               styles.featureText,
                               !feature.included && styles.featureTextDisabled,
                               isSelected && styles.selectedFeatureText
                             ]}>
                               {feature.text}
                             </Text>
                           </View>
                         ))}
                       </View>

                                            {/* Action Button */}
                      {isSelected ? (
                        <View style={[styles.currentPlanBadge, { backgroundColor: 'rgba(255,255,255,0.2)', borderWidth: 1, borderColor: 'rgba(255,255,255,0.3)' }]}>
                          <Ionicons name="checkmark-circle" size={20} color="white" />
                          <Text style={styles.currentPlanText}>Active Plan</Text>
                        </View>
                      ) : (
                        <TouchableOpacity
                          style={[styles.planActionButton, { 
                            backgroundColor: colors.cardBackground,
                            borderWidth: 2,
                            borderColor: colors.primary
                          }]}
                          onPress={() => handlePlanChange(plan.id)}
                          disabled={updating}
                        >
                          <Text style={[styles.planActionButtonText, { color: colors.primary }]}>
                            {subscription?.planId === 'plan_starter' ? `Upgrade to ${plan.name}` : `Switch to ${plan.name}`}
                          </Text>
                          <Ionicons name="arrow-forward" size={16} color={colors.primary} />
                        </TouchableOpacity>
                      )}
                    </View>
                  </LinearGradient>
                </View>
              </View>
            );
          })}
        </View>

        {/* Cancel Subscription Section */}
        {subscription && subscription.planId !== 'plan_starter' && (
          <View style={styles.cancelSection}>
                         <View style={styles.cancelCard}>
               <View style={styles.cancelIcon}>
                 <Ionicons name="warning-outline" size={24} color={colors.error} />
               </View>
              <View style={styles.cancelContent}>
                <Text style={styles.cancelTitle}>Cancel Subscription</Text>
                <Text style={styles.cancelDescription}>
                  Cancel your subscription and downgrade to the free plan at the end of your current billing period.
                </Text>
              </View>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setShowCancelDialog(true)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </KeyboardAwareView>

      {/* Plan Change Confirmation Dialog */}
      <ConfirmationDialog
        visible={showPlanChangeDialog}
        title="Change Plan?"
        message={selectedNewPlan ? `Are you sure you want to switch to the ${PLANS.find(p => p.id === selectedNewPlan)?.name} plan? Changes will take effect immediately.` : ''}
        confirmText="Change Plan"
        cancelText="Cancel"
        onConfirm={confirmPlanChange}
        onCancel={cancelPlanChange}
        destructive={false}
      />

      {/* Cancel Confirmation Dialog */}
      <ConfirmationDialog
        visible={showCancelDialog}
        title="Cancel Subscription?"
        message="Are you sure you want to cancel your subscription? You'll lose access to premium features at the end of your billing period."
        confirmText="Cancel Subscription"
        cancelText="Keep Subscription"
        onConfirm={handleCancel}
        onCancel={() => setShowCancelDialog(false)}
        destructive={true}
      />

      {/* Toast Notifications */}
      <Toast
        visible={toastVisible}
        message={toastMessage}
        type={toastType}
        onHide={() => setToastVisible(false)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  
  // Header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 12,
    backgroundColor: colors.cardBackground,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  backButton: {
    padding: 4,
    marginRight: 16,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  headerRight: {
    width: 24 + 16,
  },
  
  // Content
  scrollContainer: {
    flex: 1,
  },
  
  // Current Plan Section
  currentPlanSection: {
    paddingHorizontal: 24,
    paddingTop: 32,
    paddingBottom: 20,
  },
  currentPlanCard: {
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 12,
  },
  currentPlanContent: {
    padding: 28,
  },
  currentPlanHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 28,
  },
  planIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 20,
  },
  currentPlanInfo: {
    flex: 1,
  },
  currentPlanName: {
    fontSize: 20,
    fontWeight: '700',
    color: 'white',
    marginBottom: 4,
  },
  currentPlanPrice: {
    fontSize: 32,
    fontWeight: '800',
    color: 'white',
    marginBottom: 8,
  },
  currentPlanInterval: {
    fontSize: 16,
    fontWeight: '500',
    color: 'rgba(255,255,255,0.8)',
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'rgba(255,255,255,0.9)',
  },
  
  // Usage Container
  usageContainer: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 24,
  },
  usageCard: {
    flex: 1,
    backgroundColor: 'rgba(255,255,255,0.15)',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  usageLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: 'rgba(255,255,255,0.8)',
    textTransform: 'uppercase',
    letterSpacing: 1,
    marginBottom: 8,
  },
  usageValue: {
    fontSize: 24,
    fontWeight: '800',
    color: 'white',
    marginBottom: 12,
  },
  usageLimit: {
    fontSize: 16,
    fontWeight: '500',
    color: 'rgba(255,255,255,0.7)',
  },
  usageBar: {
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  usageProgress: {
    height: '100%',
    backgroundColor: 'white',
    borderRadius: 2,
  },
  
  // Billing Button
  billingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.15)',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  billingButtonText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginLeft: 12,
  },
  
  // Plans Section
  plansSection: {
    paddingHorizontal: 24,
    paddingTop: 20,
  },
  sectionTitle: {
    fontSize: 28,
    fontWeight: '800',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  
  // Plan Cards
  planWrapper: {
    marginBottom: 20,
    position: 'relative',
  },
  popularBadge: {
    position: 'absolute',
    top: -8,
    alignSelf: 'center',
    zIndex: 10,
  },
  popularBadgeGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  popularText: {
    fontSize: 12,
    fontWeight: '700',
    color: 'white',
  },
  planCard: {
    borderRadius: 20,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: colors.divider,
  },
  selectedPlanCard: {
    borderColor: colors.primary,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 24,
    elevation: 12,
  },
  planCardGradient: {
    flex: 1,
  },
  planCardContent: {
    padding: 24,
  },
  
  // Plan Card Header
  planCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  planCardIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  planCardTitleContainer: {
    flex: 1,
  },
  planCardTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 4,
  },
  selectedPlanCardTitle: {
    color: 'white',
  },
  planCardPricing: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  planCardPrice: {
    fontSize: 28,
    fontWeight: '800',
    color: colors.text.primary,
  },
  selectedPlanCardPrice: {
    color: 'white',
  },
  planCardInterval: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.secondary,
    marginLeft: 4,
  },
  selectedPlanCardInterval: {
    color: 'rgba(255,255,255,0.8)',
  },
  selectedBadge: {
    marginLeft: 12,
  },
  selectedBadgeIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  // Features List
  featuresList: {
    marginBottom: 24,
    gap: 12,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  featureIcon: {
    marginRight: 12,
  },
  featureText: {
    fontSize: 15,
    fontWeight: '500',
    color: colors.text.primary,
    flex: 1,
  },
  selectedFeatureText: {
    color: 'rgba(255,255,255,0.9)',
  },
  featureTextDisabled: {
    color: colors.text.tertiary,
  },
  
  // Plan Actions
  currentPlanBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    gap: 8,
  },
  currentPlanText: {
    fontSize: 16,
    fontWeight: '700',
    color: 'white',
  },
  planActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    gap: 8,
  },
  planActionButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: 'white',
  },
  
  // Cancel Section
  cancelSection: {
    paddingHorizontal: 24,
    paddingTop: 32,
  },
  cancelCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.cardBackground,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: colors.primaryVeryLight,
  },
  cancelIcon: {
    marginRight: 16,
  },
  cancelContent: {
    flex: 1,
  },
  cancelTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 4,
  },
  cancelDescription: {
    fontSize: 14,
    color: colors.text.secondary,
    lineHeight: 20,
  },
  cancelButton: {
    backgroundColor: colors.primaryVeryLight,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginLeft: 12,
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.error,
  },
}); 