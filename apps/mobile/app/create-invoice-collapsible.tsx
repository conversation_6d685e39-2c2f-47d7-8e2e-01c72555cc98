/**
 * CreateInvoiceCollapsibleScreen
 * 
 * This screen implements persistent UI preferences using Zustand + AsyncStorage.
 * User preferences like collapsible section states and footer pinning persist across app restarts.
 * 
 * Persistent UI preferences managed by settingsStore:
 * - isItemsCollapsed: Items & Services section collapsed state
 * - isTaxCollapsed: Tax Configuration section collapsed state  
 * - isFooterPinned: Footer pinned at bottom state
 */

import { ClientSelectorModal } from '@/components/invoice/ClientSelectorModal';
import { InvoiceAdditionalDetails } from '@/components/invoice/InvoiceAdditionalDetails';
import { InvoiceBasicFields } from '@/components/invoice/InvoiceBasicFields';
import { InvoiceDetailsModal } from '@/components/invoice/InvoiceDetailsModal';
import { InvoiceHeader } from '@/components/invoice/InvoiceHeader';
import { InvoiceItemsAndServices } from '@/components/invoice/InvoiceItemsAndServices';
import { InvoiceTaxAndPayment } from '@/components/invoice/InvoiceTaxAndPayment';
import { ItemCreationModal } from '@/components/invoice/ItemCreationModal';
import { ServicesSelectorModal } from '@/components/invoice/ServicesSelectorModal';
import { SignatureModal } from '@/components/invoice/SignatureModal';
import {
	PaymentMethodsSheet,
	Typography
} from '@/components/ui';
import { InvoiceFooter } from '@/components/ui/InvoiceFooter';
import { colors } from '@/constants/Colors';
import { useTaxOptions } from '@/core/hooks/use-tax-options';
import { useClients } from '@/services/client/clients';
import { useCreateClient } from '@/services/client/create';
import { useInvoice, useInvoices } from '@/services/invoice/invoices';
import { useUpdateInvoice } from '@/services/invoice/update';
import { useCreateService } from '@/services/service/create';
import { useServices } from '@/services/service/services';
import { useInvoiceStore, useOrganizationStore, useSettingsStore } from '@/stores';
import { useActiveOrganizationId } from '@/stores/organization-selectors';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { startTransition, useEffect, useMemo, useState } from 'react';
import {
	Animated,
	StyleSheet,
	View
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

export default function CreateInvoiceCollapsibleScreen() {
	const insets = useSafeAreaInsets();
	const { editInvoiceId } = useLocalSearchParams<{ editInvoiceId?: string }>();
	const isEditMode = !!editInvoiceId;

	// Zustand stores
	const activeOrganizationId = useActiveOrganizationId();
	const invoiceStore = useInvoiceStore();
	const settingsStore = useSettingsStore();
	const organizationStore = useOrganizationStore();
	const { data: taxOptions = [] } = useTaxOptions();
	const { clients = [] } = useClients();
	const { services = [] } = useServices();
	const { invoices = [] } = useInvoices();
	const { createClient } = useCreateClient();
	const { createService } = useCreateService();
	const { updateInvoice } = useUpdateInvoice();

	// Fetch existing invoice data if in edit mode
	const { invoice: existingInvoice, loading: invoiceLoading } = useInvoice(editInvoiceId || '');

	// Track if we've already loaded invoice data to prevent infinite loops
	const hasLoadedInvoiceData = React.useRef(false);

	// Load existing invoice data into store when available
	useEffect(() => {
		if (isEditMode && existingInvoice && !invoiceLoading && !hasLoadedInvoiceData.current) {
			invoiceStore.loadInvoiceData(existingInvoice);
			hasLoadedInvoiceData.current = true;
		}
	}, [isEditMode, existingInvoice, invoiceLoading]);

	// Reset the flag when editInvoiceId changes (new edit session)
	useEffect(() => {
		hasLoadedInvoiceData.current = false;
	}, [editInvoiceId]);

	// Reset form when creating a new invoice (not in edit mode)
	useEffect(() => {
		if (!isEditMode) {
			invoiceStore.resetForm();
		}
	}, [isEditMode]);

	// Generate initial invoice number for new invoices
	useEffect(() => {
		if (!isEditMode && activeOrganizationId) {
			// Get current organization data
			const currentOrganization = organizationStore.organizations.find((org: { id: string; nickname: string }) => org.id === activeOrganizationId);
			if (currentOrganization && invoices) {
				// Generate the next invoice number
				invoiceStore.generateNextInvoiceNumber(currentOrganization.nickname, invoices.length);
			}
		}
	}, [isEditMode, activeOrganizationId, invoices, organizationStore.organizations]);

	// Get UI preferences from persistent settings
	const { uiPreferences } = settingsStore;
	const { isItemsCollapsed, isTaxCollapsed, isFooterPinned } = uiPreferences;

	// Client selector states and animations
	const [showClientSelector, setShowClientSelector] = useState(false);
	const [clientSheetAnimation] = useState(new Animated.Value(0));

	// Services selector states and animations
	const [showServicesSelector, setShowServicesSelector] = useState(false);
	const [servicesSheetAnimation] = useState(new Animated.Value(0));

	// Item creation/editing states and animations
	const [showItemSheet, setShowItemSheet] = useState(false);
	const [itemSheetAnimation] = useState(new Animated.Value(0));
	const [showNewItemForm, setShowNewItemForm] = useState(false);
	const [editingItem, setEditingItem] = useState<string | null>(null);
	const [selectedServiceForEdit, setSelectedServiceForEdit] = useState<any>(null);

	const [showSignatureModal, setShowSignatureModal] = useState(false);
	const [signatureAnimation] = useState(new Animated.Value(0));

	// Invoice details sheet state
	const [showInvoiceDetailsSheet, setShowInvoiceDetailsSheet] = useState(false);
	const [invoiceDetailsAnimation] = useState(new Animated.Value(0));

	// Get data directly from React Query (services are already filtered by organization in the hook)
	const availableClients = useMemo(() =>
		clients.filter(client => client.organizationId === activeOrganizationId),
		[clients, activeOrganizationId]
	);
	const availableServices = services; // React Query already filters by organization
	const selectedClientObj = useMemo(() =>
		clients.find(client => client.id === (invoiceStore.selectedClientId || '') && client.organizationId === activeOrganizationId),
		[clients, invoiceStore.selectedClientId, activeOrganizationId]
	);

	// Check if required fields are filled
	const isFormValid = invoiceStore.isFormValid();

	// File picker states
	const [isPickingFile, setIsPickingFile] = useState(false);

	// Payment methods sheet states and animations
	const [showPaymentMethodsSheet, setShowPaymentMethodsSheet] = useState(false);
	const [paymentMethodsAnimation] = useState(new Animated.Value(0));

	// Show loading state when fetching existing invoice data in edit mode
	if (isEditMode && invoiceLoading) {
		return (
			<SafeAreaView style={styles.container}>
				<InvoiceHeader 
					isEditMode={isEditMode}
					onBack={() => router.back()}
					insets={insets}
					isLoading={true}
				/>

				<View style={styles.centerContainer}>
					<Typography variant="body" color="secondary">Loading invoice data...</Typography>
				</View>
			</SafeAreaView>
		);
	}

	// Toggle Items & Services section - now using persistent storage
	const toggleItemsSection = () => {
		startTransition(() => {
			settingsStore.setItemsCollapsed(!isItemsCollapsed);
		});
	};

	// Toggle Tax section - now using persistent storage
	const toggleTaxSection = () => {
		startTransition(() => {
			settingsStore.setTaxCollapsed(!isTaxCollapsed);
		});
	};

	// Toggle Footer pinned state - now using persistent storage
	const toggleFooterPinned = () => {
		startTransition(() => {
			settingsStore.setFooterPinned(!isFooterPinned);
		});
	};

	// Animation helpers
	const openSignatureModal = () => {
		setShowSignatureModal(true);
		Animated.timing(signatureAnimation, {
			toValue: 1,
			duration: 300,
			useNativeDriver: true,
		}).start();
	};

	const closeSignatureModal = () => {
		Animated.timing(signatureAnimation, {
			toValue: 0,
			duration: 200,
			useNativeDriver: true,
		}).start(() => {
			setShowSignatureModal(false);
		});
	};

	// Client selector functions (copied from create-invoice.tsx)
	const openClientSheet = () => {
		setShowClientSelector(true);
		Animated.timing(clientSheetAnimation, {
			toValue: 1,
			duration: 300,
			useNativeDriver: true,
		}).start();
	};

	const closeClientSheet = () => {
		Animated.timing(clientSheetAnimation, {
			toValue: 0,
			duration: 200,
			useNativeDriver: true,
		}).start(() => {
			setShowClientSelector(false);
		});
	};

	const handleSelectClient = (clientId: string) => {
		// Use startTransition to ensure proper state update scheduling
		startTransition(() => {
			invoiceStore.setSelectedClient(clientId);
		});
		
		// Delay closing to allow state update to complete
		setTimeout(() => {
			closeClientSheet();
		}, 10);
	};

	const handleClientCreationSubmit = async (clientData: any) => {
		// Add client using React Query mutation
		if (!activeOrganizationId) return;

		try {
			const newClient = await createClient({
				...clientData
			});

			// Select this client automatically with startTransition
			startTransition(() => {
				invoiceStore.setSelectedClient(newClient.id);
			});
		} catch (error) {
			console.error('Failed to create client:', error);
		}
	};

	// Services selector functions (copied from create-invoice.tsx)
	const openServicesSheet = () => {
		setShowServicesSelector(true);
		Animated.timing(servicesSheetAnimation, {
			toValue: 1,
			duration: 300,
			useNativeDriver: true,
		}).start();
	};

	const closeServicesSheet = () => {
		Animated.timing(servicesSheetAnimation, {
			toValue: 0,
			duration: 200,
			useNativeDriver: true,
		}).start(() => {
			setShowServicesSelector(false);
		});
	};

	const handleSelectService = (service: any) => {
		// Instead of directly adding the service, open the item creation modal 
		// with the service data pre-populated for editing
		setEditingItem(null); // This is for a new item, not editing existing
		setShowNewItemForm(true);
		setSelectedServiceForEdit(service); // Store the selected service
		
		// Close services sheet and open item sheet
		closeServicesSheet();
		openItemSheet();
	};

	const handleServiceCreationSubmit = async (serviceData: any) => {
		if (!activeOrganizationId) return;

		try {
			// Create service via React Query mutation
			const newServiceData = {
				name: serviceData.name,
				description: serviceData.description,
				pricing: {
					rate: parseFloat(serviceData.price),
					unit: serviceData.unit,
					currency: 'USD',
				},
				organizationId: activeOrganizationId,
				isActive: true,
				taxable: true,
				tags: [],
			};

			await createService(newServiceData);
			console.log('Service created successfully');
		} catch (error) {
			console.error('Failed to create service:', error);
		}
	};

	// Item sheet functions (copied from create-invoice.tsx)
	const openItemSheet = () => {
		setShowItemSheet(true);
		Animated.timing(itemSheetAnimation, {
			toValue: 1,
			duration: 300,
			useNativeDriver: true,
		}).start();
	};

	const closeItemSheet = () => {
		Animated.timing(itemSheetAnimation, {
			toValue: 0,
			duration: 200,
			useNativeDriver: true,
		}).start(() => {
			setShowItemSheet(false);
			setShowNewItemForm(false);
			setEditingItem(null);
			setSelectedServiceForEdit(null);
		});
	};

	const handleItemCreationSubmit = async (itemData: any) => {
		// Use startTransition for all store updates to prevent animation conflicts
		startTransition(() => {
			if (editingItem) {
				// Update existing item
				invoiceStore.updateLineItem(editingItem, 'description', itemData.name);
				invoiceStore.updateLineItem(editingItem, 'itemDescription', itemData.description);
				invoiceStore.updateLineItem(editingItem, 'price', itemData.price);
				invoiceStore.updateLineItem(editingItem, 'quantity', itemData.quantity);
				invoiceStore.updateLineItem(editingItem, 'unit', itemData.unit);
				// Update discount fields
				if (itemData.discount) {
					invoiceStore.updateLineItem(editingItem, 'discount', itemData.discount);
					invoiceStore.updateLineItem(editingItem, 'discountType', itemData.discountType || 'percentage');
				}
			} else {
				// Create new item
				invoiceStore.addLineItem();
				const lastItem = invoiceStore.lineItems[invoiceStore.lineItems.length - 1];
				invoiceStore.updateLineItem(lastItem.id, 'description', itemData.name);
				invoiceStore.updateLineItem(lastItem.id, 'itemDescription', itemData.description);
				invoiceStore.updateLineItem(lastItem.id, 'price', itemData.price);
				invoiceStore.updateLineItem(lastItem.id, 'quantity', itemData.quantity);
				invoiceStore.updateLineItem(lastItem.id, 'unit', itemData.unit);
				// Add discount fields
				if (itemData.discount) {
					invoiceStore.updateLineItem(lastItem.id, 'discount', itemData.discount);
					invoiceStore.updateLineItem(lastItem.id, 'discountType', itemData.discountType || 'percentage');
				}
			}
		});

		// Handle service creation outside of startTransition since it's async
		if (!editingItem && itemData.saveAsService) {
			// Map item units to service units
			let serviceUnit: 'fixed' | 'hour' | 'day' | 'month' | 'project' = 'fixed';
			if (['hour', 'day', 'month', 'project'].includes(itemData.unit)) {
				serviceUnit = itemData.unit as 'hour' | 'day' | 'month' | 'project';
			}

			// Create service via React Query mutation
			const newServiceData = {
				name: itemData.name,
				description: itemData.description,
				pricing: {
					rate: parseFloat(itemData.price),
					unit: serviceUnit,
					currency: 'USD',
				},
				organizationId: activeOrganizationId!,
				isActive: true,
				taxable: true,
				tags: [],
			};

			if (activeOrganizationId) {
				try {
					await createService(newServiceData);
					console.log('Service created successfully');
				} catch (error) {
					console.error('Failed to create service:', error);
				}
			}
		}

		// Close the modal
		closeItemSheet();
	};

	const handleItemCreationCancel = () => {
		closeItemSheet();
	};

	const handleItemDelete = () => {
		if (editingItem) {
			// Use startTransition for store update to prevent animation conflicts
			startTransition(() => {
				invoiceStore.deleteLineItem(editingItem);
			});
			closeItemSheet();
		}
	};

	// Handle line item updates
	const handleEditLineItem = (id: string) => {
		setEditingItem(id);
		setShowNewItemForm(true);
		openItemSheet();
	};

	// Add new line item via sheet
	const handleAddLineItem = () => {
		setEditingItem(null);
		setShowNewItemForm(true);
		openItemSheet();
	};

	// Invoice details sheet functions
	const openInvoiceDetailsSheet = () => {
		setShowInvoiceDetailsSheet(true);
		Animated.timing(invoiceDetailsAnimation, {
			toValue: 1,
			duration: 300,
			useNativeDriver: true,
		}).start();
	};

	const closeInvoiceDetailsSheet = () => {
		Animated.timing(invoiceDetailsAnimation, {
			toValue: 0,
			duration: 200,
			useNativeDriver: true,
		}).start(() => {
			setShowInvoiceDetailsSheet(false);
		});
	};

	// Tax helper functions using React Query hook instead of taxStore
	const getTaxDataByRate = (taxRate: string | number) => {
		const rate = parseFloat(taxRate.toString());
		return taxOptions.find(option => option.rate === rate);
	};

	// Tax configuration display helper using React Query hook instead of taxStore
	const getTaxDataById = (taxId: string) => {
		return taxOptions.find(option => option.id === taxId);
	};

	// Payment methods sheet functions
	const openPaymentMethodsSheet = () => {
		setShowPaymentMethodsSheet(true);
		Animated.timing(paymentMethodsAnimation, {
			toValue: 1,
			duration: 300,
			useNativeDriver: true,
		}).start();
	};

	const closePaymentMethodsSheet = () => {
		Animated.timing(paymentMethodsAnimation, {
			toValue: 0,
			duration: 200,
			useNativeDriver: true,
		}).start(() => {
			setShowPaymentMethodsSheet(false);
		});
	};

	return (
		<View style={styles.container}>
			<InvoiceHeader 
				isEditMode={isEditMode}
				onBack={() => router.back()}
				insets={insets}
				isLoading={false}
			/>

			{/* Main Content Area */}
			<KeyboardAwareScrollView
				style={styles.container}
				contentContainerStyle={[
					styles.scrollContent,
					{ paddingBottom: isFooterPinned ? 250 : 24 }
				]}
				showsVerticalScrollIndicator={false}
				keyboardShouldPersistTaps="handled"
				bottomOffset={20}
				extraKeyboardSpace={0}
			>
				{/* Step 1: Invoice Details */}
				<View style={styles.sectionContainer}>
					<View style={styles.sectionHeader}>
						<View style={styles.stepIndicator}>
							<View style={[styles.stepCircle, styles.stepActiveCircle]}>
								<Ionicons name="document-text-outline" size={16} color={colors.background} />
							</View>
							<View style={styles.stepInfo}>
								<Typography variant="h4" style={styles.sectionTitle}>Invoice Details</Typography>
								<Typography variant="bodySmall" color="secondary">Basic invoice information</Typography>
							</View>
						</View>
					</View>
					<View style={styles.sectionContent}>
						<InvoiceBasicFields 
							selectedClientObj={selectedClientObj}
							onOpenClientSheet={openClientSheet}
						/>
					</View>
				</View>

				{/* Step 2: Items & Services */}
				<View style={styles.sectionContainer}>
					<View style={styles.sectionHeader}>
						<View style={styles.stepIndicator}>
							<View style={[styles.stepCircle, invoiceStore.lineItems.some(item => item.description.trim() !== '') ? styles.stepActiveCircle : styles.stepInactiveCircle]}>
								<Ionicons name="list-outline" size={16} color={invoiceStore.lineItems.some(item => item.description.trim() !== '') ? colors.background : colors.text.secondary} />
							</View>
							<View style={styles.stepInfo}>
								<Typography variant="h4" style={styles.sectionTitle}>Items & Services</Typography>
								<Typography variant="bodySmall" color="secondary">Add products or services to invoice</Typography>
							</View>
						</View>
					</View>
					<View style={styles.sectionContent}>
						<InvoiceItemsAndServices
							isItemsCollapsed={isItemsCollapsed}
							onToggleItemsSection={toggleItemsSection}
							onAddLineItem={handleAddLineItem}
							onOpenServicesSheet={openServicesSheet}
							onEditLineItem={handleEditLineItem}
						/>
					</View>
				</View>

				{/* Step 3: Tax & Payment */}
				<View style={styles.sectionContainer}>
					<View style={styles.sectionHeader}>
						<View style={styles.stepIndicator}>
							<View style={[styles.stepCircle, styles.stepInactiveCircle]}>
								<Ionicons name="calculator-outline" size={16} color={colors.text.secondary} />
							</View>
							<View style={styles.stepInfo}>
								<Typography variant="h4" style={styles.sectionTitle}>Tax & Payment</Typography>
								<Typography variant="bodySmall" color="secondary">Configure tax settings and payment methods</Typography>
							</View>
						</View>
					</View>
					<View style={styles.sectionContent}>
						<InvoiceTaxAndPayment
							isTaxCollapsed={isTaxCollapsed}
							onToggleTaxSection={toggleTaxSection}
							onOpenPaymentMethodsSheet={openPaymentMethodsSheet}
						/>
					</View>
				</View>

				{/* Step 4: Additional Details */}
				<View style={styles.sectionContainer}>
					<View style={styles.sectionHeader}>
						<View style={styles.stepIndicator}>
							<View style={[styles.stepCircle, styles.stepInactiveCircle]}>
								<Ionicons name="document-attach-outline" size={16} color={colors.text.secondary} />
							</View>
							<View style={styles.stepInfo}>
								<Typography variant="h4" style={styles.sectionTitle}>Additional Details</Typography>
								<Typography variant="bodySmall" color="secondary">Notes, terms, attachments, and signature</Typography>
							</View>
						</View>
					</View>
					<View style={styles.sectionContent}>
						<InvoiceAdditionalDetails
							onOpenSignatureModal={openSignatureModal}
							isPickingFile={isPickingFile}
							setIsPickingFile={setIsPickingFile}
						/>
					</View>
				</View>

				{/* Step 5: Review & Submit - Unpinned Footer */}
				{!isFooterPinned && (
					<View style={styles.sectionContainer}>
						<View style={styles.sectionHeader}>
							<View style={styles.stepIndicator}>
								<View style={[styles.stepCircle, isFormValid ? styles.stepActiveCircle : styles.stepInactiveCircle]}>
									<Ionicons name="checkmark-circle-outline" size={16} color={isFormValid ? colors.background : colors.text.secondary} />
								</View>
								<View style={styles.stepInfo}>
									<Typography variant="h4" style={styles.sectionTitle}>Review & Submit</Typography>
									<Typography variant="bodySmall" color="secondary">Review invoice details and submit</Typography>
								</View>
							</View>
						</View>
						<View style={styles.sectionContent}>
							<InvoiceFooter
								isFormValid={isFormValid}
								isFixed={false}
								isPinned={isFooterPinned}
								onTogglePin={toggleFooterPinned}
								onShowDetails={openInvoiceDetailsSheet}
								paddingBottom={insets.bottom + 8}
								editInvoiceId={editInvoiceId}
								onUpdate={isEditMode ? updateInvoice : undefined}
							/>
						</View>
					</View>
				)}

			</KeyboardAwareScrollView>

			{/* Fixed Footer - Outside KeyboardAwareScrollView to prevent keyboard interference */}
			{isFooterPinned && (
				<InvoiceFooter
					isFormValid={isFormValid}
					isFixed={true}
					isPinned={isFooterPinned}
					onTogglePin={toggleFooterPinned}
					onShowDetails={openInvoiceDetailsSheet}
					paddingBottom={insets.bottom + 8}
					editInvoiceId={editInvoiceId}
					onUpdate={isEditMode ? updateInvoice : undefined}
				/>
			)}

			{/* Client Selector Modal */}
			<ClientSelectorModal
				visible={showClientSelector}
				onClose={closeClientSheet}
				animation={clientSheetAnimation}
				availableClients={availableClients}
				selectedClientId={invoiceStore.selectedClientId}
				onSelectClient={handleSelectClient}
				onCreateClient={handleClientCreationSubmit}
			/>

			{/* Services Selector Modal */}
			<ServicesSelectorModal
				visible={showServicesSelector}
				onClose={closeServicesSheet}
				animation={servicesSheetAnimation}
				availableServices={availableServices}
				activeOrganizationId={activeOrganizationId || null}
				onSelectService={handleSelectService}
				onCreateService={handleServiceCreationSubmit}
			/>

			{/* Item Creation/Edit Modal */}
			<ItemCreationModal
				visible={showItemSheet}
				onClose={closeItemSheet}
				animation={itemSheetAnimation}
				showNewItemForm={showNewItemForm}
				editingItem={editingItem}
				lineItems={invoiceStore.lineItems}
				onSubmit={handleItemCreationSubmit}
				onCancel={handleItemCreationCancel}
				onDelete={editingItem ? handleItemDelete : undefined}
				selectedService={selectedServiceForEdit}
			/>

			{/* Signature Modal */}
			<SignatureModal
				visible={showSignatureModal}
				onClose={closeSignatureModal}
				animation={signatureAnimation}
			/>

			{/* Invoice Details Sheet */}
			<InvoiceDetailsModal
				visible={showInvoiceDetailsSheet}
				onClose={closeInvoiceDetailsSheet}
				animation={invoiceDetailsAnimation}
				lineItems={invoiceStore.lineItems}
				taxConfig={invoiceStore.taxConfig}
				getItemTaxAmount={invoiceStore.getItemTaxAmount}
				getSubtotal={invoiceStore.getSubtotal}
				getTaxAmount={invoiceStore.getTaxAmount}
				getPreTaxAmount={invoiceStore.getPreTaxAmount}
				getTotal={invoiceStore.getTotal}
				getTaxDataByRate={getTaxDataByRate}
				getTaxDataById={getTaxDataById}
			/>

			{/* Payment Methods Sheet */}
			<PaymentMethodsSheet
				visible={showPaymentMethodsSheet}
				onClose={closePaymentMethodsSheet}
				animation={paymentMethodsAnimation}
			/>
		</View>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: colors.background,
	},
	centerContainer: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
	},
	scrollContent: {
		padding: 14,
	},
	sectionContainer: {
		marginBottom: 14,
		backgroundColor: colors.cardBackground,
		borderRadius: 5,
		borderWidth: StyleSheet.hairlineWidth,
		borderColor: colors.divider,
		overflow: 'hidden',
	},
	sectionHeader: {
		paddingHorizontal: 16,
		paddingVertical: 12,
		backgroundColor: colors.background,
		borderBottomWidth: StyleSheet.hairlineWidth,
		borderBottomColor: colors.divider,
	},
	stepIndicator: {
		flexDirection: 'row',
		alignItems: 'center',
	},
	stepCircle: {
		width: 32,
		height: 32,
		borderRadius: 16,
		justifyContent: 'center',
		alignItems: 'center',
		marginRight: 12,
	},
	stepActiveCircle: {
		backgroundColor: colors.primary,
	},
	stepInactiveCircle: {
		backgroundColor: colors.background,
		borderWidth: 2,
		borderColor: colors.divider,
	},
	stepInfo: {
		flex: 1,
	},
	sectionTitle: {
		marginBottom: 2,
		fontWeight: '600',
	},
	sectionContent: {
		// Remove background and border styling since RowItems already handle this
	},
});