import { Avatar, ConfirmationDialog, Toast, Typography } from '@/components/ui';
import { ActionMenu, ActionMenuItem } from '@/components/ui/ActionMenu';
import { colors } from '@/constants/Colors';
import { useClient, useClientActivities } from '@/services/client/clients';
import { useDeleteClient } from '@/services/client/delete';
import { useHasActiveOrganization } from '@/stores';
import { formatCurrency } from '@/stores/settingsStore';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import {
    Linking,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

type TabType = 'details' | 'activity';

export default function ClientDetailScreen() {
  const insets = useSafeAreaInsets();
  const { clientId } = useLocalSearchParams<{ clientId: string }>();
  const [activeTab, setActiveTab] = useState<TabType>('details');
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error' | 'info'>('success');

  // Clean organization guards
  const hasActiveOrganization = useHasActiveOrganization();

  // Use clean service hooks - no parameters, computed selectors
  const { client, loading, error } = useClient(clientId || '');
  const { data: activities = [] } = useClientActivities(clientId || '');
  const { deleteClient } = useDeleteClient();

  // Organization selection guard
  if (!hasActiveOrganization) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        <View style={styles.centerContainer}>
          <Typography variant="h3" color="secondary" style={{ marginBottom: 8 }}>
            Select an Organization
          </Typography>
          <Typography variant="body" color="secondary" center>
            Please select an organization to view client details
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        <View style={styles.centerContainer}>
          <Typography variant="body" color="secondary">Loading client...</Typography>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !client) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        <View style={styles.centerContainer}>
          <Typography variant="body" color="secondary">Client not found</Typography>
        </View>
      </SafeAreaView>
    );
  }

  // Format join date
  const formatJoinDate = () => {
    if (!client.createdAt) return 'Recently';
    try {
      const date = typeof client.createdAt === 'string' ? new Date(client.createdAt) : client.createdAt;
      return date.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      });
    } catch {
      return 'Recently';
    }
  };

  // Count invoices for this client
  const getInvoiceCount = () => {
    return activities.filter(activity => activity.type === 'used_in_invoice').length;
  };

  // Get last invoice date
  const getLastInvoiceDate = () => {
    const invoiceActivities = activities
      .filter(activity => activity.type === 'used_in_invoice')
      .sort((a, b) => {
        const aDate = a.createdAt || new Date(0);
        const bDate = b.createdAt || new Date(0);
        return bDate.getTime() - aDate.getTime();
      });
    
    if (invoiceActivities.length === 0) return 'Never';
    
    const lastActivity = invoiceActivities[0];
    if (!lastActivity.createdAt) return 'Recently';
    
    try {
      const date = typeof lastActivity.createdAt === 'string' ? new Date(lastActivity.createdAt) : lastActivity.createdAt;
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    } catch {
      return 'Recently';
    }
  };

  // Actions
  const handleBack = () => {
    router.back();
  };

  const handleEdit = () => {
    router.push(`/edit-client?clientId=${client.id}`);
  };

  const handleDelete = () => {
    setDeleteDialogVisible(true);
  };

  const handleCallClient = async () => {
    if (!client.contact?.phone) return;
    
    try {
      const phoneUrl = `tel:${client.contact.phone}`;
      const supported = await Linking.canOpenURL(phoneUrl);
      
      if (supported) {
        await Linking.openURL(phoneUrl);
      } else {
        setToastMessage('Phone calling is not supported on this device');
        setToastType('error');
        setToastVisible(true);
      }
    } catch (error) {
      setToastMessage('Failed to initiate phone call');
      setToastType('error');
      setToastVisible(true);
      console.error('Phone call error:', error);
    }
  };

  const handleEmailClient = async () => {
    if (!client.contact?.email) return;
    
    try {
      const emailUrl = `mailto:${client.contact.email}`;
      const supported = await Linking.canOpenURL(emailUrl);
      
      if (supported) {
        await Linking.openURL(emailUrl);
      } else {
        setToastMessage('Email is not supported on this device');
        setToastType('error');
        setToastVisible(true);
      }
    } catch (error) {
      setToastMessage('Failed to open email client');
      setToastType('error');
      setToastVisible(true);
      console.error('Email error:', error);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'details':
        return (
          <View style={styles.tabContent}>
            {/* Client Information */}
            <View style={styles.section}>
              <Typography variant="h4" bold style={styles.sectionTitle}>
                Client Information
              </Typography>
              <View style={styles.clientInfoCard}>
                <View style={styles.clientHeader}>
                  <Avatar
                    name={client.name}
                    photo={client.photo}
                    size={64}
                  />
                  <View style={styles.clientNameSection}>
                    <Typography variant="h3" bold style={styles.clientName}>
                      {client.name}
                    </Typography>
                    {client.company && (
                      <Typography variant="body" color="secondary" style={styles.clientCompany}>
                        {client.company}
                      </Typography>
                    )}
                    <Typography variant="bodySmall" color="secondary" style={styles.clientSince}>
                      Client since {formatJoinDate()}
                    </Typography>
                  </View>
                </View>
              </View>
            </View>

            {/* Contact Information */}
            <View style={styles.section}>
              <View style={styles.contactCard}>
                <View style={styles.contactHeader}>
                  <Typography variant="h4" bold style={styles.sectionTitle}>
                    Contact Information
                  </Typography>
                </View>

                {/* Contact Details */}
                <View style={styles.contactDetails}>
                  {client.contact?.email && (
                    <TouchableOpacity 
                      style={styles.contactItem}
                      onPress={handleEmailClient}
                      activeOpacity={0.7}
                    >
                      <View style={styles.contactIcon}>
                        <Ionicons name="mail" size={18} color={colors.primary} />
                      </View>
                      <View style={styles.contactInfo}>
                        <Typography variant="bodySmall" color="secondary" style={styles.contactLabel}>
                          Email Address
                        </Typography>
                        <Typography variant="body" color="primary" style={styles.contactValue}>
                          {client.contact.email}
                        </Typography>
                      </View>
                      <Ionicons name="open-outline" size={16} color={colors.text.secondary} />
                    </TouchableOpacity>
                  )}

                  {client.contact?.phone && (
                    <TouchableOpacity 
                      style={styles.contactItem}
                      onPress={handleCallClient}
                      activeOpacity={0.7}
                    >
                      <View style={styles.contactIcon}>
                        <Ionicons name="call" size={18} color={colors.primary} />
                      </View>
                      <View style={styles.contactInfo}>
                        <Typography variant="bodySmall" color="secondary" style={styles.contactLabel}>
                          Phone Number
                        </Typography>
                        <Typography variant="body" color="primary" style={styles.contactValue}>
                          {client.contact.phone}
                        </Typography>
                      </View>
                      <Ionicons name="open-outline" size={16} color={colors.text.secondary} />
                    </TouchableOpacity>
                  )}

                  {client.address?.fullAddress && (
                    <View style={styles.contactItem}>
                      <View style={styles.contactIcon}>
                        <Ionicons name="location" size={18} color={colors.primary} />
                      </View>
                      <View style={styles.contactInfo}>
                        <Typography variant="bodySmall" color="secondary" style={styles.contactLabel}>
                          Business Address
                        </Typography>
                        <Typography variant="body" color="primary" style={styles.contactValue}>
                          {client.address.fullAddress}
                        </Typography>
                      </View>
                    </View>
                  )}
                </View>

                {/* Business Information */}
                <View style={styles.sectionDivider} />
                <View style={styles.businessInfo}>
                  <View style={styles.infoRow}>
                    <View style={styles.infoItem}>
                      <Typography variant="bodySmall" color="secondary" style={styles.infoLabel}>
                        Total Invoices
                      </Typography>
                      <Typography variant="body" color="primary" bold>
                        {getInvoiceCount()}
                      </Typography>
                    </View>
                    
                    <View style={styles.infoItem}>
                      <Typography variant="bodySmall" color="secondary" style={styles.infoLabel}>
                        Last Invoice
                      </Typography>
                      <Typography variant="body" color="primary" bold>
                        {getLastInvoiceDate()}
                      </Typography>
                    </View>
                  </View>
                </View>
              </View>
            </View>
          </View>
        );

      case 'activity':
        return (
          <View style={styles.tabContent}>
            <View style={styles.section}>
              <Typography variant="h4" bold style={styles.sectionTitle}>
                Recent Activity
              </Typography>
              
              {activities.length === 0 ? (
                <View style={styles.activityCard}>
                  <Typography variant="body" color="secondary" center style={styles.emptyActivity}>
                    No activity recorded yet
                  </Typography>
                  <Typography variant="bodySmall" color="secondary" center style={styles.emptyActivitySubtext}>
                    Invoices and interactions will appear here when available
                  </Typography>
                </View>
              ) : (
                <View style={styles.activityTimeline}>
                  {activities.map((activity, index) => {
                    const getActivityIcon = () => {
                      switch (activity.type) {
                        case 'created':
                          return 'person-add';
                        case 'updated':
                          return 'create';
                        case 'contact_updated':
                          return 'call';
                        case 'address_updated':
                          return 'location';
                        case 'used_in_invoice':
                          return 'document-text';
                        case 'activated':
                          return 'play-circle';
                        case 'deactivated':
                          return 'pause-circle';
                        case 'note_added':
                          return 'chatbox';
                        default:
                          return 'information-circle';
                      }
                    };

                    const getActivityColor = () => {
                      switch (activity.type) {
                        case 'created':
                          return colors.success;
                        case 'updated':
                        case 'contact_updated':
                        case 'address_updated':
                          return colors.primary;
                        case 'used_in_invoice':
                          return colors.primary;
                        case 'activated':
                          return colors.success;
                        case 'deactivated':
                          return colors.error;
                        case 'note_added':
                          return colors.warning;
                        default:
                          return colors.text.secondary;
                      }
                    };

                    const formatDate = (date: Date | string | undefined) => {
                      if (!date) return 'Unknown';
                      try {
                        const dateObj = typeof date === 'string' ? new Date(date) : date;
                        return dateObj.toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric',
                          hour: 'numeric',
                          minute: '2-digit'
                        });
                      } catch {
                        return 'Unknown';
                      }
                    };

                    return (
                      <View key={activity.id} style={styles.activityItem}>
                        <View style={styles.activityIconContainer}>
                          <View style={[styles.activityIcon, { backgroundColor: getActivityColor() }]}>
                            <Ionicons 
                              name={getActivityIcon()} 
                              size={16} 
                              color={colors.background} 
                            />
                          </View>
                          {index < activities.length - 1 && <View style={styles.activityLine} />}
                        </View>
                        
                        {activity.type === 'used_in_invoice' && activity.metadata?.invoiceId ? (
                          <TouchableOpacity 
                            style={styles.activityContent}
                            onPress={() => {
                              // Navigate to invoice detail
                              if (activity.metadata?.invoiceId) {
                                router.push(`/invoice-detail?invoiceId=${activity.metadata.invoiceId}`);
                              }
                            }}
                            activeOpacity={0.7}
                          >
                            <View style={styles.clickableActivityHeader}>
                              <Typography variant="body" bold style={styles.activityMessage}>
                                {activity.description}
                              </Typography>
                              <Ionicons name="chevron-forward" size={16} color={colors.text.secondary} />
                            </View>
                            
                            {activity.metadata?.amount && (
                              <Typography variant="bodySmall" color="primary" style={styles.activityAmount}>
                                {formatCurrency(activity.metadata.amount)}
                              </Typography>
                            )}

                            {activity.metadata?.status && (
                              <Typography variant="bodySmall" color="secondary" style={styles.activityStatus}>
                                Status: {activity.metadata.status}
                              </Typography>
                            )}
                            
                            <Typography variant="caption" color="secondary" style={styles.activityDate}>
                              {formatDate(activity.createdAt)}
                            </Typography>
                          </TouchableOpacity>
                        ) : (
                          <View style={styles.activityContent}>
                            <Typography variant="body" bold style={styles.activityMessage}>
                              {activity.description}
                            </Typography>
                            
                            {activity.metadata?.amount && (
                              <Typography variant="bodySmall" color="primary" style={styles.activityAmount}>
                                {formatCurrency(activity.metadata.amount)}
                              </Typography>
                            )}

                            {activity.metadata?.status && (
                              <Typography variant="bodySmall" color="secondary" style={styles.activityStatus}>
                                Status: {activity.metadata.status}
                              </Typography>
                            )}
                            
                            <Typography variant="caption" color="secondary" style={styles.activityDate}>
                              {formatDate(activity.createdAt)}
                            </Typography>
                          </View>
                        )}
                      </View>
                    );
                  })}
                </View>
              )}
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  // Action menu items
  const actionMenuItems: ActionMenuItem[] = [
    ...(client.contact?.email ? [{
      id: 'email',
      title: 'Send Email',
      icon: 'mail' as any,
      onPress: handleEmailClient,
    }] : []),
    ...(client.contact?.phone ? [{
      id: 'call',
      title: 'Call Client',
      icon: 'call' as any,
      onPress: handleCallClient,
    }] : []),
    {
      id: 'edit',
      title: 'Edit Client',
      icon: 'create',
      onPress: handleEdit,
    },
    {
      id: 'delete',
      title: 'Delete Client',
      icon: 'trash',
      onPress: handleDelete,
      destructive: true,
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar style="dark" />
      
      {/* Header */}
      <View style={[styles.header, { paddingTop: insets.top }]}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Typography variant="body" style={styles.headerTitle}>
            Client Details
          </Typography>
        </View>
        <ActionMenu
          items={actionMenuItems}
          horizontalOffset={18}
          verticalOffset={-8}
        />
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <View style={styles.tabNavigation}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'details' && styles.activeTab]}
            onPress={() => setActiveTab('details')}
          >
            <Ionicons 
              name="person-outline" 
              size={18} 
              color={activeTab === 'details' ? colors.primary : colors.text.secondary}
              style={styles.tabIcon}
            />
            <Typography 
              variant="bodySmall" 
              color={activeTab === 'details' ? 'primary' : 'secondary'}
              style={styles.tabLabel}
            >
              Details
            </Typography>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'activity' && styles.activeTab]}
            onPress={() => setActiveTab('activity')}
          >
            <Ionicons 
              name="time-outline" 
              size={18} 
              color={activeTab === 'activity' ? colors.primary : colors.text.secondary}
              style={styles.tabIcon}
            />
            <Typography 
              variant="bodySmall" 
              color={activeTab === 'activity' ? 'primary' : 'secondary'}
              style={styles.tabLabel}
            >
              Activity
            </Typography>
          </TouchableOpacity>
        </View>
      </View>

      {/* Tab Content */}
      <ScrollView 
        style={styles.contentContainer}
        contentContainerStyle={[
          styles.scrollContent,
          { paddingBottom: insets.bottom + 20 }
        ]}
        showsVerticalScrollIndicator={false}
      >
        {renderTabContent()}
      </ScrollView>

      <ConfirmationDialog
        visible={deleteDialogVisible}
        title="Delete Client"
        message={`Are you sure you want to delete "${client.name}"? This action cannot be undone.`}
        confirmText="Delete"
        destructive={true}
        onConfirm={async () => {
          setDeleteDialogVisible(false);
          try {
            await deleteClient(client.id);
            setToastMessage('Client has been deleted.');
            setToastType('success');
            setToastVisible(true);
            // Wait a moment before navigating back to show the toast
            setTimeout(() => router.back(), 1500);
          } catch (error: any) {
            setToastMessage('Failed to delete client. Please try again.');
            setToastType('error');
            setToastVisible(true);
            console.error('Delete client error:', error);
          }
        }}
        onCancel={() => setDeleteDialogVisible(false)}
      />

      <Toast
        visible={toastVisible}
        message={toastMessage}
        type={toastType}
        onHide={() => setToastVisible(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 12,
    backgroundColor: colors.cardBackground,
  },
  backButton: {
    padding: 4,
    marginRight: 16,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  
  // Tab Navigation
  tabContainer: {
    backgroundColor: colors.cardBackground,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  tabNavigation: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 8,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 10,
    backgroundColor: 'transparent',
  },
  activeTab: {
    backgroundColor: colors.primaryVeryLight,
  },
  tabIcon: {
    marginRight: 6,
  },
  tabLabel: {
    fontSize: 13,
    fontWeight: '500',
  },

  // Content
  contentContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 12,
  },
  tabContent: {
    flex: 1,
  },
  
  // Sections
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 12,
    letterSpacing: 0.3,
  },
  sectionDivider: {
    height: 1,
    backgroundColor: colors.divider,
    marginVertical: 16,
  },
  
  // Client Info Card
  clientInfoCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  clientHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  clientNameSection: {
    flex: 1,
    marginLeft: 16,
  },
  clientName: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 4,
  },
  clientCompany: {
    fontSize: 16,
    color: colors.text.secondary,
  },
  clientSince: {
    fontSize: 13,
    color: colors.text.secondary,
    marginTop: 6,
    fontStyle: 'italic',
  },

  // Contact Card
  contactCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  contactHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  contactDetails: {
    gap: 16,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 4,
    borderRadius: 8,
  },
  contactIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primaryVeryLight,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  contactInfo: {
    flex: 1,
  },
  contactLabel: {
    fontSize: 12,
    marginBottom: 4,
    textTransform: 'uppercase',
    fontWeight: '500',
    letterSpacing: 0.5,
  },
  contactValue: {
    fontSize: 15,
    fontWeight: '500',
    lineHeight: 20,
  },
  businessInfo: {
    gap: 16,
  },
  infoRow: {
    flexDirection: 'row',
    gap: 24,
    marginBottom: 12,
  },
  infoItem: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    marginBottom: 4,
    textTransform: 'uppercase',
    fontWeight: '500',
    letterSpacing: 0.5,
  },

  // Activity
  activityCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 24,
    borderWidth: 1,
    borderColor: colors.divider,
    alignItems: 'center',
  },
  emptyActivity: {
    fontSize: 16,
    marginBottom: 8,
  },
  emptyActivitySubtext: {
    fontSize: 14,
  },
  
  // Activity Timeline
  activityTimeline: {
    flex: 1,
  },
  activityItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  activityIconContainer: {
    alignItems: 'center',
    marginRight: 12,
    width: 32,
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  activityLine: {
    width: 2,
    flex: 1,
    backgroundColor: colors.divider,
    position: 'absolute',
    top: 40,
    left: 15,
    bottom: -16,
  },
  activityContent: {
    flex: 1,
    paddingTop: 4,
  },
  activityMessage: {
    fontSize: 15,
    fontWeight: '500',
    color: colors.text.primary,
    marginBottom: 4,
  },
  activityAmount: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary,
    marginBottom: 4,
  },
  activityStatus: {
    fontSize: 13,
    color: colors.text.secondary,
    marginBottom: 4,
  },
  activityDate: {
    fontSize: 13,
    color: colors.text.secondary,
  },
  clickableActivityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
}); 