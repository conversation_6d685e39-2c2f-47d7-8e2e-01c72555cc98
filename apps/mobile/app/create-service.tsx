import { KeyboardAwareView, ServiceCreationForm, Typography } from '@/components/ui';
import { colors } from '@/constants/Colors';
import { useCreateService } from '@/services/service/create';
import { useActiveOrganizationId, useHasActiveOrganization } from '@/stores/organization-selectors';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

export default function CreateServiceScreen() {
  const insets = useSafeAreaInsets();
  
  // Clean organization guards
  const hasActiveOrganization = useHasActiveOrganization();
  const organizationId = useActiveOrganizationId();
  
  // Get create service hook
  const { createService } = useCreateService();

  // Organization selection guard
  if (!hasActiveOrganization) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        <View style={styles.centerContainer}>
          <Typography variant="h3" color="secondary" style={{ marginBottom: 8 }}>
            Select an Organization
          </Typography>
          <Typography variant="body" color="secondary" center>
            Please select an organization to create service
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  const handleBack = () => {
    router.back();
  };

  const handleCreateService = async (serviceData: {
    name: string;
    description: string;
    pricing: {
      rate: number;
      unit: string;
      currency: string;
    };
    isActive: boolean;
    taxable: boolean;
    tags: string[];
  }) => {
    try {
      await createService({
        ...serviceData,
        organizationId: organizationId!,
      });
      router.back();
    } catch (error: any) {
      console.error('Create service error:', error);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar style="dark" />
      
      {/* Header */}
      <View style={[styles.header, { paddingTop: insets.top }]}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Typography variant="body" style={styles.headerTitle}>
            Create Service
          </Typography>
        </View>
        <View style={styles.headerAction} />
      </View>

      {/* Content with Keyboard Avoidance */}
      <KeyboardAwareView 
        style={styles.contentContainer}
        contentContainerStyle={styles.scrollContent}
      >
        <ServiceCreationForm
          onSubmit={handleCreateService}
          onCancel={handleCancel}
          submitButtonText="Create Service"
          cancelButtonText="Cancel"
        />
      </KeyboardAwareView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.cardBackground,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 12,
    backgroundColor: colors.cardBackground,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  backButton: {
    padding: 4,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: '600',
  },
  headerAction: {
    width: 32,
  },
  contentContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
}); 