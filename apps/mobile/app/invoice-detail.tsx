import { FloatingActionButton } from '@/components/FloatingActionButton';
import { Avatar, InvoiceLineItem, Typography } from '@/components/ui';
import { ActionMenu } from '@/components/ui/ActionMenu';
import { ConfirmationDialog } from '@/components/ui/ConfirmationDialog';
import { ErrorDialog } from '@/components/ui/ErrorDialog';
import { InfoDialog } from '@/components/ui/InfoDialog';
import { PaymentDetailSheet } from '@/components/ui/PaymentDetailSheet';
import { PaymentRecordData } from '@/components/ui/PaymentRecordForm';
import { PaymentRecordSheet } from '@/components/ui/PaymentRecordSheet';
import { StatusBadge } from '@/components/ui/StatusIndicator';
import { SuccessDialog } from '@/components/ui/SuccessDialog';
import { Toast } from '@/components/ui/Toast';
import { colors } from '@/constants/Colors';
import { queryClient } from '@/core/query-client';
import { useClients } from '@/services/client/clients';
import { updateInvoice, useInvoice, useInvoiceActivities } from '@/services/invoice/invoices';
import { useCreatePayment, useInvoicePayments, usePaymentStatus } from '@/services/payment/payments';
import { useServices } from '@/services/service/services';
import { useActiveOrganizationId, useHasActiveOrganization } from '@/stores';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useMemo, useState } from 'react';
import {
  Animated,
  Pressable,
  ScrollView,
  Share,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { formatCurrency } from '../stores/settingsStore';

// Helper functions for activity rendering
const getActivityIcon = (type: string): React.ComponentProps<typeof Ionicons>['name'] => {
  switch (type) {
    case 'created': return 'document-text-outline';
    case 'sent': return 'send-outline';
    case 'viewed': return 'eye-outline';
    case 'paid': return 'checkmark-circle-outline';
    case 'partial_payment': return 'card-outline';
    case 'overdue': return 'warning-outline';
    case 'reminder_sent': return 'mail-outline';
    case 'status_changed': return 'swap-horizontal-outline';
    case 'edited': return 'create-outline';
    case 'cancelled': return 'close-circle-outline';
    case 'refunded': return 'return-down-back-outline';
    case 'noted': return 'chatbubble-outline';
    case 'downloaded': return 'download-outline';
    case 'archived': return 'archive-outline';
    case 'unarchived': return 'folder-open-outline';
    default: return 'information-circle-outline';
  }
};

const getActivityColor = (type: string): string => {
  switch (type) {
    case 'created': return colors.primary;
    case 'sent': return colors.success;
    case 'viewed': return colors.primary;
    case 'paid': return colors.success;
    case 'partial_payment': return colors.warning;
    case 'overdue': return colors.error;
    case 'reminder_sent': return colors.warning;
    case 'status_changed': return colors.primary;
    case 'edited': return colors.primary;
    case 'cancelled': return colors.error;
    case 'refunded': return colors.warning;
    case 'noted': return colors.text.secondary;
    case 'downloaded': return colors.primary;
    case 'archived': return colors.text.secondary;
    case 'unarchived': return colors.text.secondary;
    default: return colors.text.secondary;
  }
};

const formatActivityTime = (date: Date): string => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor(diff / (1000 * 60));

  if (days > 7) {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  } else if (days > 0) {
    return `${days} day${days === 1 ? '' : 's'} ago`;
  } else if (hours > 0) {
    return `${hours} hour${hours === 1 ? '' : 's'} ago`;
  } else if (minutes > 0) {
    return `${minutes} minute${minutes === 1 ? '' : 's'} ago`;
  } else {
    return 'Just now';
  }
};

const renderActivityMetadata = (metadata: any) => {
  if (!metadata || typeof metadata !== 'object') return null;

  return (
    <View style={styles.activityMetadata}>
      {metadata.method && (
        <Typography variant="caption" color="secondary" style={styles.metadataText}>
          Method: {metadata.method}
        </Typography>
      )}
      {metadata.amount && (
        <Typography variant="caption" color="secondary" style={styles.metadataText}>
          Amount: {formatCurrency(metadata.amount)}
        </Typography>
      )}
      {metadata.paymentMethod && (
        <Typography variant="caption" color="secondary" style={styles.metadataText}>
          Payment: {metadata.paymentMethod}
        </Typography>
      )}
      {metadata.transactionId && (
        <Typography variant="caption" color="secondary" style={styles.metadataText}>
          Transaction: {metadata.transactionId}
        </Typography>
      )}
      {metadata.oldStatus && metadata.newStatus && (
        <Typography variant="caption" color="secondary" style={styles.metadataText}>
          {metadata.oldStatus} → {metadata.newStatus}
        </Typography>
      )}
      {metadata.reminderType && (
        <Typography variant="caption" color="secondary" style={styles.metadataText}>
          Type: {metadata.reminderType}
        </Typography>
      )}
      {metadata.daysOverdue && (
        <Typography variant="caption" color="secondary" style={styles.metadataText}>
          Days overdue: {metadata.daysOverdue}
        </Typography>
      )}
    </View>
  );
};

// Helper functions for attachments
const getAttachmentIcon = (mimeType: string): React.ComponentProps<typeof Ionicons>['name'] => {
  if (mimeType.startsWith('image/')) return 'image-outline';
  if (mimeType.includes('pdf')) return 'document-text-outline';
  if (mimeType.includes('word') || mimeType.includes('document')) return 'document-outline';
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'grid-outline';
  if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'easel-outline';
  if (mimeType.includes('zip') || mimeType.includes('rar')) return 'archive-outline';
  return 'document-outline';
};

const formatAttachmentSize = (bytes: number): string => {
  if (bytes < 1024) return bytes + ' B';
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
  return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
};

const getAttachmentExtension = (filename: string): string => {
  return filename.split('.').pop()?.toUpperCase() || 'FILE';
};

// Helper functions for payments
const getPaymentMethodIcon = (method: string): React.ComponentProps<typeof Ionicons>['name'] => {
  const iconMap: Record<string, React.ComponentProps<typeof Ionicons>['name']> = {
    'bank_transfer': 'business-outline',
    'credit_card': 'card-outline',
    'debit_card': 'card-outline', 
    'cash': 'cash-outline',
    'check': 'receipt-outline',
    'paypal': 'logo-paypal',
    'stripe': 'card-outline',
    'wire_transfer': 'swap-horizontal-outline',
    'online_banking': 'globe-outline',
    'mobile_payment': 'phone-portrait-outline',
    'cryptocurrency': 'logo-bitcoin',
    'other': 'ellipsis-horizontal-outline',
  };
  
  return iconMap[method] || 'card-outline';
};

const formatPaymentMethod = (method: string): string => {
  const methodMap: Record<string, string> = {
    'bank_transfer': 'Bank Transfer',
    'credit_card': 'Credit Card', 
    'debit_card': 'Debit Card',
    'cash': 'Cash',
    'check': 'Check',
    'paypal': 'PayPal',
    'stripe': 'Stripe',
    'wire_transfer': 'Wire Transfer',
    'online_banking': 'Online Banking',
    'mobile_payment': 'Mobile Payment',
    'cryptocurrency': 'Cryptocurrency',
    'other': 'Other',
  };
  
  return methodMap[method] || method;
};

type TabType = 'details' | 'payment' | 'activity';

export default function InvoiceDetailScreen() {
  const insets = useSafeAreaInsets();
  const { invoiceId } = useLocalSearchParams<{ invoiceId: string }>();
  const [activeTab, setActiveTab] = useState<TabType>('details');

  // Comments state
  const [showAddComment, setShowAddComment] = useState(false);
  const [newComment, setNewComment] = useState('');

  // Payment state
  const [showPaymentSheet, setShowPaymentSheet] = useState(false);
  const paymentSheetAnimation = useState(new Animated.Value(0))[0];
  const [showPaymentDetailSheet, setShowPaymentDetailSheet] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<any>(null);
  const paymentDetailAnimation = useState(new Animated.Value(0))[0];

  // Dialog states
  const [showInfoDialog, setShowInfoDialog] = useState(false);
  const [infoDialogConfig, setInfoDialogConfig] = useState({ title: '', message: '' });
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [errorDialogConfig, setErrorDialogConfig] = useState({ title: '', message: '' });
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [successDialogConfig, setSuccessDialogConfig] = useState({ title: '', message: '' });
  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false);
  const [confirmationDialogConfig, setConfirmationDialogConfig] = useState({ 
    title: '', 
    message: '', 
    onConfirm: () => {},
    destructive: false
  });

  // Toast states
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error' | 'info'>('info');

  // Helper functions for dialogs
  const showInfo = (title: string, message: string) => {
    setInfoDialogConfig({ title, message });
    setShowInfoDialog(true);
  };

  const showError = (title: string, message: string) => {
    setErrorDialogConfig({ title, message });
    setShowErrorDialog(true);
  };

  const showSuccess = (title: string, message: string) => {
    setSuccessDialogConfig({ title, message });
    setShowSuccessDialog(true);
  };

  const showConfirmation = (title: string, message: string, onConfirm: () => void, destructive = false) => {
    setConfirmationDialogConfig({ title, message, onConfirm, destructive });
    setShowConfirmationDialog(true);
  };

  // Comment helpers
  const formatCommentTime = (date: Date): string => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    
    if (hours < 1) {
      const minutes = Math.floor(diff / (1000 * 60));
      return minutes < 1 ? 'Just now' : `${minutes}m ago`;
    } else if (hours < 24) {
      return `${hours}h ago`;
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit'
      });
    }
  };

  // Clean organization guards
  const hasActiveOrganization = useHasActiveOrganization();
  const organizationId = useActiveOrganizationId();

  // Use clean service hooks - no parameters, computed selectors
  const { invoice, loading, error } = useInvoice(invoiceId || '');
  const { clients = [] } = useClients();
  const { activities = [] } = useInvoiceActivities(invoiceId || '');
  const { services = [] } = useServices();

  // Payment data
  const { data: payments = [], isPending: paymentsLoading } = useInvoicePayments(invoiceId || '');
  
  // Use the same total calculation logic as display for consistency
  const invoiceTotalForPaymentStatus = invoice?.totals?.total || parseFloat(invoice?.amount?.replace(/[^0-9.-]/g, '') || '0') || 0;
  const paymentStatus = usePaymentStatus(payments, invoiceTotalForPaymentStatus);
  const createPaymentMutation = useCreatePayment();

  // Get client details - moved before early returns
  const client = useMemo(() => {
    if (!invoice) return null;
    return clients.find(client => client.id === invoice.clientId && client.organizationId === invoice.organizationId);
  }, [clients, invoice]);

  // Handle line item navigation to service detail
  const handleLineItemPress = (item: any) => {
    // Try to find a matching service by name
    const matchingService = services.find(service => 
      service.name.toLowerCase() === item.description.toLowerCase()
    );
    
    if (matchingService) {
      router.push(`/service-detail?serviceId=${matchingService.id}`);
    } else {
      // Show info that this item doesn't have a corresponding service
      showInfo('Item Info', `"${item.description}" is a custom line item and doesn't have a corresponding service.`);
    }
  };

  // Organization selection guard
  if (!hasActiveOrganization) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        <View style={styles.centerContainer}>
          <Typography variant="h3" color="secondary" style={{ marginBottom: 8 }}>
            Select an Organization
          </Typography>
          <Typography variant="body" color="secondary" center>
            Please select an organization to view invoice details
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        <View style={styles.centerContainer}>
          <Typography variant="body" color="secondary">Loading invoice...</Typography>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !invoice) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        <View style={styles.centerContainer}>
          <Typography variant="body" color="secondary">Invoice not found</Typography>
        </View>
      </SafeAreaView>
    );
  }

  // Use actual invoice data only
  const items = invoice.lineItems || [];
  const itemsSubtotal = items.reduce((sum: number, item: any) => sum + (item.total || 0), 0);
  
  // Calculate tax based on actual invoice data only
  let taxAmount = 0;
  let taxConfig = null;
  
  if (invoice.taxInfo) {
    // Use the actual tax configuration from the invoice
    taxConfig = invoice.taxInfo;
    // Calculate tax amount from the totals
    taxAmount = invoice.totals?.taxTotal || 0;
  }

  const total = invoice.totals?.total || parseFloat(invoice.amount?.replace(/[^0-9.-]/g, '') || '0') || 0;

  // Format dates
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Get client display name
  const getClientDisplayName = () => {
    if (!client) return 'Unknown Client';
    return client.displayName || client.name || 'Unknown Client';
  };

  // Actions
  const handleBack = () => {
    router.back();
  };

  const handlePreview = () => {
    // TODO: Implement preview functionality
    showInfo('Preview Invoice', 'Invoice preview functionality will be implemented soon.');
  };

  const handleEdit = () => {
    // Navigate to invoice editing screen
    if (invoice?.id) {
      router.push(`/create-invoice-collapsible?editInvoiceId=${invoice.id}`);
    }
  };

  const handleMarkAsPaid = async () => {
    if (!invoice) return;
    
    showConfirmation(
      'Mark as Paid',
      'Are you sure you want to mark this invoice as paid?',
      async () => {
        try {
          // Update invoice status using clean service function
          await updateInvoice({
            organizationId: invoice.organizationId,
            id: invoice.id,
            status: 'paid'
          });
          showSuccess('Success', 'Invoice has been marked as paid.');
          // Invalidate invoice cache to refresh the status
          queryClient.invalidateQueries({ 
            queryKey: ['invoice', organizationId, invoice.id] 
          });
        } catch (error: any) {
          showError('Error', 'Failed to update invoice status. Please try again.');
          console.error('Update invoice error:', error);
        }
      }
    );
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `Invoice ${invoice.id} for ${formatCurrency(total)}\n\nView details: [Invoice Link]`,
        title: `Invoice ${invoice.id}`,
      });
    } catch (error) {
      console.error('Error sharing invoice:', error);
    }
  };

  const handleDuplicate = () => {
    // TODO: Implement duplicate functionality
    showInfo('Duplicate Invoice', 'Invoice duplication functionality will be implemented soon.');
  };

  const handleDelete = () => {
    showConfirmation(
      'Delete Invoice',
      `Are you sure you want to delete invoice ${invoice.invoiceNumber}? This action cannot be undone.`,
      () => {
        // TODO: Implement delete functionality
        console.log('Delete invoice:', invoice.id);
        showInfo('Delete', 'Invoice deletion functionality will be implemented soon.');
      },
      true // destructive
    );
  };

  // Payment handlers
  const handlePaymentRecord = async (paymentData: PaymentRecordData) => {
    // If this is a draft invoice, validate it's complete before allowing payment
    if (invoice.status === 'draft') {
      const validationErrors = validateDraftInvoice();
      if (validationErrors.length > 0) {
        const errorMessage = `This draft invoice is incomplete and cannot accept payments:\n\n${validationErrors.join('\n')}`;
        showError('Incomplete Draft Invoice', errorMessage);
        return;
      }

      const paymentAmount = paymentData.amount;
      const totalAmount = total;
      let newStatus: 'pending' | 'paid';
      let statusText: string;
      
      if (paymentData.type === 'refund') {
        newStatus = 'pending';
        statusText = 'Pending';
      } else if (paymentAmount < totalAmount) {
        newStatus = 'pending';
        statusText = 'Pending';
      } else if (paymentAmount >= totalAmount) {
        newStatus = 'paid';
        statusText = 'Paid';
      } else {
        newStatus = 'pending';
        statusText = 'Pending';
      }
      
      const message = `Recording this ${paymentData.type === 'refund' ? 'refund' : 'payment'} will change the invoice status from Draft to ${statusText}.`;
      
      showConfirmation(
        'Update Invoice Status',
        message,
        () => {
          setShowConfirmationDialog(false); // Close confirmation dialog first
          processPaymentRecord(paymentData, newStatus);
        },
        false
      );
      return;
    }
    
    // For non-draft invoices, process payment directly
    await processPaymentRecord(paymentData);
  };

  const validateDraftInvoice = (): string[] => {
    const errors: string[] = [];
    
    // Check if client exists and is valid
    if (!client || !client.id) {
      errors.push('• Client is not selected or invalid');
    }
    
    // Check if there are line items
    if (!items || items.length === 0) {
      errors.push('• At least one line item is required');
    }
    
    // Check if total amount is valid
    if (!total || total <= 0) {
      errors.push('• Invoice total must be greater than $0.00');
    }
    
    // Check if invoice has required dates
    if (!invoice.issueDate) {
      errors.push('• Issue date is required');
    }
    
    if (!invoice.dueDate) {
      errors.push('• Due date is required');
    }
    
    // Check if invoice number exists
    if (!invoice.invoiceNumber || invoice.invoiceNumber.trim() === '') {
      errors.push('• Invoice number is required');
    }
    
    return errors;
  };

  const processPaymentRecord = async (paymentData: PaymentRecordData, newStatus?: 'pending' | 'paid') => {
    try {
      await createPaymentMutation.mutateAsync(paymentData);
      
      // If newStatus is provided, update the invoice status
      if (newStatus && invoice.status === 'draft') {
        try {
          await updateInvoice({
            organizationId: invoice.organizationId,
            id: invoice.id,
            status: newStatus
          });
          // Invalidate invoice cache to refresh the status
          queryClient.invalidateQueries({ 
            queryKey: ['invoice', organizationId, invoice.id] 
          });
        } catch (error) {
          console.error('Failed to update invoice status:', error);
          // Don't fail the payment recording if status update fails
        }
      }
      
      setShowPaymentSheet(false);
      
      // Show appropriate success message based on payment type
      const transactionType = paymentData.type === 'refund' ? 'Refund' : 'Payment';
      const actionType = paymentData.type === 'refund' ? 'processed' : 'recorded';
      showSuccess(
        `${transactionType} ${actionType === 'recorded' ? 'Recorded' : 'Processed'}`, 
        `${transactionType} of ${formatCurrency(paymentData.amount)} has been ${actionType} successfully.`
      );
    } catch (error: any) {
      console.error('Payment creation error:', error);
      showError('Error', error.message || 'Failed to record payment. Please try again.');
    }
  };

  const handlePaymentDetail = (payment: any) => {
    setSelectedPayment(payment);
    setShowPaymentDetailSheet(true);
    Animated.spring(paymentDetailAnimation, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const closePaymentDetailSheet = () => {
    Animated.spring(paymentDetailAnimation, {
      toValue: 0,
      useNativeDriver: true,
    }).start(() => {
      setShowPaymentDetailSheet(false);
      setSelectedPayment(null);
    });
  };

  const openPaymentSheet = () => {
    // Check if invoice is already 100% paid
    if (paymentStatus.status === 'paid' && paymentStatus.remainingBalance === 0) {
      showToast('Payment is already completed', 'info');
      return;
    }
    
    setShowPaymentSheet(true);
  };

  const closePaymentSheet = () => {
    setShowPaymentSheet(false);
  };

  const getInvoiceActions = () => [
    {
      id: 'edit',
      title: 'Edit Invoice',
      icon: 'create-outline' as React.ComponentProps<typeof Ionicons>['name'],
      onPress: handleEdit,
    },
    {
      id: 'duplicate',
      title: 'Duplicate',
      icon: 'copy-outline' as React.ComponentProps<typeof Ionicons>['name'],
      onPress: handleDuplicate,
    },
    {
      id: 'download',
      title: 'Download PDF',
      icon: 'download-outline' as React.ComponentProps<typeof Ionicons>['name'],
      onPress: () => showInfo('Download', 'PDF download functionality will be implemented soon.'),
    },
    {
      id: 'delete',
      title: 'Delete',
      icon: 'trash-outline' as React.ComponentProps<typeof Ionicons>['name'],
      onPress: handleDelete,
      destructive: true,
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'details':
        return (
          <View style={styles.tabContent}>
            {/* Invoice Items - Card Style */}
            <View style={styles.section}>
              <View style={styles.itemsHeader}>
                <Typography variant="h4" bold style={styles.sectionTitle}>
                  Items & Services
                </Typography>
                <Typography variant="bodySmall" color="secondary">
                  {items.length} {items.length === 1 ? 'item' : 'items'}
                </Typography>
              </View>
              
              <View style={styles.itemsContainer}>
                {items.map((item: any, index: number) => {
                  // Convert to the format expected by InvoiceLineItem
                  const lineItem = {
                    id: item.id,
                    description: item.description,
                    quantity: item.quantity.toString(),
                    price: item.unitPrice.toString(),
                    total: item.total.toString(),
                    itemDescription: item.itemDescription || '',
                    discount: item.discount || '',
                    discountType: item.discountType || 'percentage' as 'percentage' | 'fixed',
                    unit: (item.unit || 'fixed') as 'hour' | 'fixed' | 'month' | 'custom'
                  };

                  return (
                    <InvoiceLineItem
                      key={item.id}
                      item={lineItem}
                      onEdit={() => handleLineItemPress(item)}
                      itemNumber={index + 1}
                    />
                  );
                })}
              </View>

              {/* Subtotal Summary */}
              <View style={styles.itemsSummary}>
                <View style={styles.summaryRow}>
                  <Typography variant="body" color="secondary">Items Subtotal</Typography>
                  <Typography variant="body" bold>{formatCurrency(itemsSubtotal)}</Typography>
                </View>
                {taxAmount > 0 && (
                  <View style={styles.summaryRow}>
                    <Typography variant="bodySmall" color="secondary">Tax Amount:</Typography>
                    <Typography variant="bodySmall">{formatCurrency(taxAmount)}</Typography>
                  </View>
                )}
              </View>

              {/* Attachments Section */}
              {invoice.attachments && invoice.attachments.length > 0 && (
                <View style={styles.section}>
                  <Typography variant="h4" bold style={styles.sectionTitle}>
                    Attachments
                  </Typography>
                  <View style={styles.attachmentsContainer}>
                    {invoice.attachments.map((attachment: any) => (
                      <TouchableOpacity
                        key={attachment.id}
                        style={styles.attachmentItem}
                        onPress={() => {
                          // Handle attachment view/download
                          showInfo('Attachment', `Opening "${attachment.name}"...`);
                        }}
                      >
                        <View style={styles.attachmentIcon}>
                          <Ionicons 
                            name={getAttachmentIcon(attachment.type)} 
                            size={20} 
                            color={colors.primary} 
                          />
                        </View>
                        <View style={styles.attachmentInfo}>
                          <Typography variant="body" bold numberOfLines={1}>
                            {attachment.name}
                          </Typography>
                          <Typography variant="bodySmall" color="secondary">
                            {formatAttachmentSize(attachment.size)} • {getAttachmentExtension(attachment.name)}
                          </Typography>
                        </View>
                        <Ionicons name="download-outline" size={16} color={colors.text.secondary} />
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              )}

              {/* Tax Information - Show when there's tax config */}
              {invoice && taxConfig && taxConfig.enabled && (
                <View style={styles.section}>
                  <Typography variant="h4" bold style={styles.sectionTitle}>
                    Tax Information
                  </Typography>
                  <View style={styles.taxCard}>
                    <View style={styles.taxHeader}>
                      <View style={styles.taxBadge}>
                        <Typography variant="caption" color="primary" bold>
                          {taxConfig?.taxName}
                        </Typography>
                      </View>
                      <Typography variant="body" bold>
                        {taxConfig?.defaultRate}% {taxConfig?.inclusive ? 'Inclusive' : 'Exclusive'}
                      </Typography>
                    </View>
                    
                    <View style={styles.taxBreakdown}>
                      <View style={styles.taxRow}>
                        <Typography variant="bodySmall" color="secondary">Items Subtotal:</Typography>
                        <Typography variant="bodySmall">{formatCurrency(itemsSubtotal)}</Typography>
                      </View>
                      <View style={styles.taxRow}>
                        <Typography variant="bodySmall" color="secondary">Tax Rate:</Typography>
                        <Typography variant="bodySmall">{taxConfig?.defaultRate}%</Typography>
                      </View>
                      <View style={[styles.taxRow, styles.taxTotal]}>
                        <Typography variant="body" bold>Tax Amount:</Typography>
                        <Typography variant="body" bold color="primary">{formatCurrency(Math.abs(taxAmount))}</Typography>
                      </View>
                    </View>
                  </View>
                </View>
              )}
            </View>
          </View>
        );

      case 'payment':
        return (
          <View style={styles.tabContent}>
            {/* Payment Status Overview */}
            <View style={styles.section}>
              <Typography variant="h4" bold style={styles.sectionTitle}>
                Payment Status
              </Typography>
              <View style={styles.paymentStatusCard}>
                <View style={styles.paymentStatusMain}>
                  <View style={styles.paymentAmountSection}>
                    <Typography variant="h3" bold color="primary">
                      {formatCurrency(paymentStatus.remainingBalance)}
                    </Typography>
                    <Typography variant="bodySmall" color="secondary">
                      {paymentStatus.remainingBalance > 0 ? 'Outstanding' : 'Paid in Full'}
                    </Typography>
                  </View>
                  
                  <View style={styles.paymentStatusBadgeContainer}>
                    <View style={[
                      styles.paymentStatusIndicatorBadge,
                      {
                        backgroundColor: paymentStatus.status === 'partially_paid' ? colors.cardBackground : colors.primaryVeryLight
                      }
                    ]}>
                      <Ionicons 
                        name={
                          paymentStatus.status === 'paid' ? 'checkmark-circle' : 
                          paymentStatus.status === 'partially_paid' ? 'time' :
                          paymentStatus.status === 'overpaid' ? 'arrow-up-circle' :
                          paymentStatus.status === 'refunded' ? 'return-down-back' :
                          'close-circle'
                        }
                        size={16} 
                        color={
                          paymentStatus.status === 'paid' ? colors.success : 
                          paymentStatus.status === 'partially_paid' ? colors.warning :
                          paymentStatus.status === 'overpaid' ? colors.primary :
                          paymentStatus.status === 'refunded' ? colors.warning :
                          colors.error
                        }
                      />
                      <Typography 
                        variant="caption" 
                        bold 
                        color="primary"
                      >
                        {paymentStatus.status === 'paid' ? 'Paid' : 
                         paymentStatus.status === 'partially_paid' ? 'Partial' :
                         paymentStatus.status === 'overpaid' ? 'Overpaid' :
                         paymentStatus.status === 'refunded' ? 'Refunded' :
                         'Unpaid'}
                      </Typography>
                    </View>
                  </View>
                </View>

                {/* Compact Payment Summary */}
                {(paymentStatus.netPaid > 0 || paymentStatus.totalRefunded > 0) && (
                  <View style={styles.paymentSummaryCompact}>
                    <Typography variant="caption" color="secondary">
                      Paid: {formatCurrency(paymentStatus.netPaid)} • Total: {formatCurrency(paymentStatus.adjustedTotal)}
                    </Typography>
                  </View>
                )}
              </View>
            </View>

            {/* Payment History */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Typography variant="h4" bold style={styles.sectionTitle}>
                  Payment History
                </Typography>
                <Typography variant="bodySmall" color="secondary">
                  {payments.length} {payments.length === 1 ? 'transaction' : 'transactions'}
                </Typography>
              </View>

              {paymentsLoading ? (
                <View style={styles.paymentLoadingCard}>
                  <Typography variant="body" color="secondary" center>
                    Loading payments...
                  </Typography>
                </View>
              ) : payments.length === 0 ? (
                <View style={styles.emptyPaymentsCard}>
                  <Ionicons name="card-outline" size={48} color={colors.text.secondary} />
                  <Typography variant="body" color="secondary" center style={styles.emptyPaymentsTitle}>
                    No payments recorded yet
                  </Typography>
                  <Typography variant="bodySmall" color="secondary" center style={styles.emptyPaymentsSubtext}>
                    Tap the + button below to record your first payment
                  </Typography>
                </View>
              ) : (
                <View style={styles.paymentsList}>
                  {payments
                    .sort((a, b) => new Date(b.paymentDate).getTime() - new Date(a.paymentDate).getTime())
                    .map((payment) => (
                    <TouchableOpacity
                      key={payment.id}
                      style={styles.paymentItem}
                      onPress={() => handlePaymentDetail(payment)}
                      activeOpacity={0.7}
                    >
                      <View style={styles.paymentIcon}>
                        <Ionicons 
                          name={getPaymentMethodIcon(payment.method)} 
                          size={20} 
                          color={payment.type === 'refund' ? colors.warning : colors.success}
                        />
                      </View>
                      
                      <View style={styles.paymentDetails}>
                        <View style={styles.paymentTopRow}>
                          <Typography variant="body" bold style={styles.paymentAmount}>
                            {payment.type === 'refund' ? '-' : '+'}{formatCurrency(Math.abs(payment.amount))}
                          </Typography>
                          <Typography variant="bodySmall" color="secondary">
                            {formatDate(payment.paymentDate)}
                          </Typography>
                        </View>
                        
                        <View style={styles.paymentBottomRow}>
                          <Typography variant="bodySmall" color="secondary">
                            {formatPaymentMethod(payment.method)}
                          </Typography>
                        </View>
                        
                        {payment.reference && (
                          <Typography variant="caption" color="secondary" numberOfLines={1} style={styles.paymentReference}>
                            {payment.reference}
                          </Typography>
                        )}
                      </View>
                      
                      <View style={styles.paymentChevron}>
                        <Ionicons name="chevron-forward" size={16} color={colors.text.secondary} />
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          </View>
        );

      case 'activity':
        return (
          <View style={styles.tabContent}>
            <View style={styles.section}>
              <Typography variant="h4" bold style={styles.sectionTitle}>
                Invoice Activity
              </Typography>
              
              {activities.length === 0 ? (
                <View style={styles.activityCard}>
                  <Typography variant="body" color="secondary" center style={styles.emptyActivity}>
                    No activity recorded yet
                  </Typography>
                  <Typography variant="bodySmall" color="secondary" center style={styles.emptyActivitySubtext}>
                    Activity will appear here when available
                  </Typography>
                </View>
              ) : (
                <View style={styles.activityTimeline}>
                  {activities.map((activity, index) => (
                    <View key={activity.id} style={styles.activityItem}>
                      <View style={styles.activityIconContainer}>
                        <View style={[styles.activityIcon]}>
                          <Ionicons 
                            name={getActivityIcon(activity.type)} 
                            size={16} 
                            color={getActivityColor(activity.type)}
                          />
                        </View>
                        {index < activities.length - 1 && <View style={styles.activityLine} />}
                      </View>
                      
                      <View style={styles.activityContent}>
                        <Typography variant="body" bold style={styles.activityMessage}>
                          {activity.description}
                        </Typography>
                        
                        {activity.metadata && renderActivityMetadata(activity.metadata)}
                        
                        <Typography variant="caption" color="secondary" style={styles.activityDate}>
                          {formatActivityTime(activity.createdAt || new Date())}
                        </Typography>
                      </View>
                    </View>
                  ))}
                </View>
              )}
            </View>

            {/* Comments Section */}
            <View style={styles.section}>
              <View style={styles.commentsHeader}>
                <Typography variant="h4" bold style={styles.sectionTitle}>
                  Comments & Notes
                </Typography>
                <TouchableOpacity 
                  style={styles.addCommentButton}
                  onPress={() => setShowAddComment(true)}
                >
                  <Ionicons name="add" size={16} color={colors.primary} />
                  <Typography variant="bodySmall" color="primary" style={styles.addCommentText}>
                    Add Note
                  </Typography>
                </TouchableOpacity>
              </View>
              
              {showAddComment && (
                <View style={styles.addCommentCard}>
                  <Typography variant="body" bold style={styles.addCommentTitle}>
                    Add Internal Note
                  </Typography>
                  <TextInput
                    style={styles.commentInput}
                    placeholder="Enter your note..."
                    placeholderTextColor={colors.text.secondary}
                    value={newComment}
                    onChangeText={setNewComment}
                    multiline
                    numberOfLines={3}
                    textAlignVertical="top"
                  />
                  <View style={styles.commentActions}>
                    <TouchableOpacity 
                      style={styles.cancelButton}
                      onPress={() => setShowAddComment(false)}
                    >
                      <Typography variant="bodySmall" color="secondary">
                        Cancel
                      </Typography>
                    </TouchableOpacity>
                    <TouchableOpacity 
                      style={[styles.saveButton, !newComment.trim() && styles.saveButtonDisabled]}
                      onPress={() => {
                        // Handle adding a new comment
                        console.log('Adding new comment:', newComment);
                        setShowAddComment(false);
                      }}
                      disabled={!newComment.trim()}
                    >
                      <Typography 
                        variant="bodySmall" 
                        color={'white'}
                        bold
                      >
                        Add Note
                      </Typography>
                    </TouchableOpacity>
                  </View>
                </View>
              )}

              {invoice.comments.length === 0 ? (
                <View style={styles.commentsCard}>
                  <Typography variant="body" color="secondary" center style={styles.emptyComments}>
                    No comments yet
                  </Typography>
                  <Typography variant="bodySmall" color="secondary" center style={styles.emptyCommentsSubtext}>
                    Add internal notes or client communications here
                  </Typography>
                </View>
              ) : (
                <View style={styles.commentsTimeline}>
                  {invoice.comments.map((comment) => (
                    <View key={comment.id} style={styles.commentItem}>
                      <View style={styles.commentIcon}>
                        <Ionicons 
                          name={comment.type === 'internal' ? 'person-outline' : 'chatbubble-outline'} 
                          size={14} 
                          color={comment.type === 'internal' ? colors.primary : colors.success}
                        />
                      </View>
                      <View style={styles.commentContent}>
                        <View style={styles.commentHeader}>
                          <Typography variant="bodySmall" color="secondary" style={styles.commentType}>
                            {comment.type === 'internal' ? 'Internal Note' : 'Client Communication'}
                          </Typography>
                          <Typography variant="caption" color="secondary" style={styles.commentTime}>
                            {formatCommentTime(comment.createdAt || new Date())}
                          </Typography>
                        </View>
                        <Typography variant="body" style={styles.commentText}>
                          {comment.text}
                        </Typography>
                      </View>
                    </View>
                  ))}
                </View>
              )}
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setToastMessage(message);
    setToastType(type);
    setToastVisible(true);
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar style="dark" />
      
      {/* Everything scrollable - keeping exact same layout */}
      <ScrollView 
        style={styles.contentContainer}
        contentContainerStyle={[
          { 
            paddingBottom: insets.bottom + 20 + (activeTab === 'payment' ? 80 : 0)
          }
        ]}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={[styles.header, { paddingTop: insets.top }]}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <View style={styles.headerCenter}>
            <Typography variant="body" style={styles.headerTitle}>
              Invoice Details
            </Typography>
          </View>
          <View style={styles.headerActions}>
            <TouchableOpacity style={styles.headerActionButton} onPress={handlePreview}>
              <Ionicons name="eye-outline" size={20} color={colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.headerActionButton} onPress={handleShare}>
              <Ionicons name="share-outline" size={20} color={colors.primary} />
            </TouchableOpacity>
            <ActionMenu 
              items={getInvoiceActions()}
              style={styles.headerActionMenu}
            />
          </View>
        </View>

        {/* Invoice Header Section */}
        <View style={styles.invoiceHeaderSection}>
          <View style={styles.invoiceHeaderTop}>
            <View style={styles.invoiceInfoContainer}>
              <Typography variant="caption" color="secondary" style={styles.invoiceLabel}>
                Invoice
              </Typography>
              <Typography 
                variant="h4" 
                bold 
                style={styles.invoiceNumber}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {invoice.invoiceNumber}
              </Typography>
            </View>
            <View style={styles.amountContainer}>
              <Typography variant="caption" color="secondary" style={styles.amountLabel}>
                Total Amount
              </Typography>
              <Typography variant="h3" bold color="primary" style={styles.totalAmount}>
                {invoice.amount}
              </Typography>
            </View>
          </View>
          
          <View style={styles.datesContainer}>
            {/* Client and Issued Date - Left Side */}
            <Pressable style={styles.clientInfoCompact} onPress={() => {
              if (client?.id) {
                router.push(`/client-detail?clientId=${client.id}`);
              }
            }}>
              <View style={styles.clientInfoText}>
                <Typography variant="caption" color="secondary" style={styles.clientLabel}>
                  Client
                </Typography>
                <View style={styles.clientNameRow}>
                  <Avatar
                    name={client?.name}
                    photo={client?.photo}
                    size={24}
                  />
                  <Typography variant="bodySmall" bold color="primary" style={styles.headerClientName}>
                    {getClientDisplayName()}
                  </Typography>
                </View>
                
                <Typography variant="caption" color="secondary" style={styles.issuedLabel}>
                  Issued
                </Typography>
                <Typography variant="bodySmall" bold style={styles.issuedDate}>
                  {formatDate(invoice.issueDate)}
                </Typography>
              </View>
            </Pressable>
            
            {/* Status and Due Date - Right Side */}
            <View style={styles.datesStack}>
              <View style={styles.dateItem}>
                <Typography variant="caption" color="secondary">Status</Typography>
                <StatusBadge status={invoice.status} size="small" style={styles.statusBadgeCompact} />
              </View>
              <View style={styles.dateItem}>
                <Typography variant="caption" color="secondary">Due</Typography>
                <Typography variant="bodySmall" bold color={invoice.status === 'overdue' ? 'error' : 'primary'}>
                  {formatDate(invoice.dueDate)}
                </Typography>
              </View>
            </View>
          </View>
        </View>

        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          <View style={styles.tabNavigation}>
            <Pressable
              style={[styles.tab, activeTab === 'details' && styles.activeTab]}
              onPress={() => setActiveTab('details')}
            >
              <Ionicons 
                name="document-text-outline" 
                size={18} 
                color={activeTab === 'details' ? colors.primary : colors.text.secondary}
                style={styles.tabIcon}
              />
              <Typography 
                variant="bodySmall" 
                color={activeTab === 'details' ? 'primary' : 'secondary'}
                style={styles.tabLabel}
              >
                Details
              </Typography>
            </Pressable>

            <Pressable
              style={[styles.tab, activeTab === 'payment' && styles.activeTab]}
              onPress={() => setActiveTab('payment')}
            >
              <Ionicons 
                name="card-outline" 
                size={18} 
                color={activeTab === 'payment' ? colors.primary : colors.text.secondary}
                style={styles.tabIcon}
              />
              <Typography 
                variant="bodySmall" 
                color={activeTab === 'payment' ? 'primary' : 'secondary'}
                style={styles.tabLabel}
              >
                Payment
              </Typography>
            </Pressable>

            <Pressable
              style={[styles.tab, activeTab === 'activity' && styles.activeTab]}
              onPress={() => setActiveTab('activity')}
            >
              <Ionicons 
                name="time-outline" 
                size={18} 
                color={activeTab === 'activity' ? colors.primary : colors.text.secondary}
                style={styles.tabIcon}
              />
              <Typography 
                variant="bodySmall" 
                color={activeTab === 'activity' ? 'primary' : 'secondary'}
                style={styles.tabLabel}
              >
                Activity
              </Typography>
            </Pressable>
          </View>
        </View>

        {/* Tab Content */}
        <View style={styles.tabContentWrapper}>
          {renderTabContent()}
        </View>
      </ScrollView>

      {/* Floating Action Button - Only show on payment tab */}
      {activeTab === 'payment' && (
        <FloatingActionButton
          onPress={openPaymentSheet}
          iconName="add"
        />
      )}

      {/* Payment Record Sheet */}
      <PaymentRecordSheet
        visible={showPaymentSheet}
        onClose={closePaymentSheet}
        onSubmit={handlePaymentRecord}
        invoice={{
          id: invoice.id || '',
          invoiceNumber: invoice.invoiceNumber || '',
          clientName: getClientDisplayName(),
          amount: invoice.amount || formatCurrency(total),
          status: invoice.status || 'draft'
        }}
        invoiceTotal={total}
        outstandingBalance={paymentStatus.remainingBalance}
      />

      {/* Custom Dialogs */}
      <InfoDialog
        visible={showInfoDialog}
        title={infoDialogConfig.title}
        message={infoDialogConfig.message}
        onClose={() => setShowInfoDialog(false)}
      />

      <ErrorDialog
        visible={showErrorDialog}
        title={errorDialogConfig.title}
        message={errorDialogConfig.message}
        onClose={() => setShowErrorDialog(false)}
      />

      <SuccessDialog
        visible={showSuccessDialog}
        title={successDialogConfig.title}
        message={successDialogConfig.message}
        onClose={() => setShowSuccessDialog(false)}
      />

      <ConfirmationDialog
        visible={showConfirmationDialog}
        title={confirmationDialogConfig.title}
        message={confirmationDialogConfig.message}
        onConfirm={confirmationDialogConfig.onConfirm}
        onCancel={() => setShowConfirmationDialog(false)}
        destructive={confirmationDialogConfig.destructive}
      />

      <PaymentDetailSheet
        visible={showPaymentDetailSheet}
        onClose={closePaymentDetailSheet}
        payment={selectedPayment}
      />

      <Toast
        visible={toastVisible}
        message={toastMessage}
        type={toastType}
        onHide={() => setToastVisible(false)}
        position="bottom"
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 12,
    backgroundColor: colors.cardBackground,
  },
  backButton: {
    padding: 4,
    marginRight: 16,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerActionButton: {
    padding: 4,
  },
  headerActionMenu: {
    padding: 0,
  },
  
  // Invoice Header Section
  invoiceHeaderSection: {
    backgroundColor: colors.cardBackground,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  invoiceHeaderTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  invoiceInfoContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    flex: 1,
    minWidth: 0, // Allow shrinking
  },
  invoiceNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.text.primary,
    flexShrink: 1, // Allow text to shrink
    minWidth: 0, // Enable text truncation
    maxWidth: '70%', // Limit horizontal width
    marginTop: 8, // Match the spacing with amount container
  },
  statusBadge: {
    marginTop: 2,
  },
  amountContainer: {
    alignItems: 'flex-end',
  },
  amountLabel: {
    fontSize: 11,
    fontWeight: '500',
    marginBottom: 8, // Match the spacing with invoice container
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  totalAmount: {
    fontSize: 26,
    fontWeight: '800',
  },
  datesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingTop: 12,
    gap: 12,
  },
  datesStack: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    gap: 8,
  },
  dateItem: {
    flex: 0,
    alignItems: 'flex-end',
    marginBottom: 8,
  },
  clientInfoCompact: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    flex: 1,
    justifyContent: 'flex-start',
    maxWidth: '60%',
  },
  clientInfoText: {
    flexShrink: 1,
    minWidth: 0,
    alignItems: 'flex-start',
    maxWidth: '100%',
  },
  clientLabel: {
    fontSize: 10,
    fontWeight: '500',
    color: colors.text.secondary,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    textAlign: 'left',
    marginBottom: 4,
  },
  clientNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    maxWidth: '100%',
  },
  headerClientName: {
    fontSize: 13,
    fontWeight: '600',
    textAlign: 'left',
    marginLeft: 6,
    flexShrink: 1,
    minWidth: 0,
  },

  // Tab Navigation
  tabContainer: {
    backgroundColor: colors.cardBackground,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  tabNavigation: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 8,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 10,
    backgroundColor: 'transparent',
  },
  activeTab: {
    backgroundColor: colors.primaryVeryLight,
  },
  tabIcon: {
    marginRight: 6,
  },
  tabLabel: {
    fontSize: 13,
    fontWeight: '500',
  },

  // Content
  contentContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  tabContent: {
    flex: 1,
  },
  tabContentWrapper: {
    padding: 16,
  },
  
  // Sections
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  
  // Items Section
  itemsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  itemsContainer: {
    gap: 8,
  },
  itemsSummary: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: colors.divider,
  },
  
  // Summary Card
  summaryCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  totalRow: {
    borderTopWidth: 2,
    borderTopColor: colors.primary,
    marginTop: 8,
    paddingTop: 20,
  },

  // Payment Card
  paymentCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },

  // Activity Timeline
  activityTimeline: {
    flex: 1,
  },
  activityItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  activityIconContainer: {
    alignItems: 'center',
    marginRight: 12,
    width: 32,
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 20,
    borderColor: colors.disabled,
    borderWidth: StyleSheet.hairlineWidth,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  activityLine: {
    width: 2,
    flex: 1,
    backgroundColor: colors.divider,
    position: 'absolute',
    top: 40,
    left: 15,
    bottom: -16,
  },
  activityContent: {
    flex: 1,
    paddingTop: 4,
  },
  activityMessage: {
    fontSize: 15,
    fontWeight: '500',
    color: colors.text.primary,
    marginBottom: 4,
  },
  activityDate: {
    fontSize: 13,
    color: colors.text.secondary,
  },
  activityMetadata: {
    marginTop: 6,
    paddingTop: 6,
    borderTopWidth: 1,
    borderTopColor: colors.divider,
    gap: 3,
    marginBottom: 8,
  },
  metadataText: {
    fontSize: 12,
    color: colors.text.secondary,
  },

  // Activity Card (for empty state)
  activityCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 24,
    borderWidth: 1,
    borderColor: colors.divider,
    alignItems: 'center',
  },
  emptyActivity: {
    fontSize: 16,
    marginBottom: 8,
  },
  emptyActivitySubtext: {
    fontSize: 14,
  },

  // Comments
  commentsCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 16,
    padding: 32,
    borderWidth: 1,
    borderColor: colors.divider,
    alignItems: 'center',
  },
  emptyComments: {
    fontSize: 16,
    marginBottom: 8,
  },
  emptyCommentsSubtext: {
    fontSize: 14,
  },
  
  // Actions
  actionsSection: {
    marginTop: 8,
    gap: 12,
  },
  primaryButton: {
    backgroundColor: colors.primary,
  },
  successButton: {
    backgroundColor: colors.success,
  },
  outlineButton: {
    borderColor: colors.primary,
  },

  // Tax Information
  taxCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  taxHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  taxBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: colors.primaryVeryLight,
    borderRadius: 6,
  },
  taxBreakdown: {
    paddingBottom: 12,
    marginBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  taxRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  taxTotal: {
    paddingTop: 8,
    marginTop: 4,
    borderTopWidth: 1,
    borderTopColor: colors.divider,
  },

  // New styles for the invoice header
  invoiceLabel: {
    fontSize: 10,
    fontWeight: '500',
    color: colors.text.secondary,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  issuedLabel: {
    fontSize: 10,
    fontWeight: '500',
    color: colors.text.secondary,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    marginBottom: 4,
    marginTop: 12,
  },
  issuedDate: {
    fontSize: 13,
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'left',
  },
  statusBadgeCompact: {
    marginTop: 0,
    alignSelf: 'flex-end',
  },

  // Comments Section
  commentsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  addCommentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderWidth: 1,
    borderColor: colors.primary,
    borderRadius: 6,
  },
  addCommentText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.primary,
    marginLeft: 6,
  },
  addCommentCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  addCommentTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: 12,
  },
  commentInput: {
    borderWidth: 1,
    borderColor: colors.divider,
    borderRadius: 8,
    padding: 12,
    backgroundColor: colors.background,
    color: colors.text.primary,
    minHeight: 80,
    fontSize: 14,
  },
  commentActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: colors.divider,
    borderRadius: 6,
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: colors.primary,
    borderRadius: 6,
  },
  saveButtonDisabled: {
    backgroundColor: colors.disabled,
  },
  commentsTimeline: {
    marginTop: 12,
  },
  commentItem: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  commentIcon: {
    marginRight: 12,
  },
  commentContent: {
    flex: 1,
  },
  commentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  commentType: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.text.primary,
  },
  commentTime: {
    fontSize: 10,
    color: colors.text.secondary,
  },
  commentText: {
    fontSize: 14,
    color: colors.text.primary,
  },

  // Attachments styles
  attachmentsContainer: {
    gap: 8,
  },
  attachmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  attachmentIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primaryVeryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  attachmentInfo: {
    flex: 1,
    marginRight: 8,
  },

  // Payment Status Card
  paymentStatusCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  paymentStatusMain: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  paymentAmountSection: {
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  paymentStatusAmount: {
    fontSize: 26,
    fontWeight: '800',
  },
  paymentStatusLabel: {
    fontSize: 13,
    fontWeight: '500',
    color: colors.text.secondary,
  },
  paymentStatusBadgeContainer: {
    alignItems: 'flex-end',
  },
  paymentStatusIndicatorBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  paymentStatusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  paymentSummaryCompact: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: colors.divider,
    alignItems: 'center',
  },
  paymentSummaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  paymentSummaryTotal: {
    paddingTop: 8,
    marginTop: 4,
    borderTopWidth: 1,
    borderTopColor: colors.divider,
  },

  // Payment Loading Card
  paymentLoadingCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 24,
    borderWidth: 1,
    borderColor: colors.divider,
    alignItems: 'center',
  },
  emptyPaymentsCard: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 24,
    borderWidth: 1,
    borderColor: colors.divider,
    alignItems: 'center',
  },
  emptyPaymentsTitle: {
    fontSize: 16,
    marginBottom: 8,
  },
  emptyPaymentsSubtext: {
    fontSize: 14,
  },

  // Payments List
  paymentsList: {
    gap: 8,
  },
  paymentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  paymentIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primaryVeryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  paymentDetails: {
    flex: 1,
  },
  paymentTopRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  paymentAmount: {
    fontSize: 15,
    fontWeight: '500',
    color: colors.text.primary,
  },
  paymentBottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  paymentChevron: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  paymentReference: {
    marginTop: 4,
  },
}); 