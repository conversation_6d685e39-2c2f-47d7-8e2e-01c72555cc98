import { ClientCreationForm, KeyboardAwareView, Toast, Typography } from '@/components/ui';
import { colors } from '@/constants/Colors';
import { useClient } from '@/services/client/clients';
import { useUpdateClient } from '@/services/client/update';
import { useActiveOrganizationId } from '@/stores';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export default function EditClientScreen() {
  const insets = useSafeAreaInsets();
  const { clientId } = useLocalSearchParams<{ clientId: string }>();
  const activeOrganizationId = useActiveOrganizationId();
  const { client, loading } = useClient(clientId || '');
  const { updateClient } = useUpdateClient();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error'>('success');

  const handleSubmit = async (formData: any) => {
    if (isSubmitting || !activeOrganizationId || !client) return;
    
    setIsSubmitting(true);
    
    try {
      const updatedClientData = {
        name: formData.name,
        displayName: formData.displayName,
        company: formData.company,
        contact: {
          email: formData.email,
          phone: formData.phone || undefined,
        },
        address: {
          fullAddress: formData.address || undefined,
        },
        photo: formData.photo,
        notes: formData.notes,
        isActive: client.isActive,
        defaultTaxExempt: client.defaultTaxExempt,
      };

      await updateClient({
        clientId: client.id,
        updates: updatedClientData
      });
      
      setToastMessage('Client updated successfully!');
      setToastType('success');
      setToastVisible(true);
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      console.error('Error updating client:', error);
      setToastMessage('Failed to update client. Please try again.');
      setToastType('error');
      setToastVisible(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <StatusBar style="dark" />
        <View style={styles.loadingContainer}>
          <Typography variant="body" color="secondary">Loading client...</Typography>
        </View>
      </View>
    );
  }

  if (!client) {
    return (
      <View style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <StatusBar style="dark" />
        <View style={styles.loadingContainer}>
          <Typography variant="body" color="secondary">Client not found</Typography>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar style="dark" />
      
      {/* Header */}
      <View 
        style={[
          styles.header,
          { paddingTop: insets.top + 16 }
        ]}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleCancel}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Typography variant="body" style={styles.headerTitle}>
          Edit Client
        </Typography>
        <View style={styles.headerRightPlaceholder} />
      </View>

      {/* Content */}
      <KeyboardAwareView 
        style={styles.keyboardAvoidingView}
        contentContainerStyle={{
          ...styles.contentContainer,
          paddingBottom: Math.max(32, insets.bottom + 32)
        }}
      >
        <View style={styles.formContainer}>
          <Typography variant="h3" style={styles.title}>
            Edit Client Details
          </Typography>
          <Typography variant="body" color="secondary" style={styles.subtitle}>
            Update client information
          </Typography>
          
          <ClientCreationForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            submitButtonText="Save Changes"
            cancelButtonText="Cancel"
            initialData={{
              name: client.name,
              displayName: client.displayName || '',
              company: client.company || '',
              email: client.contact?.email || '',
              phone: client.contact?.phone || '',
              address: client.address?.fullAddress || '',
              photo: client.photo,
              notes: client.notes || '',
            }}
          />
        </View>
      </KeyboardAwareView>
      
      <Toast
        visible={toastVisible}
        message={toastMessage}
        type={toastType}
        onHide={() => setToastVisible(false)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.cardBackground,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 6,
    backgroundColor: colors.cardBackground,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
  },
  backButton: {
    padding: 4,
    marginLeft: -4,
  },
  headerRightPlaceholder: {
    width: 24 + 8,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  formContainer: {
    flex: 1,
  },
  title: {
    marginBottom: 8,
  },
  subtitle: {
    marginBottom: 24,
  },
}); 