import { KeyboardAwareView, ServiceCreationForm, Toast, Typography } from '@/components/ui';
import { colors } from '@/constants/Colors';
import { useService } from '@/services/service/services';
import { useUpdateService } from '@/services/service/update';
import { useHasActiveOrganization } from '@/stores';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

export default function EditServiceScreen() {
  const insets = useSafeAreaInsets();
  const { serviceId } = useLocalSearchParams<{ serviceId: string }>();
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error'>('success');

  // Clean organization guards
  const hasActiveOrganization = useHasActiveOrganization();

  // Get service data
  const { service, loading, error } = useService(serviceId || '');
  const { updateService } = useUpdateService();

  // Organization selection guard
  if (!hasActiveOrganization) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        <View style={styles.centerContainer}>
          <Typography variant="h3" color="secondary" style={{ marginBottom: 8 }}>
            Select an Organization
          </Typography>
          <Typography variant="body" color="secondary" center>
            Please select an organization to edit service
          </Typography>
        </View>
      </SafeAreaView>
    );
  }

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        <View style={styles.centerContainer}>
          <Typography variant="body" color="secondary">Loading service...</Typography>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !service) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        <View style={styles.centerContainer}>
          <Typography variant="body" color="secondary">Service not found</Typography>
        </View>
      </SafeAreaView>
    );
  }

  const handleBack = () => {
    router.back();
  };

  const handleUpdateService = async (serviceData: {
    name: string;
    description: string;
    pricing: {
      rate: number;
      unit: string;
      currency: string;
    };
    isActive: boolean;
    taxable: boolean;
    tags: string[];
  }) => {
    try {
      await updateService({
        serviceId: service.id,
        updates: serviceData,
      });
      setToastVisible(true);
      setToastMessage('Service updated successfully!');
      setToastType('success');
      setTimeout(() => {
        router.back();
      }, 1000);
    } catch (error: any) {
      setToastVisible(true);
      setToastMessage('Failed to update service. Please try again.');
      setToastType('error');
      console.error('Update service error:', error);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar style="dark" />

      {/* Header */}
      <View style={[styles.header, { paddingTop: insets.top }]}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Typography variant="body" style={styles.headerTitle}>
            Edit Service
          </Typography>
        </View>
        <View style={styles.headerAction} />
      </View>

      {/* Content with Keyboard Avoidance */}
      <KeyboardAwareView 
        style={styles.contentContainer}
        contentContainerStyle={styles.scrollContent}
      >
        <ServiceCreationForm
          onSubmit={handleUpdateService}
          onCancel={handleCancel}
          submitButtonText="Update Service"
          cancelButtonText="Cancel"
          initialData={{
            name: service.name,
            description: service.description || '',
            pricing: {
              rate: service.pricing.rate,
              unit: service.pricing.unit,
              currency: service.pricing.currency,
            },
            isActive: service.isActive,
          }}
        />
      </KeyboardAwareView>

      <Toast
        visible={toastVisible}
        message={toastMessage}
        type={toastType}
        onHide={() => setToastVisible(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.cardBackground,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 12,
    backgroundColor: colors.cardBackground,
  },
  backButton: {
    padding: 4,
    marginRight: 16,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  headerAction: {
    padding: 4,
    marginLeft: 16,
    width: 32, // Match back button for symmetry
  },
  contentContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
}); 