import { Button, Input, KeyboardAwareView, Typography } from '@/components/ui';
import { ErrorDialog } from '@/components/ui/ErrorDialog';
import { colors } from '@/constants/Colors';
import { useCreateOrganization } from '@/services/organization/create';
import { useOrganization } from '@/services/organization/organizations';
import { useUpdateOrganization } from '@/services/organization/update';
import { generateNickname, useOrganizationStore } from '@/stores';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import { Image, StyleSheet, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export default function CreateOrganizationScreen() {
  const { id } = useLocalSearchParams<{ id?: string }>();
  const isEdit = !!id;
  
  // API hooks
  const createOrganizationMutation = useCreateOrganization();
  const updateOrganizationMutation = useUpdateOrganization();
  const { organization: existingOrganization } = useOrganization(id || '');
  
  // State
  const [organizationName, setOrganizationName] = useState('');
  const [nickname, setNickname] = useState('');
  const [description, setDescription] = useState('');
  const [logo, setLogo] = useState<string | null>(null);
  const [isNameFieldActive, setIsNameFieldActive] = useState(false);
  const [hasManuallyEditedNickname, setHasManuallyEditedNickname] = useState(false);

  // Error dialog state
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [errorDialogConfig, setErrorDialogConfig] = useState({ title: '', message: '' });

  // Helper function to show error dialog
  const showError = (title: string, message: string) => {
    setErrorDialogConfig({ title, message });
    setShowErrorDialog(true);
  };

  // Load existing organization data for edit mode
  useEffect(() => {
    if (isEdit && existingOrganization) {
      setOrganizationName(existingOrganization.name);
      setNickname(existingOrganization.nickname);
      setDescription(existingOrganization.description || '');
      setLogo(existingOrganization.logo ?? null);
      setHasManuallyEditedNickname(true); // Prevent auto-generation when editing
    }
  }, [isEdit, existingOrganization]);

  // Auto-generate nickname from organization name (only for new organizations or if user hasn't manually edited)
  useEffect(() => {
    if (organizationName && !hasManuallyEditedNickname && !isEdit) {
      const autoNickname = generateNickname(organizationName);
      setNickname(autoNickname);
    }
  }, [organizationName, hasManuallyEditedNickname, isEdit]);

  const handleNicknameChange = (value: string) => {
    setNickname(value.toUpperCase());
    setHasManuallyEditedNickname(true);
  };

  const handleSave = async () => {
    if (!organizationName.trim()) {
      showError('Invalid Input', 'Organization name is required');
      return;
    }

    if (!nickname.trim()) {
      showError('Invalid Input', 'Organization nickname is required');
      return;
    }

    try {
      const organizationData: any = {
        name: organizationName.trim(),
        nickname: nickname.trim(),
        ...(description.trim() && { description: description.trim() }),
        ...(logo && { logo }),
        contact: {
          email: '',
          phone: '',
          website: '',
          address: '',
        },
        settings: {
          currency: 'USD',
          dateFormat: 'MM/DD/YYYY',
          timezone: 'UTC',
          fiscalYearStart: '01/01',
          invoicePrefix: 'INV',
          defaultPaymentTerms: 'Net 30',
        },
        isActive: true,
      };

      if (isEdit && id) {
        await updateOrganizationMutation.mutateAsync({ id, updates: organizationData });
        
        // Update local store
        const addOrganization = useOrganizationStore.getState().updateOrganization;
        addOrganization(id, organizationData);
      } else {
        const newOrganization = await createOrganizationMutation.mutateAsync(organizationData);
        
        // Add to local store
        const addOrganization = useOrganizationStore.getState().addOrganization;
        addOrganization({
          id: newOrganization.id,
          name: newOrganization.name,
          nickname: newOrganization.nickname,
          logo: newOrganization.logo || null,
          description: newOrganization.description,
        });
      }

      router.back();
    } catch (error) {
      console.error('Error saving organization:', error);
      showError(
        'Save Failed', 
        error instanceof Error ? error.message : 'Failed to save organization. Please try again.'
      );
    }
  };

  const insets = useSafeAreaInsets();

  const handleImagePicker = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (status !== 'granted') {
      showError('Permission Required', 'Please grant camera roll permissions to upload an image.');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setLogo(result.assets[0].uri);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  const isFormValid = organizationName.trim() !== '' && nickname.trim() !== '';

  return (
    <View style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar style="dark" />
      
      {/* Header */}
      <View 
        style={[
          styles.header,
          { paddingTop: insets.top + 16 }
        ]}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleCancel}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Typography variant="body" style={styles.headerTitle}>
          {isEdit ? 'Edit Organization' : 'New Organization'}
        </Typography>
        <View style={styles.headerRightPlaceholder} />
      </View>

      {/* Content */}
      <KeyboardAwareView 
        style={styles.scrollContainer}
        contentContainerStyle={{
          ...styles.contentContainer,
          paddingBottom: Math.max(32, insets.bottom + 32)
        }}
      >
        <View style={styles.formContainer}>
          <Typography variant="h3" style={styles.title}>
            {isEdit ? 'Edit Organization Details' : 'Create New Organization'}
          </Typography>
          <Typography variant="body" color="secondary" style={styles.subtitle}>
            {isEdit 
              ? 'Update your organization information' 
              : 'Add a new organization to manage invoices and clients'
            }
          </Typography>
          
          {/* Organization Icon Preview */}
          <View style={styles.iconPreview}>
            <TouchableOpacity style={styles.iconContainer} onPress={handleImagePicker}>
              {logo ? (
                <Image source={{ uri: logo }} style={styles.logoImage} />
              ) : (
                <Typography variant="h2" color="primary" bold>
                  {nickname.slice(0, 2).toUpperCase() || 'ORG'}
                </Typography>
              )}
              <View style={styles.uploadOverlay}>
                <Ionicons name="camera" size={20} color={colors.text.white} />
              </View>
            </TouchableOpacity>
            <Typography variant="bodySmall" color="secondary" style={styles.iconLabel}>
              Tap to upload logo
            </Typography>
          </View>
          
          <Input
            label="Organization Name"
            placeholder="e.g., Game Fusion Studios, AI Styling Limited"
            value={organizationName}
            onChangeText={setOrganizationName}
            onFocus={() => setIsNameFieldActive(true)}
            onBlur={() => setIsNameFieldActive(false)}
            containerStyle={styles.input}
          />
          
          <Input
            label="Short Name/Nickname"
            placeholder="(e.g., GFS, ASL)"
            value={nickname}
            onChangeText={handleNicknameChange}
            containerStyle={styles.input}
            maxLength={4}
            autoCapitalize="characters"
          />
          
          <Input
            label="Description (Optional)"
            placeholder="Brief description of your organization"
            value={description}
            onChangeText={setDescription}
            multiline={true}
            numberOfLines={3}
            containerStyle={styles.input}
          />
          
          <View style={styles.buttons}>
            <Button
              title="Cancel"
              variant="outline"
              onPress={handleCancel}
              style={styles.cancelBtn}
            />
            <Button
              title={isEdit ? 'Update' : 'Create'}
              onPress={handleSave}
              disabled={!isFormValid}
              style={{
                ...styles.submitBtn,
                backgroundColor: !isFormValid ? colors.disabled : colors.primary
              }}
            />
          </View>
        </View>
      </KeyboardAwareView>

      {/* Error Dialog */}
      <ErrorDialog
        visible={showErrorDialog}
        title={errorDialogConfig.title}
        message={errorDialogConfig.message}
        onClose={() => setShowErrorDialog(false)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.cardBackground,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 6,
    backgroundColor: colors.cardBackground,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
  },
  backButton: {
    padding: 4,
    marginLeft: -4,
  },
  headerRightPlaceholder: {
    width: 24 + 8,
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  formContainer: {
    flex: 1,
  },
  title: {
    marginBottom: 8,
  },
  subtitle: {
    marginBottom: 32,
  },
  iconPreview: {
    alignItems: 'center',
    marginBottom: 32,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.avatarBackground.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  iconLabel: {
    textAlign: 'center',
  },
  input: {
    marginBottom: 16,
  },
  buttons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
  },
  cancelBtn: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 12,
  },
  submitBtn: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 12,
  },
  logoImage: {
    width: '100%',
    height: '100%',
    borderRadius: 40,
  },
  uploadOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
}); 