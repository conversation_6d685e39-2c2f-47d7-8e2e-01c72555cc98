import { z } from 'zod';

// Common status types
export const InvoiceStatusSchema = z.enum(['draft', 'pending', 'paid', 'overdue']);
export type InvoiceStatus = z.infer<typeof InvoiceStatusSchema>;

// Currency and formatting (consolidated from settingsStore.ts)
export const CurrencySchema = z.object({
  code: z.string(), // ISO 4217 currency code
  symbol: z.string(), // Currency symbol
  name: z.string(), // Full currency name
  decimalPlaces: z.number().default(2), // Number of decimal places
  symbolPosition: z.enum(['before', 'after']).default('before'), // Symbol position relative to amount
});
export type Currency = z.infer<typeof CurrencySchema>;

// Date helpers
export const DateSchema = z.date();

// ID types
export const EntityIdSchema = z.string();
export type EntityId = z.infer<typeof EntityIdSchema>;

// Unit types for services and items
export const UnitTypeSchema = z.union([
  z.enum(['hour', 'day', 'month', 'project', 'fixed']),
  z.string().min(1) // Allow any custom string
]);
export type UnitType = z.infer<typeof UnitTypeSchema>;

// Discount types
export const DiscountTypeSchema = z.enum(['percentage', 'fixed']);
export type DiscountType = z.infer<typeof DiscountTypeSchema>;

// Base contact schema (consolidated from client and organization contacts)
export const BaseContactSchema = z.object({
  email: z.string().email('Invalid email format').optional(),
  phone: z.string().optional(),
  website: z.string().url('Invalid website URL').optional(),
});
export type BaseContact = z.infer<typeof BaseContactSchema>;

// Base address schema
export const BaseAddressSchema = z.object({
  street: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
  fullAddress: z.string().optional(), // For simple address input
});
export type BaseAddress = z.infer<typeof BaseAddressSchema>;

// Base entity with common fields
export const BaseEntitySchema = z.object({
  id: EntityIdSchema,
  createdAt: DateSchema.optional(),
  updatedAt: DateSchema.optional(),
});
export type BaseEntity = z.infer<typeof BaseEntitySchema>;