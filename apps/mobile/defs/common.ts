import { z } from 'zod';

// Common status types
export const InvoiceStatusSchema = z.enum(['draft', 'pending', 'paid', 'overdue']);
export type InvoiceStatus = z.infer<typeof InvoiceStatusSchema>;

// Currency and formatting
export const CurrencySchema = z.object({
  code: z.string(),
  symbol: z.string(),
  name: z.string(),
});
export type Currency = z.infer<typeof CurrencySchema>;

// Date helpers
export const DateSchema = z.date();

// ID types
export const EntityIdSchema = z.string();
export type EntityId = z.infer<typeof EntityIdSchema>;

// Unit types for services and items
export const UnitTypeSchema = z.union([
  z.enum(['hour', 'day', 'month', 'project', 'fixed']),
  z.string().min(1) // Allow any custom string
]);
export type UnitType = z.infer<typeof UnitTypeSchema>;

// Discount types
export const DiscountTypeSchema = z.enum(['percentage', 'fixed']);
export type DiscountType = z.infer<typeof DiscountTypeSchema>;

// Base entity with common fields
export const BaseEntitySchema = z.object({
  id: EntityIdSchema,
  createdAt: DateSchema.optional(),
  updatedAt: DateSchema.optional(),
});
export type BaseEntity = z.infer<typeof BaseEntitySchema>; 