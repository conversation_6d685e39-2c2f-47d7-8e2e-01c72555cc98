import { z } from 'zod';
import { BaseEntitySchema, EntityIdSchema } from './common';

// Client contact information
export const ClientContactSchema = z.object({
  email: z.string().email('Invalid email format').optional(),
  phone: z.string().optional(),
  website: z.string().url('Invalid website URL').optional(),
});
export type ClientContact = z.infer<typeof ClientContactSchema>;

// Client address
export const ClientAddressSchema = z.object({
  street: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
  fullAddress: z.string().optional(), // For simple address input
});
export type ClientAddress = z.infer<typeof ClientAddressSchema>;

// Client activity types
export const ClientActivityTypeSchema = z.enum([
  'created',
  'updated',
  'contact_updated',
  'address_updated',
  'activated',
  'deactivated',
  'used_in_invoice',
  'note_added',
]);
export type ClientActivityType = z.infer<typeof ClientActivityTypeSchema>;

// Client activity entity
export const ClientActivitySchema = BaseEntitySchema.extend({
  clientId: EntityIdSchema,
  organizationId: EntityIdSchema,
  type: ClientActivityTypeSchema,
  description: z.string(),
  metadata: z.record(z.any()).optional(), // Flexible metadata for different activity types
});
export type ClientActivity = z.infer<typeof ClientActivitySchema>;

// Client entity
export const ClientSchema = BaseEntitySchema.extend({
  name: z.string().min(1, 'Client name is required'),
  displayName: z.string().max(30, 'Display name must be 30 characters or less').optional(),
  company: z.string().optional(),
  contact: ClientContactSchema,
  address: ClientAddressSchema.optional(),
  notes: z.string().optional(),
  photo: z.string().optional(), // URL or base64 string for client photo
  organizationId: EntityIdSchema,
  isActive: z.boolean().default(true),
  // Tax settings specific to this client
  defaultTaxExempt: z.boolean().default(false),
  taxId: z.string().optional(), // Business tax ID
});
export type Client = z.infer<typeof ClientSchema>;

// Client creation input (for forms)
export const CreateClientSchema = ClientSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});
export type CreateClientInput = z.infer<typeof CreateClientSchema>;

// Client update input (partial)
export const UpdateClientSchema = CreateClientSchema.partial();
export type UpdateClientInput = z.infer<typeof UpdateClientSchema>;

// Service DTOs for client operations
export const CreateClientDtoSchema = CreateClientSchema.extend({
  organizationId: EntityIdSchema,
});
export type CreateClientDto = z.infer<typeof CreateClientDtoSchema>;

export const UpdateClientDtoSchema = z.object({
  id: EntityIdSchema,
  organizationId: EntityIdSchema,
  updates: UpdateClientSchema,
});
export type UpdateClientDto = z.infer<typeof UpdateClientDtoSchema>;

export const DeleteClientDtoSchema = z.object({
  id: EntityIdSchema,
  organizationId: EntityIdSchema,
});
export type DeleteClientDto = z.infer<typeof DeleteClientDtoSchema>; 