import { z } from 'zod';

export const UserProfileSchema = z.object({
  id: z.string(),
  firstName: z.string().min(1, 'First name required'),
  lastName: z.string().min(1, 'Last name required'),
  email: z.string().email('Valid email required'),
  phone: z.string().optional(),
  avatar: z.string().url().optional(),
  timezone: z.string().default('UTC'),
  dateFormat: z.enum(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD']).default('MM/DD/YYYY'),
  language: z.string().default('en'),
  notifications: z.object({
    email: z.boolean().default(true),
    push: z.boolean().default(true),
    marketing: z.boolean().default(false),
    invoiceReminders: z.boolean().default(true),
    paymentUpdates: z.boolean().default(true),
  }),
  preferences: z.object({
    currency: z.string().default('USD'),
    theme: z.enum(['light', 'dark', 'system']).default('system'),
    compactMode: z.boolean().default(false),
  }),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type UserProfile = z.infer<typeof UserProfileSchema>;

export const CreateUserProfileSchema = UserProfileSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const UpdateUserProfileSchema = UserProfileSchema.partial().omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export type CreateUserProfileInput = z.infer<typeof CreateUserProfileSchema>;
export type UpdateUserProfileInput = z.infer<typeof UpdateUserProfileSchema>;

// Avatar upload schema
export const AvatarUploadSchema = z.object({
  uri: z.string(),
  type: z.string(),
  name: z.string(),
  size: z.number(),
});

export type AvatarUpload = z.infer<typeof AvatarUploadSchema>;

// Security settings schema
export const SecuritySettingsSchema = z.object({
  id: z.string(),
  userId: z.string(),
  twoFactorEnabled: z.boolean().default(false),
  loginNotifications: z.boolean().default(true),
  sessionTimeout: z.number().default(30), // minutes
  lastPasswordChange: z.date(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type SecuritySettings = z.infer<typeof SecuritySettingsSchema>;

export const UpdateSecuritySettingsSchema = SecuritySettingsSchema.partial().omit({
  id: true,
  userId: true,
  createdAt: true,
  updatedAt: true,
});

export type UpdateSecuritySettingsInput = z.infer<typeof UpdateSecuritySettingsSchema>; 