import { z } from 'zod';
import { BaseEntitySchema } from './common';

// Payment gateway configuration status
export const PaymentGatewayStatus = z.enum(['not_configured', 'configured', 'active']);
export type PaymentGatewayStatus = z.infer<typeof PaymentGatewayStatus>;

// Payment gateway configuration
export const PaymentGatewayConfigSchema = z.object({
  id: z.string(),
  type: z.enum(['stripe', 'paypal']),
  status: PaymentGatewayStatus,
  accountId: z.string().optional(), // Stripe Connect account ID or PayPal merchant ID
  accountEmail: z.string().email().optional(),
  displayName: z.string().optional(), // e.g., "John's Business Account"
  configuredAt: z.date().optional(),
  lastUsed: z.date().optional(),
});
export type PaymentGatewayConfig = z.infer<typeof PaymentGatewayConfigSchema>;

// Custom payment instruction
export const CustomPaymentInstructionSchema = BaseEntitySchema.extend({
  organizationId: z.string(),
  title: z.string().min(1, 'Payment method name is required'),
  instructions: z.string().min(1, 'Payment instructions are required'),
  isActive: z.boolean().default(true),
  sortOrder: z.number().default(0),
});
export type CustomPaymentInstruction = z.infer<typeof CustomPaymentInstructionSchema>;

// Organization payment methods configuration
export const OrganizationPaymentMethodsSchema = z.object({
  organizationId: z.string(),
  // Only one gateway can be active at a time
  activeGateway: z.string().optional(), // ID of the active gateway config
  paymentGateways: z.array(PaymentGatewayConfigSchema).default([]),
  customInstructions: z.array(z.string()).default([]), // Array of CustomPaymentInstruction IDs
  allowPartialPayments: z.boolean().default(true),
  // Auto-create payment links when invoice is sent
  autoCreatePaymentLinks: z.boolean().default(true),
  updatedAt: z.date(),
});
export type OrganizationPaymentMethods = z.infer<typeof OrganizationPaymentMethodsSchema>;

// Create custom payment instruction input
export const CreateCustomPaymentInstructionSchema = CustomPaymentInstructionSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});
export type CreateCustomPaymentInstructionInput = z.infer<typeof CreateCustomPaymentInstructionSchema>;

// Update custom payment instruction input
export const UpdateCustomPaymentInstructionSchema = CreateCustomPaymentInstructionSchema.partial().extend({
  id: z.string(),
});
export type UpdateCustomPaymentInstructionInput = z.infer<typeof UpdateCustomPaymentInstructionSchema>;

// Update organization payment methods input
export const UpdateOrganizationPaymentMethodsSchema = z.object({
  organizationId: z.string(),
  activeGateway: z.string().optional(),
  paymentGateways: z.array(PaymentGatewayConfigSchema).optional(),
  customInstructions: z.array(z.string()).optional(),
  allowPartialPayments: z.boolean().optional(),
  autoCreatePaymentLinks: z.boolean().optional(),
});
export type UpdateOrganizationPaymentMethodsInput = z.infer<typeof UpdateOrganizationPaymentMethodsSchema>;

// Gateway connection inputs
export const ConnectStripeAccountSchema = z.object({
  organizationId: z.string(),
  accountId: z.string(),
  accountEmail: z.string().email(),
  displayName: z.string(),
});
export type ConnectStripeAccountInput = z.infer<typeof ConnectStripeAccountSchema>;

export const ConnectPayPalAccountSchema = z.object({
  organizationId: z.string(),
  merchantId: z.string(),
  accountEmail: z.string().email(),
  displayName: z.string(),
});
export type ConnectPayPalAccountInput = z.infer<typeof ConnectPayPalAccountSchema>; 