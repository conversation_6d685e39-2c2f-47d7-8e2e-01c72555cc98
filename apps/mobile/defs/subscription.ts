import { z } from 'zod';

export const SubscriptionPlanSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  price: z.number(),
  currency: z.string(),
  interval: z.enum(['month', 'year']),
  features: z.array(z.string()),
  limits: z.object({
    organizations: z.number(),
    invoices: z.number(), // -1 for unlimited
    clients: z.number(),
    storage: z.number(), // in MB
    templates: z.number(),
    customFields: z.number(),
  }),
  isPopular: z.boolean().default(false),
  isActive: z.boolean().default(true),
  trialDays: z.number().default(0),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type SubscriptionPlan = z.infer<typeof SubscriptionPlanSchema>;

export const SubscriptionSchema = z.object({
  id: z.string(),
  userId: z.string(),
  planId: z.string(),
  status: z.enum(['active', 'past_due', 'canceled', 'trialing', 'incomplete']),
  currentPeriodStart: z.date(),
  currentPeriodEnd: z.date(),
  trialStart: z.date().optional(),
  trialEnd: z.date().optional(),
  cancelAtPeriodEnd: z.boolean().default(false),
  canceledAt: z.date().optional(),
  metadata: z.record(z.string()).optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type Subscription = z.infer<typeof SubscriptionSchema>;

export const BillingHistorySchema = z.object({
  id: z.string(),
  subscriptionId: z.string(),
  userId: z.string(),
  amount: z.number(),
  currency: z.string(),
  status: z.enum(['paid', 'pending', 'failed', 'refunded']),
  invoiceUrl: z.string().url().optional(),
  description: z.string(),
  periodStart: z.date(),
  periodEnd: z.date(),
  paymentMethod: z.object({
    type: z.enum(['card', 'bank', 'paypal']),
    last4: z.string().optional(),
    brand: z.string().optional(),
  }),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type BillingHistory = z.infer<typeof BillingHistorySchema>;

export const PaymentMethodSchema = z.object({
  id: z.string(),
  userId: z.string(),
  type: z.enum(['card', 'bank', 'paypal']),
  isDefault: z.boolean().default(false),
  card: z.object({
    brand: z.string(),
    last4: z.string(),
    expMonth: z.number(),
    expYear: z.number(),
    country: z.string().optional(),
  }).optional(),
  bank: z.object({
    accountType: z.enum(['checking', 'savings']),
    last4: z.string(),
    routingNumber: z.string(),
    bankName: z.string().optional(),
  }).optional(),
  paypal: z.object({
    email: z.string().email(),
  }).optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type PaymentMethod = z.infer<typeof PaymentMethodSchema>;

// Subscription change request
export const SubscriptionChangeSchema = z.object({
  newPlanId: z.string(),
  prorationBehavior: z.enum(['always_invoice', 'none', 'create_prorations']).default('create_prorations'),
  effectiveDate: z.enum(['immediately', 'next_period']).default('immediately'),
});

export type SubscriptionChangeRequest = z.infer<typeof SubscriptionChangeSchema>;

// Usage tracking
export const UsageStatsSchema = z.object({
  id: z.string(),
  userId: z.string(),
  period: z.object({
    start: z.date(),
    end: z.date(),
  }),
  usage: z.object({
    organizations: z.number(),
    invoices: z.number(),
    clients: z.number(),
    storage: z.number(), // in MB
    apiCalls: z.number(),
  }),
  limits: z.object({
    organizations: z.number(),
    invoices: z.number(),
    clients: z.number(),
    storage: z.number(),
    apiCalls: z.number(),
  }),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type UsageStats = z.infer<typeof UsageStatsSchema>; 