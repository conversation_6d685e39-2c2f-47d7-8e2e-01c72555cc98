import { z } from 'zod';
import { BaseEntitySchema, EntityIdSchema, UnitTypeSchema } from './common';

// Service pricing
export const ServicePricingSchema = z.object({
  rate: z.number().min(0, 'Rate must be positive'),
  unit: UnitTypeSchema,
  customUnit: z.string().optional(),
  currency: z.string().default('USD'),
  minimumQuantity: z.number().min(0).optional(),
  maximumQuantity: z.number().min(0).optional(),
});
export type ServicePricing = z.infer<typeof ServicePricingSchema>;

// Service entity
export const ServiceSchema = BaseEntitySchema.extend({
  name: z.string().min(1, 'Service name is required'),
  description: z.string().optional(),
  pricing: ServicePricingSchema,
  organizationId: EntityIdSchema,
  category: z.string().optional(),
  isActive: z.boolean().default(true),
  // Tax settings for this service
  taxable: z.boolean().default(true),
  taxRate: z.number().min(0).max(100).optional(), // Override default tax rate
  tags: z.array(z.string()).default([]),
});
export type Service = z.infer<typeof ServiceSchema>;

// Service creation input (for forms)
export const CreateServiceSchema = ServiceSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});
export type CreateServiceInput = z.infer<typeof CreateServiceSchema>;

// Service update input (partial)
export const UpdateServiceSchema = CreateServiceSchema.partial();
export type UpdateServiceInput = z.infer<typeof UpdateServiceSchema>;

// Simplified service (for backward compatibility)
export const SimpleServiceSchema = z.object({
  id: EntityIdSchema,
  name: z.string(),
  description: z.string().optional(),
  rate: z.number(),
  unit: UnitTypeSchema,
});
export type SimpleService = z.infer<typeof SimpleServiceSchema>;

// Service DTOs for service operations
export const CreateServiceDtoSchema = CreateServiceSchema.extend({
  organizationId: EntityIdSchema,
});
export type CreateServiceDto = z.infer<typeof CreateServiceDtoSchema>;

export const UpdateServiceDtoSchema = z.object({
  id: EntityIdSchema,
  organizationId: EntityIdSchema,
  updates: UpdateServiceSchema,
});
export type UpdateServiceDto = z.infer<typeof UpdateServiceDtoSchema>;

export const DeleteServiceDtoSchema = z.object({
  id: EntityIdSchema,
  organizationId: EntityIdSchema,
});
export type DeleteServiceDto = z.infer<typeof DeleteServiceDtoSchema>; 

// Service Activity
export const ServiceActivitySchema = z.object({
  id: EntityIdSchema,
  serviceId: EntityIdSchema,
  organizationId: EntityIdSchema,
  type: z.enum(['created', 'updated', 'used_in_invoice', 'activated', 'deactivated', 'pricing_changed']),
  description: z.string(),
  metadata: z.object({
    invoiceId: EntityIdSchema.optional(),
    invoiceNumber: z.string().optional(),
    clientName: z.string().optional(),
    oldValue: z.any().optional(),
    newValue: z.any().optional(),
    field: z.string().optional(),
    amount: z.number().optional(),
  }).optional(),
  createdAt: z.date(),
});
export type ServiceActivity = z.infer<typeof ServiceActivitySchema>; 