import { z } from 'zod';
import { BaseEntitySchema, EntityIdSchema } from './common';

// Tax calculation methods
export const TaxMethodSchema = z.enum(['none', 'on_total', 'per_item', 'as_deduction']);
export type TaxMethod = z.infer<typeof TaxMethodSchema>;

// Tax option (predefined tax types)
export const TaxOptionSchema = BaseEntitySchema.extend({
  name: z.string().min(1, 'Tax name is required'),
  rate: z.number().min(0).max(100, 'Tax rate must be between 0 and 100'),
  description: z.string().optional(),
  region: z.string().optional(),
});
export type TaxOption = z.infer<typeof TaxOptionSchema>;

// Tax configuration for invoices
export const TaxConfigurationSchema = z.object({
  method: TaxMethodSchema,
  taxId: EntityIdSchema.optional(), // Reference to TaxOption
  inclusive: z.boolean().default(false),
  customRate: z.number().min(0).max(100).optional(), // For custom tax rates
});
export type TaxConfiguration = z.infer<typeof TaxConfigurationSchema>;

// Tax information saved with invoice
export const InvoiceTaxInfoSchema = z.object({
  taxName: z.string(),
  defaultRate: z.number(),
  inclusive: z.boolean(),
  enabled: z.boolean(),
  method: TaxMethodSchema.optional(),
});
export type InvoiceTaxInfo = z.infer<typeof InvoiceTaxInfoSchema>;

// Tax calculation result
export const TaxCalculationSchema = z.object({
  subtotal: z.number(),
  taxAmount: z.number(),
  total: z.number(),
  breakdown: z.array(z.object({
    description: z.string(),
    amount: z.number(),
    rate: z.number(),
  })).optional(),
});
export type TaxCalculation = z.infer<typeof TaxCalculationSchema>; 