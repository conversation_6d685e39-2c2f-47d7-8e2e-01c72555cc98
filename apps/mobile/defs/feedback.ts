import { z } from 'zod';

export const FeedbackCategorySchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  icon: z.string(),
  color: z.string(),
  sortOrder: z.number(),
  isActive: z.boolean().default(true),
});

export type FeedbackCategory = z.infer<typeof FeedbackCategorySchema>;

export const FeedbackAttachmentSchema = z.object({
  id: z.string(),
  name: z.string(),
  size: z.number(),
  type: z.string(),
  uri: z.string().url(),
  uploadedAt: z.date(),
});

export type FeedbackAttachment = z.infer<typeof FeedbackAttachmentSchema>;

export const FeedbackSchema = z.object({
  id: z.string(),
  userId: z.string(),
  category: z.enum(['bug', 'feature', 'improvement', 'general', 'billing', 'performance']),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(20, 'Message must be at least 20 characters'),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  status: z.enum(['open', 'in_progress', 'resolved', 'closed', 'duplicate']).default('open'),
  tags: z.array(z.string()).default([]),
  deviceInfo: z.object({
    platform: z.string(),
    version: z.string(),
    model: z.string().optional(),
    osVersion: z.string().optional(),
  }).optional(),
  appInfo: z.object({
    version: z.string(),
    buildNumber: z.string(),
    environment: z.enum(['development', 'staging', 'production']),
  }).optional(),
  attachments: z.array(FeedbackAttachmentSchema).default([]),
  adminNotes: z.string().optional(),
  adminResponse: z.string().optional(),
  responseDate: z.date().optional(),
  resolvedDate: z.date().optional(),
  followUpRequired: z.boolean().default(false),
  customerSatisfaction: z.number().min(1).max(5).optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type Feedback = z.infer<typeof FeedbackSchema>;

export const CreateFeedbackSchema = FeedbackSchema.omit({
  id: true,
  status: true,
  adminNotes: true,
  adminResponse: true,
  responseDate: true,
  resolvedDate: true,
  followUpRequired: true,
  customerSatisfaction: true,
  createdAt: true,
  updatedAt: true,
});

export const UpdateFeedbackSchema = FeedbackSchema.partial().omit({
  id: true,
  userId: true,
  createdAt: true,
  updatedAt: true,
});

export type CreateFeedbackInput = z.infer<typeof CreateFeedbackSchema>;
export type UpdateFeedbackInput = z.infer<typeof UpdateFeedbackSchema>;

// Feedback response from user
export const FeedbackResponseSchema = z.object({
  id: z.string(),
  feedbackId: z.string(),
  userId: z.string(),
  message: z.string(),
  isFromAdmin: z.boolean().default(false),
  attachments: z.array(FeedbackAttachmentSchema).default([]),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type FeedbackResponse = z.infer<typeof FeedbackResponseSchema>;

// Rating and review
export const AppReviewSchema = z.object({
  id: z.string(),
  userId: z.string(),
  rating: z.number().min(1).max(5),
  review: z.string().optional(),
  version: z.string(),
  platform: z.enum(['ios', 'android', 'web']),
  isPublic: z.boolean().default(true),
  isPromoted: z.boolean().default(false),
  adminResponse: z.string().optional(),
  responseDate: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type AppReview = z.infer<typeof AppReviewSchema>;

// Feature request
export const FeatureRequestSchema = z.object({
  id: z.string(),
  userId: z.string(),
  title: z.string().min(5, 'Title must be at least 5 characters'),
  description: z.string().min(20, 'Description must be at least 20 characters'),
  category: z.string(),
  priority: z.enum(['low', 'medium', 'high']).default('medium'),
  status: z.enum(['submitted', 'under_review', 'planned', 'in_progress', 'completed', 'declined']).default('submitted'),
  votes: z.number().default(0),
  implementationComplexity: z.enum(['low', 'medium', 'high']).optional(),
  estimatedReleaseVersion: z.string().optional(),
  adminNotes: z.string().optional(),
  businessJustification: z.string().optional(),
  targetAudience: z.array(z.string()).default([]),
  attachments: z.array(FeedbackAttachmentSchema).default([]),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type FeatureRequest = z.infer<typeof FeatureRequestSchema>;

// Bug report
export const BugReportSchema = z.object({
  id: z.string(),
  userId: z.string(),
  title: z.string().min(5, 'Title must be at least 5 characters'),
  description: z.string().min(20, 'Description must be at least 20 characters'),
  stepsToReproduce: z.array(z.string()).default([]),
  expectedBehavior: z.string().optional(),
  actualBehavior: z.string().optional(),
  severity: z.enum(['low', 'medium', 'high', 'critical']).default('medium'),
  status: z.enum(['open', 'in_progress', 'resolved', 'closed', 'duplicate', 'wont_fix']).default('open'),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  assignedTo: z.string().optional(),
  tags: z.array(z.string()).default([]),
  deviceInfo: z.object({
    platform: z.string(),
    version: z.string(),
    model: z.string().optional(),
    osVersion: z.string().optional(),
    screenResolution: z.string().optional(),
    browserInfo: z.string().optional(),
  }),
  appInfo: z.object({
    version: z.string(),
    buildNumber: z.string(),
    environment: z.enum(['development', 'staging', 'production']),
  }),
  reproductionRate: z.enum(['always', 'sometimes', 'rarely', 'once']).default('sometimes'),
  workaround: z.string().optional(),
  attachments: z.array(FeedbackAttachmentSchema).default([]),
  duplicateOf: z.string().optional(),
  relatedBugs: z.array(z.string()).default([]),
  fixedInVersion: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type BugReport = z.infer<typeof BugReportSchema>; 