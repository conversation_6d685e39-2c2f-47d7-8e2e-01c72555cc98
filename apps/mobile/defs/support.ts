import { z } from 'zod';

export const HelpCategorySchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  icon: z.string(),
  color: z.string(),
  sortOrder: z.number(),
  parentId: z.string().optional(),
  isActive: z.boolean().default(true),
});

export type HelpCategory = z.infer<typeof HelpCategorySchema>;

export const HelpArticleSchema = z.object({
  id: z.string(),
  title: z.string(),
  slug: z.string(),
  content: z.string(),
  excerpt: z.string().optional(),
  categoryId: z.string(),
  tags: z.array(z.string()).default([]),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).default('beginner'),
  estimatedReadTime: z.number().optional(), // in minutes
  views: z.number().default(0),
  helpful: z.number().default(0),
  notHelpful: z.number().default(0),
  featured: z.boolean().default(false),
  searchKeywords: z.array(z.string()).default([]),
  relatedArticles: z.array(z.string()).default([]),
  lastReviewed: z.date().optional(),
  reviewedBy: z.string().optional(),
  version: z.string().default('1.0.0'),
  isPublished: z.boolean().default(true),
  publishedAt: z.date().optional(),
  authorId: z.string(),
  updatedBy: z.string().optional(),
  attachments: z.array(z.object({
    id: z.string(),
    name: z.string(),
    type: z.string(),
    size: z.number(),
    url: z.string().url(),
    description: z.string().optional(),
  })).default([]),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type HelpArticle = z.infer<typeof HelpArticleSchema>;

export const HelpArticleRatingSchema = z.object({
  id: z.string(),
  articleId: z.string(),
  userId: z.string(),
  isHelpful: z.boolean(),
  feedback: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type HelpArticleRating = z.infer<typeof HelpArticleRatingSchema>;

export const FAQSchema = z.object({
  id: z.string(),
  question: z.string(),
  answer: z.string(),
  categoryId: z.string(),
  priority: z.number().default(0),
  tags: z.array(z.string()).default([]),
  views: z.number().default(0),
  helpful: z.number().default(0),
  notHelpful: z.number().default(0),
  isPublished: z.boolean().default(true),
  searchKeywords: z.array(z.string()).default([]),
  relatedFAQs: z.array(z.string()).default([]),
  relatedArticles: z.array(z.string()).default([]),
  lastUpdated: z.date(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type FAQ = z.infer<typeof FAQSchema>;

export const SupportTicketSchema = z.object({
  id: z.string(),
  userId: z.string(),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  description: z.string().min(20, 'Description must be at least 20 characters'),
  category: z.enum(['technical', 'billing', 'feature_request', 'bug_report', 'general']),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  status: z.enum(['open', 'in_progress', 'waiting_for_customer', 'resolved', 'closed']).default('open'),
  assignedTo: z.string().optional(),
  tags: z.array(z.string()).default([]),
  customFields: z.record(z.string()).optional(),
  internalNotes: z.string().optional(),
  customerSatisfactionRating: z.number().min(1).max(5).optional(),
  customerFeedback: z.string().optional(),
  resolutionTime: z.number().optional(), // in minutes
  firstResponseTime: z.number().optional(), // in minutes
  escalatedAt: z.date().optional(),
  resolvedAt: z.date().optional(),
  closedAt: z.date().optional(),
  attachments: z.array(z.object({
    id: z.string(),
    name: z.string(),
    type: z.string(),
    size: z.number(),
    url: z.string().url(),
    uploadedBy: z.string(),
    uploadedAt: z.date(),
  })).default([]),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type SupportTicket = z.infer<typeof SupportTicketSchema>;

export const SupportTicketMessageSchema = z.object({
  id: z.string(),
  ticketId: z.string(),
  senderId: z.string(),
  senderName: z.string(),
  senderType: z.enum(['customer', 'agent', 'system']),
  message: z.string(),
  isInternal: z.boolean().default(false),
  attachments: z.array(z.object({
    id: z.string(),
    name: z.string(),
    type: z.string(),
    size: z.number(),
    url: z.string().url(),
  })).default([]),
  readAt: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type SupportTicketMessage = z.infer<typeof SupportTicketMessageSchema>;

export const ContactFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Valid email required'),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(20, 'Message must be at least 20 characters'),
  category: z.enum(['sales', 'support', 'billing', 'partnership', 'media', 'other']).default('support'),
  company: z.string().optional(),
  phone: z.string().optional(),
  urgency: z.enum(['low', 'medium', 'high']).default('medium'),
  preferredContactMethod: z.enum(['email', 'phone', 'no_preference']).default('email'),
  subscribeToNewsletter: z.boolean().default(false),
  gdprConsent: z.boolean(),
  captchaToken: z.string().optional(),
});

export type ContactFormInput = z.infer<typeof ContactFormSchema>;

export const ContactSubmissionSchema = ContactFormSchema.extend({
  id: z.string(),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
  referrer: z.string().optional(),
  processed: z.boolean().default(false),
  processedAt: z.date().optional(),
  assignedTo: z.string().optional(),
  status: z.enum(['new', 'contacted', 'resolved', 'spam']).default('new'),
  internalNotes: z.string().optional(),
  followUpDate: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type ContactSubmission = z.infer<typeof ContactSubmissionSchema>;

export const KnowledgeBaseSearchSchema = z.object({
  query: z.string().min(1, 'Search query required'),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  limit: z.number().min(1).max(50).default(10),
  offset: z.number().min(0).default(0),
  sortBy: z.enum(['relevance', 'date', 'views', 'rating']).default('relevance'),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
});

export type KnowledgeBaseSearchInput = z.infer<typeof KnowledgeBaseSearchSchema>;

export const SearchResultSchema = z.object({
  id: z.string(),
  title: z.string(),
  excerpt: z.string(),
  url: z.string(),
  type: z.enum(['article', 'faq', 'tutorial', 'video']),
  category: z.string(),
  relevanceScore: z.number(),
  lastUpdated: z.date(),
  tags: z.array(z.string()),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']),
});

export type SearchResult = z.infer<typeof SearchResultSchema>;

export const UserGuideSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  steps: z.array(z.object({
    id: z.string(),
    title: z.string(),
    description: z.string(),
    image: z.string().url().optional(),
    video: z.string().url().optional(),
    code: z.string().optional(),
    tips: z.array(z.string()).default([]),
    warnings: z.array(z.string()).default([]),
  })),
  category: z.string(),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']),
  estimatedTime: z.number(), // in minutes
  prerequisites: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
  isInteractive: z.boolean().default(false),
  completionRate: z.number().default(0),
  rating: z.number().default(0),
  totalRatings: z.number().default(0),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type UserGuide = z.infer<typeof UserGuideSchema>; 