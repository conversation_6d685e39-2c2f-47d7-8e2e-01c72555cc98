import { z } from 'zod';

export const ColorSchemeSchema = z.object({
  primary: z.string(),
  secondary: z.string(),
  accent: z.string(),
  text: z.string(),
  background: z.string(),
  border: z.string().optional(),
  success: z.string().optional(),
  warning: z.string().optional(),
  error: z.string().optional(),
});

export type ColorScheme = z.infer<typeof ColorSchemeSchema>;

export const LayoutSettingsSchema = z.object({
  headerStyle: z.enum(['minimal', 'classic', 'modern', 'bold']).default('modern'),
  fontFamily: z.enum(['Inter', 'Roboto', 'Open Sans', 'Lato', 'Poppins']).default('Inter'),
  fontSize: z.enum(['small', 'medium', 'large']).default('medium'),
  logoPosition: z.enum(['left', 'center', 'right']).default('left'),
  logoSize: z.enum(['small', 'medium', 'large']).default('medium'),
  showCompanyDetails: z.boolean().default(true),
  showClientDetails: z.boolean().default(true),
  itemTableStyle: z.enum(['minimal', 'bordered', 'striped', 'modern']).default('modern'),
  showItemNumbers: z.boolean().default(false),
  showTaxColumn: z.boolean().default(true),
  showDiscountColumn: z.boolean().default(false),
  compactLayout: z.boolean().default(false),
});

export type LayoutSettings = z.infer<typeof LayoutSettingsSchema>;

export const CustomizationSchema = z.object({
  logoUrl: z.string().url().optional(),
  logoWidth: z.number().min(50).max(300).optional(),
  logoHeight: z.number().min(50).max(200).optional(),
  headerImage: z.string().url().optional(),
  footerText: z.string().optional(),
  thanksMessage: z.string().optional(),
  notesPlaceholder: z.string().optional(),
  termsPlaceholder: z.string().optional(),
  showPaymentInstructions: z.boolean().default(true),
  showSignature: z.boolean().default(false),
  signatureImage: z.string().url().optional(),
  watermark: z.object({
    enabled: z.boolean().default(false),
    text: z.string().optional(),
    opacity: z.number().min(0.1).max(0.5).default(0.1),
  }),
  customCss: z.string().optional(),
});

export type Customization = z.infer<typeof CustomizationSchema>;

export const InvoiceTemplateSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  thumbnail: z.string().url(),
  category: z.enum(['business', 'creative', 'professional', 'minimal', 'modern']).default('business'),
  isPremium: z.boolean().default(false),
  isBuiltIn: z.boolean().default(true),
  colorScheme: ColorSchemeSchema,
  layout: LayoutSettingsSchema,
  customization: CustomizationSchema,
  organizationId: z.string().optional(), // null for built-in templates
  isDefault: z.boolean().default(false),
  isActive: z.boolean().default(true),
  usage: z.object({
    count: z.number().default(0),
    lastUsed: z.date().optional(),
  }),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type InvoiceTemplate = z.infer<typeof InvoiceTemplateSchema>;

export const CreateInvoiceTemplateSchema = InvoiceTemplateSchema.omit({
  id: true,
  usage: true,
  createdAt: true,
  updatedAt: true,
});

export const UpdateInvoiceTemplateSchema = InvoiceTemplateSchema.partial().omit({
  id: true,
  organizationId: true,
  createdAt: true,
  updatedAt: true,
});

export type CreateInvoiceTemplateInput = z.infer<typeof CreateInvoiceTemplateSchema>;
export type UpdateInvoiceTemplateInput = z.infer<typeof UpdateInvoiceTemplateSchema>;

// Template categories for filtering
export const TemplateCategorySchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  icon: z.string(),
  sortOrder: z.number(),
});

export type TemplateCategory = z.infer<typeof TemplateCategorySchema>;

// Color palette presets
export const ColorPaletteSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  colors: ColorSchemeSchema,
  category: z.string(),
  isPopular: z.boolean().default(false),
});

export type ColorPalette = z.infer<typeof ColorPaletteSchema>;

// Font pairing suggestions
export const FontPairingSchema = z.object({
  id: z.string(),
  name: z.string(),
  headingFont: z.string(),
  bodyFont: z.string(),
  description: z.string(),
  category: z.enum(['modern', 'classic', 'creative', 'minimal']),
});

export type FontPairing = z.infer<typeof FontPairingSchema>; 