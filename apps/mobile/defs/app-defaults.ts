import { z } from 'zod';

export const CurrencySettingsSchema = z.object({
  code: z.string().length(3),
  symbol: z.string(),
  position: z.enum(['before', 'after']).default('before'),
  decimalPlaces: z.number().min(0).max(4).default(2),
  thousandsSeparator: z.enum([',', '.', ' ', "'"]).default(','),
  decimalSeparator: z.enum(['.', ',']).default('.'),
});

export type CurrencySettings = z.infer<typeof CurrencySettingsSchema>;

export const TaxSettingsSchema = z.object({
  defaultRate: z.number().min(0).max(100).default(0),
  taxName: z.string().default('Tax'),
  inclusive: z.boolean().default(false),
  enabled: z.boolean().default(true),
  compoundTax: z.boolean().default(false),
  taxOnShipping: z.boolean().default(false),
  exemptDescription: z.string().default('Tax Exempt'),
  regions: z.array(z.object({
    id: z.string(),
    name: z.string(),
    rate: z.number(),
    isDefault: z.boolean().default(false),
  })).default([]),
});

export type TaxSettings = z.infer<typeof TaxSettingsSchema>;

export const PaymentSettingsSchema = z.object({
  defaultTerms: z.enum(['Due on receipt', 'Net 15', 'Net 30', 'Net 45', 'Net 60', 'Net 90']).default('Net 30'),
  lateFeeEnabled: z.boolean().default(false),
  lateFeeAmount: z.number().min(0).default(0),
  lateFeeType: z.enum(['fixed', 'percentage']).default('fixed'),
  lateFeeGracePeriod: z.number().min(0).default(0), // days
  discountEnabled: z.boolean().default(false),
  earlyPaymentDiscount: z.number().min(0).max(100).default(0),
  discountDays: z.number().min(0).default(10),
  allowPartialPayments: z.boolean().default(true),
  minimumPaymentAmount: z.number().min(0).default(0),
});

export type PaymentSettings = z.infer<typeof PaymentSettingsSchema>;

export const NumberingSettingsSchema = z.object({
  prefix: z.string().default('INV'),
  suffix: z.string().default(''),
  format: z.enum(['sequential', 'date-based', 'custom']).default('sequential'),
  startNumber: z.number().min(1).default(1),
  padZeros: z.number().min(0).max(10).default(3),
  resetAnnually: z.boolean().default(false),
  customFormat: z.string().optional(), // e.g., "INV-{YYYY}-{####}"
  separators: z.object({
    beforeNumber: z.string().default('-'),
    afterNumber: z.string().default(''),
    dateFormat: z.enum(['YYYY', 'YY', 'YYYYMM', 'YYMMDD']).default('YYYY'),
  }),
});

export type NumberingSettings = z.infer<typeof NumberingSettingsSchema>;

export const NotificationSettingsSchema = z.object({
  sendReminders: z.boolean().default(true),
  reminderDays: z.array(z.number()).default([7, 3, 1]),
  reminderFrequency: z.enum(['once', 'weekly', 'daily']).default('once'),
  autoMarkOverdue: z.boolean().default(true),
  overdueDays: z.number().min(1).default(30),
  escalationEnabled: z.boolean().default(false),
  escalationDays: z.number().min(1).default(60),
  ccOnReminders: z.array(z.string().email()).default([]),
  customReminderText: z.string().optional(),
  sendConfirmations: z.boolean().default(true),
  sendReceiptNotifications: z.boolean().default(true),
});

export type NotificationSettings = z.infer<typeof NotificationSettingsSchema>;

export const BrandingSettingsSchema = z.object({
  companyLogo: z.string().url().optional(),
  logoWidth: z.number().min(50).max(300).default(150),
  logoHeight: z.number().min(50).max(200).default(75),
  websiteUrl: z.string().url().optional(),
  primaryColor: z.string().default('#2563eb'),
  secondaryColor: z.string().default('#64748b'),
  socialLinks: z.object({
    linkedin: z.string().url().optional(),
    twitter: z.string().url().optional(),
    facebook: z.string().url().optional(),
    instagram: z.string().url().optional(),
    website: z.string().url().optional(),
  }),
  customFooter: z.string().optional(),
  showPoweredBy: z.boolean().default(true),
});

export type BrandingSettings = z.infer<typeof BrandingSettingsSchema>;

export const AppDefaultsSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  currency: CurrencySettingsSchema,
  taxes: TaxSettingsSchema,
  payment: PaymentSettingsSchema,
  numbering: NumberingSettingsSchema,
  notifications: NotificationSettingsSchema,
  branding: BrandingSettingsSchema,
  preferences: z.object({
    defaultDueDays: z.number().min(0).default(30),
    showItemCodes: z.boolean().default(false),
    showLineNumbers: z.boolean().default(false),
    defaultItemTaxable: z.boolean().default(true),
    showClientNotes: z.boolean().default(true),
    showInternalNotes: z.boolean().default(false),
    autoCalculateDiscount: z.boolean().default(true),
    roundTotals: z.boolean().default(true),
    language: z.string().default('en'),
    dateFormat: z.enum(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD']).default('MM/DD/YYYY'),
    timeFormat: z.enum(['12', '24']).default('12'),
    timezone: z.string().default('UTC'),
  }),
  integrations: z.object({
    quickbooksEnabled: z.boolean().default(false),
    quickbooksSettings: z.record(z.string()).optional(),
    xeroEnabled: z.boolean().default(false),
    xeroSettings: z.record(z.string()).optional(),
    zapierWebhooks: z.array(z.string()).default([]),
    customWebhooks: z.array(z.object({
      id: z.string(),
      url: z.string().url(),
      events: z.array(z.string()),
      isActive: z.boolean().default(true),
    })).default([]),
  }),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type AppDefaults = z.infer<typeof AppDefaultsSchema>;

export const UpdateAppDefaultsSchema = AppDefaultsSchema.partial().omit({
  id: true,
  organizationId: true,
  createdAt: true,
  updatedAt: true,
});

export type UpdateAppDefaultsInput = z.infer<typeof UpdateAppDefaultsSchema>;

// Currency list for dropdown
export const SupportedCurrencySchema = z.object({
  code: z.string(),
  name: z.string(),
  symbol: z.string(),
  country: z.string(),
  isPopular: z.boolean().default(false),
});

export type SupportedCurrency = z.infer<typeof SupportedCurrencySchema>;

// Template defaults for different business types
export const BusinessTypeTemplateSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  category: z.enum(['consulting', 'retail', 'service', 'freelance', 'manufacturing']),
  defaults: AppDefaultsSchema.omit({ id: true, organizationId: true, createdAt: true, updatedAt: true }),
});

export type BusinessTypeTemplate = z.infer<typeof BusinessTypeTemplateSchema>; 