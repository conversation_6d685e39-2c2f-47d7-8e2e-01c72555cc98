# Type Definitions

This folder contains all the core type definitions for the invoice application using Zod for runtime validation and type safety.

## Structure

- `common.ts` - Common types used across entities (IDs, enums, base schemas)
- `invoice.ts` - Invoice-related types (Invoice, LineItem, etc.)
- `client.ts` - Client entity types
- `service.ts` - Service entity types  
- `organization.ts` - Organization entity types
- `tax.ts` - Tax configuration and calculation types

## Usage

### Importing Types

```typescript
// Import from the main index
import { Invoice, Client, Service } from '@/defs';

// Or import from specific files
import { Invoice, InvoiceLineItem } from '@/defs/invoice';
import { TaxConfiguration } from '@/defs/tax';
```

### Validation with Zod

```typescript
import { InvoiceSchema } from '@/defs/invoice';

// Validate data at runtime
const result = InvoiceSchema.safeParse(invoiceData);
if (result.success) {
  const validInvoice = result.data; // Fully typed
} else {
  console.error('Validation errors:', result.error);
}
```

### Form Input Types

Each entity has corresponding input types for forms:

```typescript
import { CreateInvoiceInput, UpdateInvoiceInput } from '@/defs/invoice';
import { CreateClientInput } from '@/defs/client';
```

## Key Benefits

1. **Type Safety** - Compile-time and runtime validation
2. **Consistency** - Single source of truth for all types
3. **Scalability** - Easy to extend and modify
4. **Documentation** - Self-documenting with Zod constraints
5. **Validation** - Built-in runtime validation

## Migration Guide

When updating existing code:

1. Replace inline interfaces with imports from `@/defs`
2. Use the proper entity types instead of `any` or ad-hoc interfaces
3. Add validation where data enters the system (APIs, forms, storage)
4. Use the input types for form handling

## Example

### Before (Bad)
```typescript
interface Invoice {
  id: string;
  amount: string;
  // ... other fields defined inline
}
```

### After (Good)
```typescript
import { Invoice } from '@/defs';
// Type is now centralized, validated, and consistent
``` 