{"name": "docs", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --port 3001", "build": "next build", "start": "next start", "lint": "eslint . --max-warnings 0"}, "dependencies": {"@repo/ui": "workspace:*", "next": "14.2.8", "react": "catalog:react18", "react-dom": "catalog:react18"}, "devDependencies": {"@next/eslint-plugin-next": "14.2.8", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/eslint": "catalog:", "@types/node": "catalog:", "@types/react": "catalog:react18", "@types/react-dom": "catalog:react18", "autoprefixer": "^10", "eslint": "catalog:", "postcss": "^8", "tailwindcss": "catalog:", "typescript": "catalog:"}}