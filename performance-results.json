{"packages": {"@repo/schemas": {"buildTime": 272, "typeCheckTime": 281, "srcSize": 15351, "distSize": 0, "success": true}, "@repo/stores": {"buildTime": 274, "typeCheckTime": 285, "srcSize": 31411, "distSize": 0, "success": true}, "@repo/queries": {"buildTime": 283, "typeCheckTime": 288, "srcSize": 18110, "distSize": 0, "success": true}, "@repo/utils": {"buildTime": 277, "typeCheckTime": 273, "srcSize": 12896, "distSize": 0, "success": true}, "@repo/constants": {"buildTime": 281, "typeCheckTime": 277, "srcSize": 11223, "distSize": 0, "success": true}, "@repo/dtos": {"buildTime": 284, "typeCheckTime": 279, "srcSize": 3357, "distSize": 0, "success": true}, "@repo/providers": {"buildTime": 283, "typeCheckTime": 285, "srcSize": 0, "distSize": 0, "success": true}, "@repo/ui-interfaces": {"buildTime": 295, "typeCheckTime": 278, "srcSize": 13800, "distSize": 0, "success": true}}, "apps": {"mobile": {"typeCheckTime": 283, "srcSize": 0}}, "summary": {"totalBuildTime": 2249, "successfulBuilds": 8, "failedBuilds": 0, "totalBundleSize": 0}}